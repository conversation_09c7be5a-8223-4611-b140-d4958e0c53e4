import {View, Image, Text} from '@tarojs/components';
import {FC, useCallback} from 'react';
import cx from 'classnames';
import {WiseAuthoritySolid} from '@baidu/wz-taro-tools-icons';
import {navigate} from '../../../../pages-im/src/utils/basicAbility/commonNavigate';
import {ubcCommonClkSend} from '../../../../pages-im/src/utils/generalFunction/ubc';
import {ApplicationProps} from './index.d';
import styles from './index.module.less';

const ApplicationStructure: FC<ApplicationProps> = props => {
    const {data, lineClamp = '2', needCircle = false} = props;

    const handleClick = useCallback(() => {
        const isImTabPage = data?.url && data?.url?.startsWith('/vita/pages/im');
        navigate({
            url: isImTabPage ? `${data.url}&tab=ai` : data.url,
            openType: isImTabPage ? 'relaunch' : 'navigate'
        });

        ubcCommonClkSend({
            value: data.type,
            page: 'vita/pages/service/index'
        });
    }, [data.url, data.type]);

    return (
        <View className={cx(styles.applicationStructure, 'c-click-status')} onClick={handleClick}>
            <View className={styles.applicationImg}>
                <View className={styles.inner}>
                    <Image
                        src={data.icon}
                        className={cx(styles.avatar, {[styles.circle]: needCircle})}
                    />
                    {data.cornerUrl ? (
                        <Image src={data.cornerUrl} className={styles.cornerIcon} />
                    ) : null}
                </View>
            </View>
            <View className={styles.applicationDetail}>
                <View className={styles.personal}>
                    <Text className={styles.name}>{data.name}</Text>
                    {data.professional && (
                        <Text className={styles.professional}>{data.professional}</Text>
                    )}
                    {data.department && (
                        <Text className={styles.department}>{data.department}</Text>
                    )}
                </View>
                {data.hospital || data.rank ? (
                    <View className={styles.external}>
                        {data.hospital && <Text className={styles.hospital}>{data.hospital}</Text>}
                        {data.rank && (
                            <View className={styles.rankLabel}>
                                <WiseAuthoritySolid color='#39B362' />
                                <Text className={styles.rank}>{data.rank}</Text>
                            </View>
                        )}
                    </View>
                ) : null}
                <View className={styles.description} style={{WebkitLineClamp: lineClamp}}>
                    {data.description}
                </View>
            </View>
        </View>
    );
};

export default ApplicationStructure;
