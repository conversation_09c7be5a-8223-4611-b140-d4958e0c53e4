export interface ApplicationItem {
    icon: string;
    title: string;
    needLogin?: boolean;
    interactionInfo: {
        url: string;
        params?: Record<string, unknown>;
    };
    type?: string;
}

export interface entryList {
    title?: string;
    list: ApplicationItem[];
}

export interface HeadData {
    avatarUrl: string;
    uname: string;
    isLogin: boolean;
}

export interface ProfileData {
    headData?: HeadData;
    entryList?: entryList[];
}
