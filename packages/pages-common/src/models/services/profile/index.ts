import {API_HOST} from '../../../models/apis/host';
import httpRequest from '../../../../../pages-im/src/utils/basicAbility/comonRequest/common';
import {ProfileData} from './index.d';

/**
 *
 * @description 获取tab我的数据
 * @returns {Promise<GetRecomListResponse>}
 */
export const getProfileData = () => {
    return httpRequest<ProfileData>({
        url: `${API_HOST}/vtui/usercenter/index`,
        method: 'GET',
        data: {},
        isNeedLogin: false,
        isFirstScreen: true
    });
};
