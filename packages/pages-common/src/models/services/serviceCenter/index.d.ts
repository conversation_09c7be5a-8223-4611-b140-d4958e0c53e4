export interface ApplicationItem {
    icon: string;
    cornerUrl?: string;
    name: string;
    professional?: string;
    department?: string;
    hospital?: string;
    rank?: string;
    description?: string;
    url?: string;
    departmentType?: string;
    title?: string;
    type: string;
}

export interface TabItem {
    name: string;
    filterType: string;
    filterValue: string;
}

export interface ServiceCenterTab {
    title: string;
    hasSecondary?: boolean;
    apiUrl?: string;
    secondaryTabs?: TabItem[];
    children?: ApplicationItem[];
}

export interface SecondTabParams {
    source?: string;
    filterType?: string;
    filterValue?: string;
    pn?: number;
    rn?: number;
}

export interface SecondTabResult {
    doctorAgentList?: ApplicationItem[];
    secondaryTabs?: TabItem[];
    pn?: number;
    rn?: number;
    hasMore?: boolean;
}
