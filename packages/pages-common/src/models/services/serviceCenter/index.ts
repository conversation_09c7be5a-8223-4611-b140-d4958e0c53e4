import {API_HOST} from '../../../models/apis/host';
import httpRequest from '../../../../../pages-im/src/utils/basicAbility/comonRequest/common';
import {ServiceCenterTab, SecondTabParams, SecondTabResult} from './index.d';

interface CenterData {
    list: ServiceCenterTab[];
}

/**
 *
 * @description 获取服务应用中心tab数据
 * @returns {Promise<GetRecomListResponse>}
 */
export const serviceCenterData = () => {
    return httpRequest<CenterData>({
        url: `${API_HOST}/vtui/servicecenter/index`,
        method: 'POST',
        data: {},
        isNeedLogin: false,
        isFirstScreen: true
    });
};

/**
 *
 * @description 二级tab相关数据
 * @returns {Promise<GetRecomListResponse>}
 */
export const secondaryTabData = (url: string, params: SecondTabParams) => {
    return httpRequest<SecondTabResult>({
        url: url,
        method: 'POST',
        data: params,
        isNeedLogin: false,
        isFirstScreen: true
    });
};
