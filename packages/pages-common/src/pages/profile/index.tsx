import {useState, useEffect, useCallback} from 'react';
import cx from 'classnames';
import {View, Image, Text} from '@tarojs/components';
import {navigate} from '../../../../pages-im/src/utils/basicAbility/commonNavigate';
import {useGetSwanMsgListSceneStatus} from '../../../../pages-im/src/hooks/triageStream/useGetSwanMsgListSceneStatus';
import CPageContainer from '../../../../pages-im/src/components/CPageContainer';
import CLoginButton from '../../../../ui-cards-common/CloginBtn';
import {getProfileData} from '../../models/services/profile';
import {
    ubcCommonViewSend,
    ubcCommonClkSend
} from '../../../../pages-im/src/utils/generalFunction/ubc';
import {ProfileData} from '../../models/services/profile/index.d';
import styles from './index.module.less';

const barBg =
    'left top / 100% auto #EFF3F9 no-repeat url("https://med-fe.cdn.bcebos.com/wenzhen-mini-app/aiContainerBg.png")';
const loginIcon = 'https://med-fe.cdn.bcebos.com/vita/loginDirect.png';
const linkIcon = 'https://med-fe.cdn.bcebos.com/vita/linkDirect.png';
const pagePath = 'vita/pages/profile/index';

export default function Index() {
    const [profileData, setProfileData] = useState<ProfileData>({});
    const [showSkeleton, setShowSkeleton] = useState(true);
    const {status} = useGetSwanMsgListSceneStatus();

    const getPageData = useCallback(async () => {
        const [err, res] = await getProfileData();
        if (!err && res?.data) {
            setProfileData(res.data);
            setShowSkeleton(false);
        }
    }, []);

    useEffect(() => {
        getPageData();
        ubcCommonViewSend({
            value: pagePath,
            page: pagePath
        });
    }, [getPageData]);

    // 跳转
    const handleDirect = useCallback(directItem => {
        navigate({
            url: directItem.interactionInfo?.url,
            params: directItem.interactionInfo?.params || {},
            openType: 'navigate'
        });
        ubcCommonClkSend({
            value: directItem.type || '',
            page: pagePath
        });
    }, []);

    return (
        <CPageContainer
            showSkeleton={showSkeleton}
            skeletonName='vitaProfile'
            className={styles.profileContainer}
            topBarProps={{
                title: ' ',
                className: 'white',
                textColor: '#000311',
                barBg: barBg,
                blank: true,
                hideHome: true,
                titleLeftSlot: 'AI健康管家',
                ...(status
                    ? {
                        hideBack: true,
                        isInTabBar: true
                    }
                    : {})
            }}
        >
            <>
                <View className={styles.userWrap}>
                    <View className={styles.userContainer}>
                        <View className={styles.userMsg}>
                            {profileData.headData?.avatarUrl ? (
                                <View className={styles.userAvatar}>
                                    <Image
                                        src={profileData.headData?.avatarUrl}
                                        className={styles.avatar}
                                        mode='widthFix'
                                    />
                                </View>
                            ) : null}
                            <View className={styles.userText}>
                                {profileData.headData?.isLogin ? (
                                    <Text>{profileData.headData.uname}</Text>
                                ) : (
                                    <>
                                        <CLoginButton
                                            isLogin={profileData.headData?.isLogin}
                                            useH5CodeLogin={true}
                                            closeShowNewUserTag={true}
                                            onLoginFail={error => {
                                                console.error('error', error);
                                            }}
                                            onLoginSuccess={async () => {
                                                // 登录成功 更新用户信息
                                                getPageData();
                                            }}
                                        >
                                            <View className={cx('wz-flex')}>
                                                <Text>立即登录</Text>
                                                <Image
                                                    src={loginIcon}
                                                    className={styles.loginIcon}
                                                    mode='widthFix'
                                                />
                                            </View>
                                        </CLoginButton>
                                    </>
                                )}
                            </View>
                        </View>
                    </View>
                </View>
                {profileData.entryList?.length ? (
                    <View className={styles.directWrap}>
                        {profileData.entryList.map((item, index) => {
                            return (
                                <View className={styles.directBlock} key={index}>
                                    {item.list?.map((directItem, idx) => {
                                        return (
                                            <>
                                                {directItem.needLogin &&
                                                !profileData.headData?.isLogin ? (
                                                        <>
                                                            <CLoginButton
                                                                isLogin={false}
                                                                useH5CodeLogin={true}
                                                                closeShowNewUserTag={true}
                                                                key={idx}
                                                                onLoginFail={error => {
                                                                    console.error('error', error);
                                                                }}
                                                                onLoginSuccess={async () => {
                                                                // 登录成功 更新用户信息
                                                                    getPageData();
                                                                }}
                                                            >
                                                                <View className={styles.directItem}>
                                                                    <View className={styles.inner}>
                                                                        <View
                                                                            className={styles.innerLeft}
                                                                        >
                                                                            <Image
                                                                                src={directItem.icon}
                                                                                className={
                                                                                    styles.directIcon
                                                                                }
                                                                                mode='widthFix'
                                                                            />
                                                                            <Text
                                                                                className={styles.title}
                                                                            >
                                                                                {directItem.title}
                                                                            </Text>
                                                                        </View>
                                                                        <Image
                                                                            src={linkIcon}
                                                                            className={styles.linkIcon}
                                                                            mode='widthFix'
                                                                        />
                                                                    </View>
                                                                </View>
                                                            </CLoginButton>
                                                        </>
                                                    ) : (
                                                        <View
                                                            key={idx}
                                                            className={styles.directItem}
                                                            onClick={() => handleDirect(directItem)}
                                                        >
                                                            <View className={styles.inner}>
                                                                <View className={styles.innerLeft}>
                                                                    <Image
                                                                        src={directItem.icon}
                                                                        className={styles.directIcon}
                                                                        mode='widthFix'
                                                                    />
                                                                    <Text className={styles.title}>
                                                                        {directItem.title}
                                                                    </Text>
                                                                </View>
                                                                <Image
                                                                    src={linkIcon}
                                                                    className={styles.linkIcon}
                                                                    mode='widthFix'
                                                                />
                                                            </View>
                                                        </View>
                                                    )}
                                            </>
                                        );
                                    })}
                                </View>
                            );
                        })}
                    </View>
                ) : null}
            </>
        </CPageContainer>
    );
}
