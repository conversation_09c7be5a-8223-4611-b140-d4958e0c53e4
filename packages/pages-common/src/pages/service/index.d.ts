export interface ApplicationItem {
    icon: string;
    cornerUrl?: string;
    name: string;
    professional?: string;
    department?: string;
    hospital?: string;
    rank?: string;
    description?: string;
    url?: string;
    departmentType?: string;
    title?: string;
    type: string;
}

export interface TabItem {
    name: string;
    filterType: string;
    filterValue: string;
}

export interface ServiceCenterTab {
    title: string;
    hasSecondary?: boolean;
    apiUrl?: string;
    secondaryTabs?: TabItem[];
    children?: ApplicationItem[];
}
