import {useState, useRef, useEffect, useCallback, useMemo} from 'react';
import cx from 'classnames';
import {View, ScrollView} from '@tarojs/components';
import {Loading, Skeleton, Empty} from '@baidu/wz-taro-tools-core';
import HTabs from '@baidu/health-ui/lib/components/HTabs';
import CPageContainer from '../../../../pages-im/src/components/CPageContainer';
import ApplicationStructure from '../../components/ApplicationStructure';
import {useGetSwanMsgListSceneStatus} from '../../../../pages-im/src/hooks/triageStream/useGetSwanMsgListSceneStatus';
import {serviceCenterData, secondaryTabData} from '../../models/services/serviceCenter';
import {ubcCommonViewSend} from '../../../../pages-im/src/utils/generalFunction/ubc';
import {ServiceCenterTab} from './index.d';
import styles from './index.module.less';

const barBg =
    'left top / 100% auto #EFF3F9 no-repeat url("https://med-fe.cdn.bcebos.com/wenzhen-mini-app/aiContainerBg.png")';
const pagePath = 'vita/pages/service/index';
const emptyImg = 'https://med-fe.cdn.bcebos.com/common/pageStatus/status_empty.png';

const SkeletonItem = () => (
    <View className={cx('wz-flex', styles.skeletonBlock)}>
        <Skeleton className={styles.skeletonLeft} animation='wave' />
        <View className={styles.skeletonRight}>
            <Skeleton className={cx(styles.searchLine, styles.searchLine1)} animation='wave' />
            <Skeleton className={styles.searchLine} animation='wave' />
            <Skeleton className={cx(styles.searchLine, styles.searchLine2)} animation='wave' />
        </View>
    </View>
);

// 有二级tab时增加骨架屏
const SkeletonList = ({list}: {list: number}) => (
    <View className={styles.skeleton}>
        {Array(list)
            .fill(null)
            .map((_, index) => (
                <SkeletonItem key={index} />
            ))}
    </View>
);

export default function Index() {
    const [data, setData] = useState<ServiceCenterTab[]>([]);
    const [value, setValue] = useState(0);
    const dataSave = useRef<ServiceCenterTab[]>([]);
    const {status} = useGetSwanMsgListSceneStatus();
    const [loading, setLoading] = useState(true);
    const [initSkeleton, setInitSkeleton] = useState(true);
    const [showSkeleton, setShowSkeleton] = useState(true);
    const secondaryIndex = useRef(0);
    const tabIndex = useRef(0);
    const secondaryTabPn = useRef(0);
    const tabHasMore = useRef(true);

    const handleOnLoad = useCallback(async () => {
        if (!tabHasMore.current) {
            return;
        }
        setLoading(true);
        const newData = [...dataSave.current];
        const tabList = newData[tabIndex.current];
        const secondTabs = data?.[tabIndex.current]?.secondaryTabs || [];
        const filterType =
            (secondTabs.length && secondTabs?.[secondaryIndex.current]?.filterType) || '';
        const filterValue =
            (secondTabs.length && secondTabs?.[secondaryIndex.current]?.filterValue) || '';
        const [err, res] = await secondaryTabData(dataSave.current[tabIndex.current].apiUrl || '', {
            source: '',
            filterType,
            filterValue,
            pn: secondaryTabPn.current,
            rn: 10
        });
        if (!err && res?.data) {
            const {secondaryTabs, doctorAgentList = [], hasMore = false, pn} = res?.data || {};
            if (pn) {
                secondaryTabPn.current = pn;
            } else {
                secondaryTabPn.current++;
            }
            newData[tabIndex.current] = {
                ...tabList,
                secondaryTabs,
                children: (data?.[tabIndex.current]?.children || [])?.concat(
                    ...(doctorAgentList || [])
                )
            };
            setData(newData);
            tabHasMore.current = hasMore;
            setLoading(false);
            setShowSkeleton(false);
        }
    }, [data]);

    const getPageData = useCallback(async () => {
        const [err, res] = await serviceCenterData();
        if (!err && res?.data) {
            const {list} = res.data || {};
            dataSave.current = list;
            setData(list);
            setInitSkeleton(false);

            // 兼容首次加载时需要二次请求 如医生分身tab
            // 医生分身列表接口需要多次请求其他服务，接口耗时会比较大，且需要支持分页，rd评估不适合放到服务中心接口
            if (list[value]?.apiUrl && !list[value]?.children) {
                setShowSkeleton(true);
                handleOnLoad();
            }
        }
    }, []);

    useEffect(() => {
        getPageData();
        ubcCommonViewSend({
            value: pagePath,
            page: pagePath
        });
    }, [getPageData]);

    // 一级tab切换
    const onTabClick = useCallback(
        (e, index) => {
            if (tabIndex.current === index) {
                return;
            }
            setValue(index);
            tabIndex.current = index;
            secondaryIndex.current = 0;
            secondaryTabPn.current = 0;
            tabHasMore.current = true;
            if (data[index].apiUrl) {
                setShowSkeleton(true);
                data[index].children = [];
                data[index].secondaryTabs = [];
                setData(data);
                handleOnLoad();
            }
        },
        [data, handleOnLoad]
    );

    // 二级tab切换
    const handleClonedTab = useCallback(
        (e, index) => {
            if (secondaryIndex.current === index) {
                return;
            }
            setShowSkeleton(true);
            secondaryIndex.current = index;
            secondaryTabPn.current = 0;
            data[tabIndex.current].children = [];
            setData(data);
            tabHasMore.current = true;
            handleOnLoad();
        },
        [handleOnLoad, data]
    );

    const renderEmpty = useMemo(() => {
        return (
            <Empty className={cx(styles.empty, 'wz-flex')}>
                <Empty.Image className={styles.emptyImg} src={emptyImg} />
                <Empty.Description>暂无内容</Empty.Description>
            </Empty>
        );
    }, []);

    return (
        <CPageContainer
            className={styles.serviceContainer}
            topBarProps={{
                title: ' ',
                className: 'white',
                textColor: '#000311',
                barBg: barBg,
                blank: true,
                hideHome: true,
                titleLeftSlot: 'AI健康管家',
                ...(status
                    ? {
                        hideBack: true,
                        isInTabBar: true
                    }
                    : {})
            }}
        >
            {initSkeleton ? (
                <View className={styles.skeletonContainer}>
                    <View className={cx(styles.skeletonTab, 'wz-flex')}>
                        <View className={styles.skeletonTabItem}></View>
                        <View className={styles.skeletonTabItem}></View>
                        <View className={styles.skeletonTabItem}></View>
                    </View>
                    <SkeletonList list={4} />
                </View>
            ) : (
                <View className={styles.innerContent}>
                    <HTabs
                        selectedIndex={value}
                        isEqualWidth={false}
                        activeLineMode='fixed'
                        activatedType='line'
                        onTabClick={onTabClick}
                        selectedItemStyle={{
                            color: '#272933',
                            fontWeight: '500'
                        }}
                        itemStyle={{
                            color: '#848691',
                            fontSize: '17px',
                            lineHeight: '17px',
                            padding: '10px 13px',
                            fontFamily: 'PingFangSC',
                            fontWeight: 'normal'
                        }}
                        underLineStyle={{
                            width: '20px',
                            height: '4px'
                        }}
                        className={styles.tabsTitle}
                    >
                        {data.map((item, index) => {
                            return (
                                <HTabs.TabPane
                                    key={index}
                                    title={item.title}
                                    className={styles.service}
                                ></HTabs.TabPane>
                            );
                        })}
                    </HTabs>
                    {data?.[tabIndex.current]?.secondaryTabs ? (
                        <>
                            <HTabs
                                selectedIndex={secondaryIndex.current}
                                className={styles.secondaryTab}
                                itemStyle={{
                                    height: '32px',
                                    marginRight: '8px',
                                    lineHeight: '32px',
                                    fontSize: '14px',
                                    padding: '9px 16px',
                                    color: '#1f1f1f',
                                    borderRadius: '22px',
                                    background: 'rgba(255, 255, 255, 0.7)'
                                }}
                                selectedItemStyle={{
                                    background: 'rgba(0, 200, 200, 0.3)'
                                }}
                                isEqualWidth={false}
                                onTabClick={handleClonedTab}
                            >
                                {data[tabIndex.current].secondaryTabs?.map((item, i) => (
                                    <HTabs.TabPane key={i} title={item.name} />
                                ))}
                            </HTabs>
                            <ScrollView
                                scrollY
                                onScrollToLower={handleOnLoad}
                                className={styles.scrollContent}
                            >
                                {showSkeleton ? (
                                    <SkeletonList list={3} />
                                ) : (
                                    <>
                                        {!data?.[tabIndex.current]?.children?.length ? (
                                            renderEmpty
                                        ) : (
                                            <>
                                                <View className={styles.itemContainer}>
                                                    {data[tabIndex.current].children?.map(
                                                        (child, idx) => {
                                                            return (
                                                                <View
                                                                    key={`content_${idx}`}
                                                                    className={styles.serviceItem}
                                                                >
                                                                    {
                                                                        <ApplicationStructure
                                                                            key={`content_${idx}`}
                                                                            data={child}
                                                                            lineClamp='1'
                                                                            needCircle
                                                                        />
                                                                    }
                                                                </View>
                                                            );
                                                        }
                                                    )}
                                                </View>
                                                {loading && (
                                                    <View className={styles.loadingStatus}>
                                                        <Loading>加载中...</Loading>
                                                    </View>
                                                )}
                                                {!tabHasMore.current && (
                                                    <View className={styles.noMore}>
                                                        没有更多了
                                                    </View>
                                                )}
                                            </>
                                        )}
                                    </>
                                )}
                            </ScrollView>
                        </>
                    ) : (
                        <>
                            {!data?.[tabIndex.current]?.children?.length ? (
                                renderEmpty
                            ) : (
                                <ScrollView scrollY className={styles.scrollContent}>
                                    <View className={styles.itemContainer}>
                                        {data?.[tabIndex.current]?.children?.map((child, idx) => {
                                            return (
                                                <View
                                                    key={`content_${idx}`}
                                                    className={styles.serviceItem}
                                                >
                                                    {
                                                        <ApplicationStructure
                                                            key={`content_${idx}`}
                                                            data={child}
                                                        />
                                                    }
                                                </View>
                                            );
                                        })}
                                    </View>
                                </ScrollView>
                            )}
                        </>
                    )}
                </View>
            )}
        </CPageContainer>
    );
}
