{"name": "@baidu/vita-pages-im-old", "version": "1.0.0", "private": true, "description": "AI 健康助手", "templateInfo": {"name": "default", "typescript": true, "css": "None", "framework": "React"}, "main": "index.tsx", "scripts": {"test": "jest"}, "browserslist": ["last 3 versions", "Android >= 4.1", "ios >= 8"], "author": "", "dependencies": {"@baidu/boxx": "^4.0.4", "@baidu/antifraud-swan": "^1.3.0", "@baidu/antifraud-xaf3": "^3.3.53", "@baidu/health-ubc": "1.0.14", "@baidu/health-utils": "1.1.3", "@baidu/vita-ui-cards-common": "workspace:*", "@baidu/vita-ui-cards-wz": "workspace:*", "@baidu/vita-utils-shared": "workspace:*", "@baidu/xaf-we": "^1.1.1", "@searchfe/user-agent": "^1.9.14", "classnames": "^2.5.1", "jotai": "^2.12.2", "js-base64": "^3.7.7", "lodash-es": "^4.17.21", "sse-kit": "1.0.7"}, "devDependencies": {"jotai-devtools": "0.12.0", "react-scan": "^0.1.3"}}