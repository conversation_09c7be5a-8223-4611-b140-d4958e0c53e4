{
    "extends": "../../tsconfig.json",
    "compilerOptions": {
        "baseUrl": ".",
        "types": []
    },
    "include": [
        "src/**/*",
        "src/typings/**/*"
    ],
    "exclude": ["node_modules"],
    "paths": {
        "@src/*": ["./src/*"],
        "@common/*": ["./src/common/*"],
        "@components/*": ["./src/components/*"],
        "@typings/*": ["./src/typings/*"],
        // todo:替换
        "@wzTaroUtils": ["src/common/utils/taro/index"],
        "@path/*": ["./path/*"],
        "@hooks/*": ["./src/hooks/*"],
        "@models/*": ["./src/models/*"],
        "@api/*": ["./src/models/api/*"],
        "@globalDataStore/*": ["./src/globalDataStore/*"],
        "@skeleton/*": ["./src/skeleton/*"],
    }
}
