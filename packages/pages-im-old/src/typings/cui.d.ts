interface ActionInfo {
    value: string;
    type?: string;
    interaction: string;
    interactionInfo: InteractionInfo;
}

export interface richItem {
    value?: string;
    type?: string;
}

export interface InteractionInfo {
    intent?: string;
    url?: string;
    sceneType?: string;
    method?: 'GET' | 'POST';
    contentType?: string;
    wxPayConfig?: unknown;
    wxPayInfo?: {[key: string]: string};
    inlinePaySign?: string;
    type?: string;
    version?: string;
    params?: {
        [key in string]: string | number | unknown;
    };
    fail?: () => void;
    success?: () => void;
}
