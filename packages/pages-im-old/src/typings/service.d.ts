import {ActionInfo} from './msg';
export interface IDecription {
    list: string[];
    title: string;
}

export interface ICouponTip {
    type?: string;
    value?: string;
}

export interface ICouponList {
    id?: number;
    cid?: number;
    name?: string;
    startTime?: number;
    endTime?: number;
    subTitle?: string;
    worth?: string;
    description?: IDecription[];
    category?: number;
    maxPrice?: number;
    fullPrice?: number;
    salePrice?: number;
    current?: number;
    usable?: number;
}

export interface CouponInfo {
    cid: number;
    url: string;
    name: string;
    worth: string;
    end_time: number;
    subtitle: string;
    category: number;
    max_price: number;
    sale_price: number;
    full_price: number;
    start_time: number;
    description: string;
    currentSend?: boolean;
}

export interface couponInfoItemProps {
    title?: string;
    subtitle?: string;
    startTime?: number;
    endTime?: number;
    salePrice?: number;
    fullPrice?: number;
    worth?: string;
}

export interface PatinentItemInfo {
    key?: string;
    label?: string;
}

// 支付
export interface PayResponse {
    payUrl?: string;
    wxPayInfo?: {[key: string]: string};
    jumpMsUrl?: string;
    toast?: string;
    freeJumpUrl?: string;
    jumpUrl?: string;
    pay_url?: string;
    isExpertUnavailable?: boolean;
    isUserUnavailable?: boolean;
    successJumpUrl?: string;
    phone?: string;
    sceneType?: string; // makeOrderInplace原地调起支付
    cancelJumpUrl?: string; // 取消后跳转页面
    actionInfo?: ActionInfo;
}
