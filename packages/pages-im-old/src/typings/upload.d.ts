import {Uploader} from '@baidu/wz-taro-tools-core';

export interface IPicProps {
    // 本地blob路径
    path?: string;
    // H5 base64 小程序是 小程序的本地路径
    filePath?: string;
    // 图片宽度
    width?: number;
    // 图片高度
    height?: number;
    // 图片大小
    size?: number;
    // 浏览器 file 对象
    originalFileObj?: File;
    // 传给后端的bosId
    fileName?: string;
    // 文件类型
    type?: string;
    // 时长
    duration?: number;
    thumb?: string;
    snapshotURLs?: string[];
    bdFileMap?: Record<string, string>;
}

export interface IPicConfProps {
    count?: number;
    quality?: number;
    maxDuration?: number;
    compressed?: boolean;
    bucketConfName?: string;
    // 最大图片大小，单位为 MB
    imageMaxSize?: number;
    // 图片大小超出后的提示文案
    imageOversizeToast?: string;
    H5Tips?: string; //H5 端上传图片的提示文案
    remainNum?: number; // 剩余可上传图片数量，h5提示文案使用
    btnType?: 'camera' | 'album' | '';
}

// 图片上传文件
export interface FileProps extends Uploader.File {
    fileName: string;
    picID?: string;
    icon?: string;
    origin: string;
    small: string;
    path: string;
}
