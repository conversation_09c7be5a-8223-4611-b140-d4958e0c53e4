// 问诊诊中提示用户使用语音缓存数据，存储 key
export const WZ_CHAT_VOICEID = 'wz_chat_voiceId';

// 微信端问诊诊中用来记录调用语音token的过去时间，存储 key
export const WZ_CHAT_VOICEID_EXPIRE = 'wz_chat_voiceExpireTime';

// 手百问诊禁止诊中页面渲染，存储 key
export const WZ_NOT_PAGE_RENDER = 'wzNotPageRender';
export const WZ_CHAT_VOICE_INIT = 'wz_chat_voiceInit';

export const VITA_PATIENT_DESC_IS_SHOW_MORE = 'vita_patient_desc_is_show_more';

export const VITA_RECOMMEND_SKU_UNFOLD = 'vita_recommend_sku_unfold';

// 定向卡展开更多缓存
export const VITA_RECOMMEND_EXPERT_UNFOLD = 'vita_recommend_expert_unfold';
