export const imgUrlMap = {
    drugProcess:
        'https://hcg-rome.cdn.bcebos.com/uha/senate/20220727/1658907772221_drugProcess.png',
    agreeIcon:
        'https://ss1.baidu.com/6ONXsjip0QIZ8tyhnq/it/u=3380361448,245250595&fm=179&app=35&f=PNG?w=45&h=45',
    disAgreeIcon:
        'https://ss0.baidu.com/6ONWsjip0QIZ8tyhnq/it/u=1601670114,4265098755&fm=179&app=35&f=PNG?w=45&h=45&s=ADE07A231347A1154E719D070000E0C1',
    gyqzBg: 'https://bj.bcebos.com/v1/hcg-rome/uha/senate/20220805/1659684340148_gyqzBg.png',
    // 用药建议
    drugText: 'https://med-fe.cdn.bcebos.com/wenzhen-mini-app/drugText.png',
    // 电子处方
    prescriptionText: 'https://med-fe.cdn.bcebos.com/wenzhen-mini-app/prescriptionText.png',
    // 中药颗粒剂说明
    granuleInfo: 'https://med-fe.cdn.bcebos.com/drugs/carpenter_instructions.png',
    // 处方，中药优惠券弹层动画
    chineseDrugCouponBg: 'https://med-fe.cdn.bcebos.com/zhgy/single.gif',
    // 中药膏方说明
    creamGranuleInfo: 'https://med-fe.cdn.bcebos.com/wz-mini/recipel/recipel_cream.png',
    // 中药饮片说明
    tabletGranuleInfo: 'https://med-fe.cdn.bcebos.com/wz-mini/recipel/recipel_tablet.png',
    // 复购弹层绿色，有服务列表
    gjLv: 'https://med-fe.cdn.bcebos.com/wz/repeatcustomers/gj_lv.png',
    // 复购弹层黄色，无服务列表
    bjJu: 'https://med-fe.cdn.bcebos.com/wz/repeatcustomers/bj_ju.png',
    // 家医-购买家医卡-页面底部半透明“百度健康”logo
    purchaseBDJKLogo: 'https://med-fe.cdn.bcebos.com/brand/doc/doc-icon-bottom-logo.png',
    // 家医-购买家医卡-按钮背景
    purchaseBtnImg: 'https://med-fe.cdn.bcebos.com/private-doctor/purchase-btn.png',
    // 家医-购买家医卡-微信-按钮背景
    purchaseBtnImgWX: 'https://med-fe.cdn.bcebos.com/private-doctor/purchase-btn-wx.png',
    // 家医-购买家医卡-活动规则
    purchaseRuleImg: 'https://med-fe.cdn.bcebos.com/private-doctor/purchase-rule.png',
    // 家医-购买家医卡-活动规则
    purchaseRuleImgWX: 'https://med-fe.cdn.bcebos.com/private-doctor/purchase-rule-wx.png',
    // 家医-购买家医卡-客服按钮
    purchaseChatBtn: 'https://med-fe.cdn.bcebos.com/private-doctor/bd-cs-guide.png',
    // 家医-购买家医卡-微信-客服按钮
    purchaseChatBtnWX: 'https://med-fe.cdn.bcebos.com/private-doctor/bd-cs-guide-wx.png',
    // 家医-购买家医卡-买赠记录按钮
    purchaseGuideBtn: 'https://med-fe.cdn.bcebos.com/private-doctor/bd-history-guide.png',
    // 家医-购买家医卡-微信-买赠记录按钮
    purchaseGuideBtnWX: 'https://med-fe.cdn.bcebos.com/private-doctor/bd-history-guide-wx.png',
    // 家医-购买家医卡-赠送步骤
    purchaseGiveOther: 'https://med-fe.cdn.bcebos.com/private-doctor/bd-giveother.png',
    // 家医-购买家医卡-微信-赠送步骤
    purchaseGiveOtherWX: 'https://med-fe.cdn.bcebos.com/private-doctor/bd-giveother-wx.png',
    // 家医-购买家医卡-logo
    purchaseJiaYiLogo: 'https://med-fe.cdn.bcebos.com/private-doctor/bd-jiayi-logo.png',
    // 家医-购买家医卡-购买卡长背景2.0
    purchaseBuyBacLong: 'https://med-fe.cdn.bcebos.com/private-doctor/bd-private-buy-back-long.png',
    // 家医-购买家医卡-购买卡长背景2.0
    purchaseBuyBacShort:
        'https://med-fe.cdn.bcebos.com/private-doctor/bd-private-buy-back-short.png',
    // 家医-购买家医卡-购买卡长背景微信3.0
    purchaseBuyBackLong3WX:
        'https://med-fe.cdn.bcebos.com/private-doctor/bd-private-buy-back-long3-wx.png',
    // 家医-购买家医卡-购买卡短背景微信3.0
    purchaseBuyBackShort3WX:
        'https://med-fe.cdn.bcebos.com/private-doctor/bd-private-buy-back-short3-wx.png',
    // 家医-购买家医卡-年卡短背景微信3.0
    purchaseBuyBackYearShort3WX:
        'https://med-fe.cdn.bcebos.com/private-doctor/bd-private-buy-back-short3-wx-present.png',
    // 家医-购买家医卡-年卡长背景微信3.0
    purchaseBuyBackYearLong3WX:
        'https://med-fe.cdn.bcebos.com/private-doctor/bd-private-buy-back-long3-wx-present.png',
    // 家医-购买家医卡-服务介绍
    purchaseNotAllTimeDescription:
        'https://med-fe.cdn.bcebos.com/private-doctor/bd-purchase-bg-not-all-time.png',
    // 家医-购买家医卡-Banner
    purchaseBanner: 'https://med-fe.cdn.bcebos.com/private-doctor/bd-purchase-header.png',
    // 家医-购买家医卡-Banner
    purchaseBannerWX: 'https://med-fe.cdn.bcebos.com/private-doctor/bd-purchase-header-wx.png',
    // 家医-购买家医卡-QA
    purchaseQA: 'https://med-fe.cdn.bcebos.com/private-doctor/bd-purchase-qa.png',
    // 家医-购买家医卡-微信-QA
    purchaseQAWX: 'https://med-fe.cdn.bcebos.com/private-doctor/bd-purchase-qa-wx.png',
    // 家医-购买家医卡-如何找到介绍
    purchaseHowToFind: 'https://med-fe.cdn.bcebos.com/private-doctor/bd-search-history.png',
    // 家医-购买家医卡-使用流程介绍
    purchaseUseWay: 'https://med-fe.cdn.bcebos.com/private-doctor/bd-use-process.png',
    // 家医-购买家医卡-微信购买成功-icon
    purchasePaySuccessIcon:
        'https://med-fe.cdn.bcebos.com/private-doctor/jiayi-pay-success-icon.png',
    // 家医-购买家医卡-顶部自定义分享按钮
    purchaseHeaderShareBtn:
        'https://med-fe.cdn.bcebos.com/private-doctor/bd-purchase-header-share.png',
    // 家医-购买家医卡-顶部自定义分享按钮-黑版
    purchaseHeaderShareDarkBtn:
        'https://med-fe.cdn.bcebos.com/private-doctor/bd-purchase-header-share-dark.png',
    // 家医-首页-搜索模块图标
    jiayiIndexSearchQustionIcon:
        'https://med-fe.cdn.bcebos.com/private-doctor/bd-jiayi-search-question.png',
    // 家医-赠送他人分享图
    jiayiPresentShareImg: 'https://med-fe.cdn.bcebos.com/private-doctor/bd-jiayi-present-share.png',
    // 家医-赠送他人分享图
    jiayiPurchaseShareImg: 'https://med-fe.cdn.bcebos.com/private-doctor/family-doc-share.jpg',
    // 家医-微信-赠送他人分享图
    jiayiPurchaseShareImgWX: 'https://med-fe.cdn.bcebos.com/private-doctor/family-doc-share-wx.jpg',
    // 家医-赠送他人分享图
    purchasePresentStep:
        'https://med-fe.cdn.bcebos.com/private-doctor/bd-purchase-present-step.png',
    // 家医-赠送他人分享图
    purchasePresentStepWX:
        'https://med-fe.cdn.bcebos.com/private-doctor/bd-purchase-present-step-wx.png',
    // 家医-赠送他人-列表-医生头像
    purchasePresentDoctorAvatar:
        'https://med-fe.cdn.bcebos.com/private-doctor/bd-jiayi-send-doctor-avatar.png',
    // 家医-个人中心-默认头像
    jiayiCenterDefaultImg:
        'https://med-fe.cdn.bcebos.com/private-doctor/bd-private-center-headimg.jpeg',
    // 问诊-我的医生-底部无数据
    bottomLogo: 'https://med-fe.cdn.bcebos.com/wz-mini/bottomLogo.png',
    // 问诊-微信-首页-分享图
    wenzhenShareIndexImg: 'https://med-fe.cdn.bcebos.com/wz-mini/shareImg/shareIndex.png',
    // 问诊-微信-找专家二级页-分享图
    wenzhenShareFindeExportImg:
        'https://med-fe.cdn.bcebos.com/wz-mini/shareImg/shareFindeExport.png',
    // 问诊-微信-医院列表页-分享图
    wenzhenShareHospitalListImg:
        'https://med-fe.cdn.bcebos.com/wz-mini/shareImg/shareHospitalList.png',
    // 问诊-微信-医生列表页-分享图
    wenzhenShareDocotorListImg:
        'https://med-fe.cdn.bcebos.com/wz-mini/shareImg/shareDocotorList.png',
    // 问诊-微信-非定向主诉收集页-分享图
    wenzhenShareTriageImg: 'https://med-fe.cdn.bcebos.com/wz-mini/shareImg/shareTriage.png',
    // 问诊-医生列表-右侧小优惠券背景图
    couponSmallBg: 'https://med-fe.cdn.bcebos.com/wz-mini/coupon-small-bg.png',
    // 问诊-微信-默认医生头像
    wenzhenDefaultPicture: 'https://med-fe.cdn.bcebos.com/wz/assistant.png',
    // 页页无数据时图片
    noDataIcon: 'https://med-fe.cdn.bcebos.com/common/pageStatus/status_error.png',
    // 义诊-无数据或分发失败时图片
    yzNoDataIcon: 'https://med-fe.cdn.bcebos.com/wenzhen-mini-app/tanghao-2023-04-07.png',
    // AI皮肤检测-相机拍摄顶部背景
    aiCameraBg: 'https://med-fe.cdn.bcebos.com/wenzhen-mini-app/tackBg.png',
    // AI 皮肤检测-相机拍摄顶部 H5 背景
    aiSkinBgOfH5: 'https://med-fe.cdn.bcebos.com/wenzhen-mini-app/aiSkinBgOfH5.png',
    // AI 皮肤检测-相机拍摄顶部 返回按钮
    aiSkinBack: 'https://med-fe.cdn.bcebos.com/wenzhen-mini-app/aiskinBack.png',
    // AI皮肤检测-相机拍摄 lottie 循环动画
    aiCameraLottie: 'https://med-fe.cdn.bcebos.com/lottie/aiCameraLottie.json',
    aiCameraLottieNew: 'https://med-fe.cdn.bcebos.com/wz-mini/aiCameraLottieNew.json',
    // AI皮肤检测-图片预览 lottie 动画
    aiPreviewLottie: 'https://med-fe.cdn.bcebos.com/lottie/skinBlue.json',
    aiPreviewLottieOfWechat: 'https://med-fe.cdn.bcebos.com/lottie-test/skinBlueScan.json',
    // AI 皮肤检测兜底展示图片
    aiSkinUnderImg: 'https://med-fe.cdn.bcebos.com/skinUnderImg.png',
    // AI 皮肤检测-手百相机拍照tips
    aiSkinSwanCameraTip: 'https://med-fe.cdn.bcebos.com/wenzhen-mini-app/aiSkinSwanCameraTip.png',
    // AI皮肤 检测 相册ICON
    aiSkinPics: 'https://med-fe.cdn.bcebos.com/pics.png',
    // AI皮肤检测-相机反转ICON
    aiSkinReverseCamera: 'https://med-fe.cdn.bcebos.com/reverseCamera.png',
    // AI皮肤检测-相机拍照ICON
    aiSkinTackPic: 'https://med-fe.cdn.bcebos.com/tackPic.png',
    // AI皮肤检测-拍照页分享图
    aiSkinCameraShare: 'https://med-fe.cdn.bcebos.com/wenzhen-mini-app/aiSkinCameraShare.png',
    GuahaoBg: 'https://med-fe.cdn.bcebos.com/wenzhen-mini-app/gh-bg.png',
    WecomBg: 'https://med-fe.cdn.bcebos.com/wz-mini/qw-bg.png',
    // 挂号-放号订阅背景图
    subscribeBg: 'https://med-fe.cdn.bcebos.com/wenzhen-mini-app/subscribe_bg.png',
    // 投诉-投诉页-默认医生头像
    defaultAvator: 'https://med-fe.cdn.bcebos.com/med-ui/img/avatar.png',
    // 问诊-医生卡-医院排行榜
    doctorRand: 'https://med-fe.cdn.bcebos.com/wz/comicon/doctor_fd_rank.png',
    // 问诊-医生卡-新医院排行榜
    doctorRandNew: 'https://med-fe.cdn.bcebos.com/wz/fudan-new-tag.png',
    // 问诊-复旦榜单弹层背景图
    fudanBgImg:
        'https://material-from-upload.cdn.bcebos.com/images/yd/2023-08/108860e03856836a7dd80f6b97079d3d.png?x-bce-process=image/resize,w_1140,h_240,m_mfit/quality,q_75',
    // 问诊-医生卡-疾病好评排行榜
    offlineCommentReason: 'https://med-fe.cdn.bcebos.com/wz/comicon/doctor_pennant.png',
    // 问诊-保障icon
    baoZhangIcon: 'https://med-fe.cdn.bcebos.com/wenzhen-mini-app/baozhang_6.png',
    // 义诊-分享图片：
    freeclinicShare: 'https://med-fe.cdn.bcebos.com/wenzhen-mini-app/freeclinic_share.jpg',
    // AI皮肤检测-未登录锁 icon
    unLoginLock: 'https://med-fe.cdn.bcebos.com/wenzhen-mini-app/unLoginLock.png',
    // 医生榜单详情页背景图
    doctorRankBg: 'https://med-fe.cdn.bcebos.com/wz-mini/doctorRank/doc_rank_detail_bg1.png',
    // 医生榜单详情页榜单弹窗背景图
    doctorRankPopBg: 'https://med-fe.cdn.bcebos.com/wz-mini/doctorRank/dialog_bg_img2.png',
    // 云影像默认为空图片
    dicomDefaultImg: 'https://med-fe.cdn.bcebos.com/wenzhen-mini-app/dicomImg.png',
    // 华西医院背景图
    huaxiBgImg: 'https://med-fe.cdn.bcebos.com/wenzhen-mini-app/huaxiBgImg.png',
    jkbotOpt: 'https://med-fe.cdn.bcebos.com/wz/jkbotOpt.png',
    jkbotBgLogoNew: 'https://med-fe.cdn.bcebos.com/wz/jkbotBgLogoNew.png',
    // 默认医院logo
    hospitalDefaultLogo:
        'https://ss0.baidu.com/6ONWsjip0QIZ8tyhnq/it/u=2128139361,2499500856&fm=179&app=35&f=PNG?w=144&h=144&s=88011F7C4F636F204C6611D20300E0BA',
    qiweiGreenIcon: 'https://med-fe.cdn.bcebos.com/wenzhen-mini-app/guahao/wxGreenIcon.png',
    qiweiWhiteIcon: 'https://med-fe.cdn.bcebos.com/wenzhen-mini-app/guahao/wxWhiteIcon.png',
    qiweiGreenBg: 'https://med-fe.cdn.bcebos.com/wenzhen-mini-app/guahao/greenBg.png',
    qiweiWhiteBg: 'https://med-fe.cdn.bcebos.com/wenzhen-mini-app/guahao/whiteBg.png',
    // 播放暂停Btn
    videoPauseBtn: 'https://med-fe.cdn.bcebos.com/wenzhen-mini-app/textVideo-play.png',
    // 播放中Btn
    videoPlayBtn: 'https://med-fe.cdn.bcebos.com/wenzhen-mini-app/textVideo-pause.png',
    // 播放暂停gif条
    // videoPauseGif: 'https://med-fe.cdn.bcebos.com/wenzhen-mini-app/textVideo-pauseing.png',
    // 播放中gif条
    // videoPlayGif: 'https://med-fe.cdn.bcebos.com/wenzhen-mini-app/textVideo-playing.gif',
    // 订单列表3元三甲问诊券图标（带渐变）
    orderListCouponIcon: 'https://med-fe.cdn.bcebos.com/physical/coupon-icon.png',
    // 金色健康Logo
    healthGoldLogo: 'https://med-fe.cdn.bcebos.com/wenzhen-mini-app/healthGoldLogo.png',
    // 订单列表3元三甲问诊券图标（带渐变）
    rankListSearchCloseIcon:
        'https://med-fe.cdn.bcebos.com/wz-mini/doctor-rank-list-close-icon.png',
    alipayLogo: 'https://med-fe.cdn.bcebos.com/wenzhen-mini-app/alipay.png',
    wechatpayLogo: 'https://med-fe.cdn.bcebos.com/wenzhen-mini-app/wechatpay.png',
    // 非定向sku推荐理由
    reasonsLeft: 'https://med-fe.cdn.bcebos.com/wz/common/reasons-left-new.png',
    reasonsRight: 'https://med-fe.cdn.bcebos.com/wz/common/reasons-right-new.png',
    goodAtIcon: 'https://med-fe.cdn.bcebos.com/wz/goodAtIcon.png',
    // 挂号-问医生医生头像
    askDoctorAvatar:
        'https://expertimg.cdn.bcebos.com/expertcms/med/allhealth/wz_icon_search_head.png',
    // 挂号-我的挂号悬浮球图标
    myghIcon: 'https://med-fe.cdn.bcebos.com/gh/mygh.png',
    // 挂号-云影像删除图标
    myPhotoIcom: 'https://med-fe.cdn.bcebos.com/ghdicom/icon_photo.png',
    // 医生团队
    teamHomeExpertDefault: 'https://zhuanjia.cdn.bcebos.com/zhuanjia/b1565609865906356344.png',
    // 挂号-标题栏左右图标
    titleLeftIcon: 'https://med-fe.cdn.bcebos.com/wenzhen-mini-app/guahao/titleLeftIcon.png',
    titleRightIcon: 'https://med-fe.cdn.bcebos.com/wenzhen-mini-app/guahao/titleRightIcon.png',
    videoInfoBg: 'https://med-fe.cdn.bcebos.com/wenzhen-mini-app/video-info-bg.png',
    ineffectiveIcon: 'https://med-fe.cdn.bcebos.com/wz%2Fineffective.png',
    ineffectiveNewIcon: 'https://med-fe.cdn.bcebos.com/wz/ineffectiveNew.png',
    ineffectiveNew: 'https://med-fe.cdn.bcebos.com/wenzhen-mini-app/ineffective_new.png',
    freeTagIcon: 'https://med-fe.cdn.bcebos.com/wz%2FfreeNew.png',
    // 复旦榜
    fudanIcon:
        'https://material-from-upload.cdn.bcebos.com/images/yd/2025-02/fb92efe21f460d1d03782a7176d4581c.png',
    // 滚动到底部按钮
    arrowDownIcon: 'https://med-fe.cdn.bcebos.com/private-doctor/arrow-down.png',
    scrollToBottomIcon: 'https://med-fe.cdn.bcebos.com/private-doctor/scroll-to-bottom-btn.png',
    // 挂号-vita挂号卡标题图标
    guahaoVitaCardTitleIcon: 'https://med-fe.cdn.bcebos.com/wz-mini/vitaGuahaoRightArrow.png',
    // 背书-popup 头部大标题
    endorsePopupTitleIcon: 'https://med-fe.cdn.bcebos.com/vita/vita_bigTitle.png',
    // 背书条小箭头图标
    endorseLineRightArrowIcon: 'https://med-fe.cdn.bcebos.com/vita/beishu/rightArrow.png',
    // nps提交成功
    npsSuccessIcon: 'https://med-fe.cdn.bcebos.com/wenzhen-mini-app/nps_model_success.png',
    goodAt: 'https://med-fe.cdn.bcebos.com/wenzhen-mini-app/vita/expGoodAt.png',
    expRuzhu: 'https://med-fe.cdn.bcebos.com/wenzhen-mini-app/expRuzhu.png'
};
