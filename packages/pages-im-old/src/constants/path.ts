// 微信小程序登录页
export const WX_LOGIN_PATH =
    process.env.TARO_ENV === 'weapp' ? 'pages/bdpass/index' : 'pages/common/error/index';

export const weappShareBlacklist = [
    '/pages/chat/index', // 问诊im
    '/pages/guahao/orderList/index', // 我的挂号
    '/pages/guahao/orderDetail/index', // 挂号订单详情
    '/pages/guahao/orderState/index', // 挂号订单状态页
    '/pages/guahao/patientInfoEdit/patientFill/index', // 挂号-填写挂号信息
    '/pages/imRecipeLlist/index', // 就诊人信息页
    '/pages/recipellist/index', // 我的处⽅
    '/pages/recipeldetail/index', // 处⽅详情
    '/pages/message/list/index', // 消息列表
    '/pages/personalCenter/index', // 个⼈中⼼
    '/pages/patientRegistration/detail/index', // 患者报道状态页
    '/pages/patientRegistration/patientInfo/index', // 患者报道填写信息页
    '/vas/pages/orderList/index', // 我的服务包
    '/vas/pages/mineEquity/index', // 服务包订单详情
    '/pages/serviceRecord/list/index', // 我的医⽣
    '/pages/serviceRecord/detail/index', // 我的医⽣-服务详情
    '/pages/order/list/index', // 我的问诊
    '/pages/order/detail/index', // 问诊订单详情
    '/pages/middlePage/index', // 中间页
    '/pages/reward/index', // 答谢医生页面
    '/pages/common/validateLogin/index', // 账号不一致的错误提示页面
    '/pages/common/loginMiddlePage/index', // 登录中间页面
    '/pages/common/error/index', // 错误页面
    '/pages/guahao/middlePage/index', // 挂号中间页
    '/pages/bdpass/index' // 登录页
];
