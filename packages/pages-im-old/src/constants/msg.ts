export const MSG_CARDID_ENUM_STREAM = {
    ImThinking: 2,
    ImFlow: 11412,
    ImCollectedInfoAndSku: 11600,
    ImAIRecommendExpert: 11601, // 定向医生卡
    ImAIRecommendUnDirect: 11602, // 非定向sku卡
    ImWelcomeModule: 11603, // 欢迎模块
    ImAIGuahaoRecExpert: 11604, // 挂号推荐医生卡
    ImAIFormMenstrualPeriod: 11605, // 经期计算ai表单
    ImAIFormOvulationPeriod: 11606, // 排卵期计算ai表单
    ImUploadGuide: 11546 // 上传图片引导卡
} as const;

export const MSG_KEY_LENGTH = 10;

export const MSG_THINKING_MSG_KEY_PREFIX = 'thinking_';

export type MSG_CARDID_ENUM_STREAM_TYPE =
    (typeof MSG_CARDID_ENUM_STREAM)[keyof typeof MSG_CARDID_ENUM_STREAM];
export type MSG_CARDID_STREAM_TYPE = typeof MSG_CARDID_ENUM_STREAM;

export const MSG_CARDID_ENUM = {
    ImInputing: 1, // 本地交互使用
    ImCommon: 11401,
    ImText: 11402,
    ImImage: 11403,
    ImAudio: 11404,
    ImAI: 11406,
    ImSystemMsg: 11407,
    ImMultImage: 11408,
    ImAction: 11409,
    ImVideo: 11410,
    ImTextAudio: 11411, // 文字+语音卡片
    ImRichMsg: 11413, // 文字+图片
    ImGridCard: 11501,
    ImFocusDoctor: 11502,
    ImUndirectService: 11503,
    ImFocusService: 11504,
    ImUndirectDoctor: 11505,
    ImRepurchase: 11506,
    ImDiscountReminder: 11407,
    ImNoFocusDoctor: 11509,
    ImGuahao: 11510,
    ImFaqContent: 11511,
    ImFaqComment: 11512,
    ImFocusRecommend: 11513,
    ImHighLiterText: 11514,
    ImEducation: 11515,
    ImChatRecord: 11516,
    ImPhone: 11517,
    ImZhaomu: 11519,
    ImOpenQuestion: 11520,
    ImChatComment: 11521,
    ImRichText: 11522,
    ImGuideWx: 11523,
    ImDrugsTips: 11524,
    ImMsgOther: 11525,
    ImChatPhone: 11526,
    ImDrugsToChat: 11527,
    ImReferral: 11528,
    ImConsultEdit: 11529,
    ImPatientInfoCollect: 11530,
    ImMedicalRecord: 11531,
    ImMedicalRecordNianjian: 11537,
    ImBanner: 11535,
    ImCoupon: 11532,
    InfoCollectorForm: 11536,
    ImWxGuide: 11533,
    // 仅诊前表单用
    SubmitButton: 11537,
    ImPatientCollector: 11538,
    ImPastProblems: 11539,
    ImUndirectRecExpert: 11540,
    IMUndirectRecExpertHead: 11541, // 占位卡
    ImHistoryMedical: 11542,
    ImBotStreamContent: 11543,
    ImAIRewriteTriage: 11545,
    ImHistoryConsultation: 11544,
    ImNpsVita: 11548,
    ImUploadGuide: 11607 // 图片引导卡
} as const;

/**
 * @description 消息 cardId 值的集合
 */
export type MSG_CARDID_TYPE = (typeof MSG_CARDID_ENUM)[keyof typeof MSG_CARDID_ENUM];

/**
 *
 * @description 视频消息默认的封面图
 */
export const DEFAULT_VIDEO_THUMB = '';

/**
 * @description
 */
export const PUBLIC_SEND_MSG_EVENT = 'publicSendMsg';
export const NEW_SERVICECARD_RENDER_TYPE = [2, 3, 4, 5];
