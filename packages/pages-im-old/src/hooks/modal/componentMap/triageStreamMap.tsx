import React, {type ComponentType} from 'react';

// import ImWeChatModal from '@components/globalModal/ImWeChatModal';
// import MultitalkModal from '@components/globalModal/MultitalkModal';
import { MSG_CARDID_TYPE, MSG_CARDID_ENUM } from '../../../constants/msg';
// import { ImPhoneNumberModal, ImDoctorRealNameAuth } from '../../../components/globalModal';

import {type IProps as MultitalkModalProps} from './index.d';

// 定义弹窗组件的类型
export interface ModalProps extends Omit<MultitalkModalProps, 'scene'> {
    // scene?: MultitalkModalProps['scene'];
    interaction: any;
    interactionInfo: any;
    onClosePopup: () => void;
    msgData?: any;
    open: boolean;
    paramsInfo?: Record<string, any>;
    [key: string]: any;
}

export interface SceneModalMap {
    [key: string]: ComponentType<ModalProps>;
}

export type CardModalMap = Partial<Record<MSG_CARDID_TYPE, SceneModalMap | ComponentType<ModalProps>>>;

const createModalComponent = (Component: React.ComponentType<ModalProps>, extraProps = {}) => {
    return (props: ModalProps) => <Component {...props} {...extraProps} />;
};

export const MODAL_COMPONENTS_MAP: CardModalMap = {
    // [MSG_CARDID_ENUM.ImSystemMsg]: {
    //     changePhone: createModalComponent(ImPhoneNumberModal, {
    //         getVerificationCodeSpace: 60
    //     }),
    //     // multitalkModal: createModalComponent(MultitalkModal, {
    //     //     scene: 'IM'
    //     // })
    // },
    // [MSG_CARDID_ENUM.ImCommon]: {
    //     changePhone: createModalComponent(ImPhoneNumberModal, {
    //         desc: '医生将通过此号码联系您'
    //     })
    // },
    // [MSG_CARDID_ENUM.ImVideo]: {
    //     showVideo: createModalComponent(ImVideoModal)
    // },
    // [MSG_CARDID_ENUM.ImWxGuide]: {
    //     openWxModal: createModalComponent(ImWeChatModal, { open: true })
    // },
    // [MSG_CARDID_ENUM.ImFocusDoctor]: {
    //     showAuthModal: createModalComponent(ImDoctorRealNameAuth, { open: true })
    // },
    // [MSG_CARDID_ENUM.ImUndirectDoctor]: {
    //     showAuthModal: createModalComponent(ImDoctorRealNameAuth, { open: true })
    // }
};