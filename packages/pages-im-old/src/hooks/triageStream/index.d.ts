import type {MSG_CARDID_TYPE, MSG_CARDID_ENUM_STREAM_TYPE} from '../../constants/msg';

import type {IPicProps} from '../../typings/upload';
import type {MsgId, Qid, InteractionInfo} from '../../typings';
import type {SessionId} from '../../store/triageStreamAtom/index.type.ts';
import {type FkParamsVal} from '../../utils/generalFunction/riskControler';
import type {
    InputImgMap,
    ConversationSSEParams,
    SceneTypeOfParams
} from '../../models/services/triageStream/sse/index.d';

type MsgType = 'text' | 'image' | 'richText';
type Para = ConversationSSEParams['params'];

// 定义 richText 类型的 content 结构
interface RichTextContent {
    text: string;
    images?: {
        fileName: string;
        path: string;
        filePath: string;
    }[];
    hiddenInfo?: Record<string, unknown>;
    hiddenInfoType?: string;
}

export interface CreateConversationArgs {
    withOutMsg?: boolean;
    withOutThinkingMsg?: boolean;
    msg: Omit<Para['msg']['payload'][0], 'msgKey'> &
        (
            | {
                  type: Exclude<MsgType, 'richText'>;
                  preData?: IPicProps[];
                  sceneType: SceneTypeOfParams;
                  content: string;
              }
            | {
                  type: Extract<MsgType, 'richText'>;
                  preData?: IPicProps[];
                  sceneType?: SceneTypeOfParams;
                  content: RichTextContent;
              }
        );
    ctrlData?: Para['ctrlData'];
    formSubmitData?: Para['formSubmitData']; // ImAi表单提交数据
    intent?: string; // 从im自建页面跳转携带的参数，识别触发imAiForm卡片意图
    passthroughData?: string; // 反问卡携带参数，意图保持不关注内容，当前使用场景为意图保持
    sseOnErrorCallback?: (error: Error) => void;
}

export interface ConvertParamsType {
    cardId: MSG_CARDID_TYPE | MSG_CARDID_ENUM_STREAM_TYPE;
    content?: {
        value: string | InputImgMap;
        origin?: string; // 原尺寸图片链接
        text?: string;
        images?: string[];
    };
    contentType?: number;
    ext: {
        msgKey: MsgId;
        sessionId: SessionId;
    };
}

export interface AntiPassDataType {
    refreshParams: {
        sessionId?: string;
        qid?: Qid;
    };
    data: {
        interaction: string;
        interactionInfo: InteractionInfo;
    };
    fkParams: FkParamsVal;
}
