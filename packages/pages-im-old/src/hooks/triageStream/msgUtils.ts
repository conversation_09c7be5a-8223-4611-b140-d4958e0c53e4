/* eslint-disable @typescript-eslint/ban-ts-comment */
import {
    MSG_CARDID_ENUM,
    MSG_CARDID_ENUM_STREAM,
    MSG_CARDID_ENUM_STREAM_TYPE,
    type MSG_CARDID_TYPE
} from '../../constants/msg';

import {
    updateAdjectiveDirectedSkuMsgIdAtom,
    updateImAjectiveRecommendExpertMsgIdAtomAtom,
    updateImGuahaoRecommendExpertMsgIdAtomAtom,
    updateImAjectiveRecommendUnDirectMsgIdAtomAtom,
    getAdjectiveDirectedSkuMsgId,
    updateImWelcomeMouleMsgIdsAtom
} from '../../store/triageStreamAtom/speCardAtom';

import type {MsgId} from '../../typings';
import {OwnerTypeEnum} from '../../typings/msg.type';
import type {AgreementDataType} from '../../models/services/triageStream/index.d';
import type {InputImgMap} from '../../models/services/triageStream/sse/index.d';
import type {MsgItemType, SessionId} from '../../store/triageStreamAtom/index.type.ts';

import type {ConvertParamsType} from './index.d';

/**
 * 将输入的文本内容、会话ID和消息键转换为文字消息
 *
 * @param v 包含文本内容、会话ID和消息键的对象
 * @returns 返回消息项类型
 */
export const convertText = (v: {
    content: string;
    sessionId: SessionId;
    msgKey: MsgId;
}): MsgItemType<unknown> => {
    const {content, sessionId, msgKey} = v;
    const curTime = new Date().getTime();

    const d: MsgItemType<unknown> = {
        type: 'static',
        meta: {
            sessionId,
            msgId: msgKey,
            ownerType: OwnerTypeEnum['需求方'],
            sendTime: String(curTime),
            createTime: String(curTime),
            posterInfo: {
                avatar: ''
            },
            showPosterRole: 1,
            localMsgStatus: 'pending',
            localExt: {
                dataSource: 'mock',
                insertType: 'push',
                needScrollToBottom: true
            }
        },
        data: {
            action: 'end',
            content: {
                cardId: 11402,
                cardName: 'ImText',
                data: {
                    cardStyle: {
                        needHead: true
                    },
                    content: {
                        value: content
                    }
                }
            }
        }
    };

    return d;
};

/**
 * 将输入的文本内容、会话ID和消息键转换为文字消息
 *
 * @param v 包含文本内容、会话ID和消息键的对象
 * @returns 返回消息项类型
 */
export const convertImage = (v: {
    content: string;
    sessionId: SessionId;
    msgKey: MsgId;
}): MsgItemType<unknown> => {
    const {content, sessionId, msgKey} = v;
    const curTime = new Date().getTime();

    const d: MsgItemType<unknown> = {
        type: 'static',
        meta: {
            sessionId,
            msgId: msgKey,
            ownerType: OwnerTypeEnum['需求方'],
            sendTime: String(curTime),
            createTime: String(curTime),
            posterInfo: {
                avatar: ''
            },
            showPosterRole: 1,
            localMsgStatus: 'pending',
            localExt: {
                dataSource: 'mock',
                insertType: 'push',
                needScrollToBottom: true
            }
        },
        data: {
            action: 'end',
            content: {
                cardId: 11403,
                cardName: 'ImImage',
                data: {
                    cardStyle: {
                        needHead: true
                    },
                    content: {
                        value: content,
                        origin: content
                    }
                }
            }
        }
    };

    return d;
};

/**
 * 将输入的文本内容、会话ID和消息键转换为富文本消息
 *
 * @param v 包含文本内容、图片信息、会话ID和消息键的对象
 * @returns 返回消息项类型
 */
export const convertRichText = (v: {
    content: {
        value: InputImgMap;
    };
    sessionId: SessionId;
    msgKey: MsgId;
}): MsgItemType<unknown> => {
    const {content, sessionId, msgKey} = v;
    const curTime = new Date().getTime();

    const d: MsgItemType<unknown> = {
        type: 'static',
        meta: {
            sessionId,
            msgId: msgKey,
            ownerType: OwnerTypeEnum['需求方'],
            sendTime: String(curTime),
            createTime: String(curTime),
            posterInfo: {
                avatar: ''
            },
            showPosterRole: 1,
            localMsgStatus: 'pending',
            localExt: {
                dataSource: 'mock',
                insertType: 'push',
                needScrollToBottom: true
            }
        },
        data: {
            action: 'end',
            content: {
                cardId: MSG_CARDID_ENUM.ImRichMsg as MSG_CARDID_TYPE,
                cardName: 'ImRichText',
                data: {
                    cardStyle: {
                        needHead: true
                    },
                    content: {
                        list: [
                            {
                                sectionId: 1,
                                images: content?.value?.images?.map(item => ({
                                    icon: item.path,
                                    origin: item.path,
                                    small: item.path
                                })),
                                type: 'images'
                            },
                            {
                                sectionId: 2,
                                content: content?.value?.text,
                                type: 'text'
                            }
                        ]
                    }
                }
            }
        }
    };

    return d;
};

/**
 * 将正在思考的消息转化为 MsgItemType 类型
 *
 * @param v 包含 sessionId 和 msgKey 的对象
 * @returns MsgItemType<unknown> 类型的消息
 */
export const covertImThinking = (v: {
    content: string;
    sessionId: SessionId;
    msgKey: MsgId;
}): MsgItemType<unknown> => {
    const {sessionId, msgKey, content} = v;
    const curTime = new Date().getTime();

    const d: MsgItemType<unknown> = {
        type: 'static',
        meta: {
            sessionId,
            msgId: msgKey,
            ownerType: OwnerTypeEnum['服务方'],
            sendTime: String(curTime),
            createTime: String(curTime),
            posterInfo: {
                avatar: ''
            },
            showPosterRole: 1,
            localExt: {
                dataSource: 'mock',
                insertType: 'push',
                needScrollToBottom: true
            }
        },
        // 消息渲染相关内容；
        data: {
            action: 'end',
            content: {
                cardId: MSG_CARDID_ENUM_STREAM.ImThinking as MSG_CARDID_TYPE,
                cardName: 'ImThinking',
                version: 2,
                data: {
                    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                    // @ts-expect-error
                    text: content
                }
            }
        }
    };

    return d;
};

export const convertSystemMsg = (v: {
    content: AgreementDataType;
    sessionId: SessionId;
    msgKey: MsgId;
}): MsgItemType<unknown> => {
    const {content, sessionId, msgKey} = v;
    const curTime = new Date().getTime();

    const d: MsgItemType<unknown> = {
        type: 'static',
        meta: {
            sessionId,
            msgId: msgKey,
            ownerType: OwnerTypeEnum['系统'],
            sendTime: String(curTime),
            createTime: String(curTime),
            posterInfo: {
                avatar: ''
            },
            showPosterRole: 1,
            localMsgStatus: 'success',
            localExt: {
                dataSource: 'mock',
                insertType: 'push',
                needScrollToBottom: true
            }
        },
        data: {
            action: 'end',
            content: {
                cardId: 11407,
                cardName: 'ImSystemMsg',
                data: {
                    cardStyle: {
                        renderType: 0
                    },
                    content
                }
            }
        }
    };

    return d;
};

/**
 * 发送内容转换成对应消息协议
 *
 * @param arg 转换参数
 * @returns 消息项类型，若转换失败则返回 undefined
 */
export const convertStreamMsgProtocol = (
    arg: ConvertParamsType
): MsgItemType<unknown> | undefined => {
    const {cardId, content, ext} = arg;

    switch (cardId) {
        case MSG_CARDID_ENUM.ImText:
            return typeof content?.value === 'string'
                ? convertText({
                    msgKey: ext.msgKey,
                    content: content?.value || '',
                    sessionId: ext.sessionId
                })
                : undefined;
        case MSG_CARDID_ENUM.ImImage:
            return typeof content?.value === 'string'
                ? convertImage({
                    msgKey: ext.msgKey,
                    content: content?.origin || '',
                    sessionId: ext.sessionId
                })
                : undefined;
        case MSG_CARDID_ENUM.ImRichText:
            return typeof content?.value === 'object'
                ? convertRichText({
                    msgKey: ext.msgKey,
                    content: content as unknown as {value: InputImgMap},
                    sessionId: ext.sessionId
                })
                : undefined;
        case MSG_CARDID_ENUM_STREAM.ImThinking:
            return typeof content?.value === 'string'
                ? covertImThinking({
                    msgKey: ext.msgKey,
                    sessionId: ext.sessionId,
                    content: content?.value || ''
                })
                : undefined;
        default:
            return undefined;
    }
};

/**
 * 更新特殊消息卡片有效状态
 *
 * @param cardId - 卡片ID
 * @param msgId - 消息ID
 */
export const updateSpecialCardAdjectiveMsgId = (
    cardId: MSG_CARDID_ENUM_STREAM_TYPE,
    msgId: MsgId,
    ops?: {
        isUnForceUpdate?: boolean;
        type?: 'unshift' | 'push';
    }
) => {
    const shouldUpdate = (currentValue: MsgId | null) => {
        if (ops?.isUnForceUpdate) {
            // 如果是 unshift 操作，只有当前 atom 没有值时才更新
            return !currentValue;
        }
        // 非 unshift 操作，始终更新
        return true;
    };

    // 更新有效定向SKU消息的ID；
    if ((cardId as MSG_CARDID_ENUM_STREAM_TYPE) === MSG_CARDID_ENUM_STREAM.ImCollectedInfoAndSku) {
        const currentValue = getAdjectiveDirectedSkuMsgId();
        if (shouldUpdate(currentValue)) {
            updateAdjectiveDirectedSkuMsgIdAtom(msgId);
        }
        return;
    }

    // 更新 AI 推荐专家消息 ID；
    if ((cardId as MSG_CARDID_ENUM_STREAM_TYPE) === MSG_CARDID_ENUM_STREAM.ImAIRecommendExpert) {
        updateImAjectiveRecommendExpertMsgIdAtomAtom({msgId, type: ops?.type || 'push'});
        return;
    }

    // 更新 挂号 推荐专家消息 ID；
    if ((cardId as MSG_CARDID_ENUM_STREAM_TYPE) === MSG_CARDID_ENUM_STREAM.ImAIGuahaoRecExpert) {
        updateImGuahaoRecommendExpertMsgIdAtomAtom({msgId, type: ops?.type || 'push'});
        return;
    }

    // 更新 AI 推荐未定向消息 ID；
    if ((cardId as MSG_CARDID_ENUM_STREAM_TYPE) === MSG_CARDID_ENUM_STREAM.ImAIRecommendUnDirect) {
        updateImAjectiveRecommendUnDirectMsgIdAtomAtom({msgId, type: ops?.type || 'push'});

        return;
    }

    if ((cardId as MSG_CARDID_ENUM_STREAM_TYPE) === MSG_CARDID_ENUM_STREAM.ImWelcomeModule) {
        updateImWelcomeMouleMsgIdsAtom(msgId, ops?.type || 'push');
        return;
    }
};
