import {hideKeyboard} from '@tarojs/taro';
import {useAtomValue, useSetAtom} from 'jotai';

import {curSessionMsgIdsAtom, triageStreamMsgAtomFamily} from '../../store/triageStreamAtom/msg';
import {
    curSessionIdAtom,
    textareaHoldAtom,
    textareaFocusAtom,
    textareaPlaceholder
} from '../../store/triageStreamAtom';

import {
    titleInfoAtom,
    ongoingToastAtom,
    viewOrderInfoAtom,
    showBotEntranceCardAtom
} from '../../store/triageStreamAtom/otherData';

import type {MsgId} from '../../typings';
import type {SessionId, MsgItemType} from '../../store/triageStreamAtom/index.type.ts';

/**
 * 消息数据集控制器自定义 Hook
 *
 * @param msgId 消息ID
 * @returns 包含更新消息数据功能的对象
 */
export const useMsgDataSetController = ({msgId}: {msgId: MsgId}) => {
    const sessionId = useAtomValue(curSessionIdAtom);
    const dispatch = useSetAtom(triageStreamMsgAtomFamily(`${sessionId || ''}_${msgId}`));

    /**
     * 更新这条消息数据
     *
     * @param {MsgItemType} msgData  消息数据类型
     */
    const updateMsgData = (msgData: MsgItemType<unknown>) => {
        dispatch({
            type: 'update',
            payload: msgData // 消息数据
        });
    };

    /**
     * 更新消息数据中的 data 部分
     *
     * @param {MsgItemType['data']} data 消息数据中的 data 部分
     */
    const updateMsgDataOfData = (data: MsgItemType<unknown>['data']) => {
        dispatch({
            type: 'updateDataOfData',
            payload: data // 消息数据中的 data 部分；
        });
    };

    return {
        updateMsgData,
        updateMsgDataOfData
    };
};

/**
 * 获取消息数据的控制器
 *
 * @param id 消息类型标识
 * @returns 包含数据对象的对象
 */
export const useMsgDataGetController = ({msgId}: {msgId: MsgId}) => {
    const sessionId = useAtomValue(curSessionIdAtom);
    // 获取数据，数据索引为 sessionId 拼接 msgId；
    // Tips: 当没有 sessionId 时，会返回 undefined，创建的 atom 后续会销毁；
    const data = useAtomValue(triageStreamMsgAtomFamily(`${sessionId || ''}_${msgId}`));

    return {
        data
    };
};

/**
 * 获取会话消息ID的钩子函数
 *
 * @param _id 会话ID
 * @returns 包含消息ID的对象
 */
export const useGetSessionMsgIds = (_id?: SessionId) => {
    const msgIds = useAtomValue(curSessionMsgIdsAtom);

    return {
        msgIds
    };
};

/**
 * 更改triage页面是否支持textarea的hold
 *
 * @param action 页面的动作是否支持hold
 * @returns 是否支持hold
 */

export const useGetTextareaHoldType = () => {
    const setHold = useSetAtom(textareaHoldAtom);

    const updateTriageTextareaHold = () => {
        setHold(true);
    };

    const updateTriageTextareaNotHold = () => {
        setHold(false);
    };

    return {
        updateTriageTextareaHold,
        updateTriageTextareaNotHold
    };
};

/**
 * 更改triage页面是否支持textarea的focus
 *
 * @param action 页面的动作是否支持focus
 * @returns 是否支持focus
 */
export const useGetTextareaFocusType = () => {
    const setFocus = useSetAtom(textareaFocusAtom);

    const updateTriageTextareaFocus = () => {
        setFocus(true);
    };

    const updateTriageTextareaNotFocus = () => {
        setFocus(false);

        if (process.env.TARO_ENV === 'swan') {
            hideKeyboard();
        }
    };

    return {
        updateTriageTextareaFocus,
        updateTriageTextareaNotFocus
    };
};

export const useGetTextareaFocusStatus = () => {
    const focusStatus = useAtomValue(textareaFocusAtom);

    return {
        focusStatus
    };
};

/**
 * 获取进行中订单的 toast 引导弹窗数据
 *
 * @returns {OngoingToastType} 进行中订单的 toast 引导弹窗数据
 */
export const useGetOngoingToast = () => {
    const ongoingToast = useAtomValue(ongoingToastAtom);

    return {
        ongoingToast
    };
};

/**
 * 获取挽留弹窗实验 655951 的开关状态
 *
 * @returns 返回实验开关状态
 */
export const useGetTBotEntranceCardStatus = () => {
    const isShowBotEntranceCard = useAtomValue(showBotEntranceCardAtom);

    return {
        isShowBotEntranceCard
    };
};

/**
 * 获取标题信息
 *
 * @returns {TitleInfoType} 标题信息
 */
export const useGetTitleInfo = () => {
    const titleInfo = useAtomValue(titleInfoAtom);

    return {
        titleInfo
    };
};

/**
 * 获取查看订单信息
 *
 * @returns {viewOrderEntrysType} 查看订单信息
 */
export const useGetViewOrderInfo = () => {
    const viewOrderInfo = useAtomValue(viewOrderInfoAtom);

    return {
        viewOrderInfo
    };
};

/**
 *
 * @returns 获取textarea的placeholder
 */
export const useGetTextareaPlaceholder = () => {
    const placeholder = useAtomValue(textareaPlaceholder);

    return {
        placeholder
    };
};

/**
 *
 * @returns 设置textarea的placeholder
 */
export const useSetTextareaPlaceholder = () => {
    const setPlaceholder = useSetAtom(textareaPlaceholder);

    return {
        setPlaceholder
    };
};
