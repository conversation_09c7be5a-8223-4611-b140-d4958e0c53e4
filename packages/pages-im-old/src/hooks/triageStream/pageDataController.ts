import {useCallback} from 'react';
import {useAtomValue, useSetAtom} from 'jotai';

import {
    createTriageStreamMsgAtom,
    updateCurSessionMsgIdsAtom
} from '../../store/triageStreamAtom/msg';
import {
    userData<PERSON>tom,
    inputData<PERSON>tom,
    popData<PERSON>tom,
    lastMsgIdAtom,
    curSessionIdAtom,
    sessionCapsulesToolsAtom,
    imageSourceAtom
} from '../../store/triageStreamAtom';
import {
    adjectiveDirectedSkuMsgIdAtom,
    adjectiveRecommendExpertMsgIdAtom,
    guahaoRecommendExpertMsgIdAtom,
    adjectiveRecommendUnDirectMsgIdAtom
} from '../../store/triageStreamAtom/speCardAtom';
import {type MSG_CARDID_ENUM_STREAM_TYPE} from '../../constants/msg';

import type {MsgId} from '../../typings';
import type {MsgItemType} from '../../store/triageStreamAtom/index.type.ts';

import {updateSpecialCardAdjectiveMsgId} from './msgUtils';

/**
 * 自定义Hook，用于插入消息
 *
 * @returns 包含插入消息功能的对象
 */
export const useInsertMsg = () => {
    const sessionId = useAtomValue(curSessionIdAtom);
    const updateLastMsgId = useSetAtom(lastMsgIdAtom);

    const insertMsg = useCallback(
        (msgId: MsgId, data: MsgItemType<unknown>) => {
            if (!sessionId) return;

            updateCurSessionMsgIdsAtom([msgId], {
                type: 'push'
            });

            createTriageStreamMsgAtom(`${sessionId}_${msgId}`, data);
            updateLastMsgId(msgId);

            const cardId = data?.data?.content?.cardId || '';

            // 更新特殊卡片的有效状态；
            updateSpecialCardAdjectiveMsgId(cardId as MSG_CARDID_ENUM_STREAM_TYPE, msgId);
        },
        [sessionId, updateLastMsgId]
    );

    return {
        insertMsg
    };
};

/**
 * 获取有效定向SKU消息的ID
 *
 * @returns 包含有效定向SKU消息ID的对象
 */
export const useGetAdjectiveDirectedSkuMsgId = () => {
    const adjectiveDirectedSkuMsgId = useAtomValue(adjectiveDirectedSkuMsgIdAtom);

    return {
        adjectiveDirectedSkuMsgId
    };
};

/**
 * 获取有效的 AI 推荐专家消息的 ID
 *
 * @returns 包含有效的 AI 推荐专家消息 ID 的对象
 */
export const useGetAdjectiveRecommendExpertMsgId = () => {
    const adjectiveRecommendExpertMsgId = [
        ...(useAtomValue(adjectiveRecommendExpertMsgIdAtom) || [])
    ];

    return {
        adjectiveRecommendExpertMsgId: adjectiveRecommendExpertMsgId?.pop() || ''
    };
};

/**
 * 获取有效的 挂号卡的 ID
 *
 * @returns 包含有效的 挂号卡的 ID 的对象
 */
export const useGetGuahaoRecommendExpertMsgId = () => {
    const guahaoRecommendExpertMsgId = [...(useAtomValue(guahaoRecommendExpertMsgIdAtom) || [])];

    return {
        guahaoRecommendExpertMsgId: guahaoRecommendExpertMsgId?.pop() || ''
    };
};

/**
 * 获取有效的 AI 推荐未定向消息的 ID
 *
 * @returns 包含有效的 AI 推荐未定向消息 ID 的对象
 */
export const useGetAdjectiveRecommendUnDirectMsgId = () => {
    const adjectiveRecommendUnDirectMsgId = [
        ...(useAtomValue(adjectiveRecommendUnDirectMsgIdAtom) || [])
    ];

    return {
        adjectiveRecommendUnDirectMsgId: adjectiveRecommendUnDirectMsgId?.pop() || ''
    };
};

/**
 * 获取当前会话中的胶囊工具
 *
 * @returns {CapsulesToolsType[]} 返回包含会话中的胶囊工具的对象
 */
export const useGetCapsulesTools = () => {
    const sessionCapsulesTools = useAtomValue(sessionCapsulesToolsAtom);

    return {
        sessionCapsulesTools
    };
};

/**
 * 获取上传图片来源
 */
export const useGetImageSource = () => {
    const imageSource = useAtomValue(imageSourceAtom);

    return {
        imageSource
    };
};

/**
 * 设置图片上传来源
 */
export const useSetImageSource = () => {
    const setImageSource = useSetAtom(imageSourceAtom);

    return {
        setImageSource
    };
};

/**
 * 使用自定义钩子获取用户数据
 *
 * @returns {UserDataType} 返回包含用户数据的对象
 */
export const useGetUserData = () => {
    const userData = useAtomValue(userDataAtom);

    return {
        userData
    };
};

/**
 * 获取最后一条消息的ID
 *
 * @returns 返回包含最后一条消息ID的对象
 */
export const useGetLastMsgId = () => {
    const lastMsgId = useAtomValue(lastMsgIdAtom);

    return {
        lastMsgId
    };
};

/**
 * 获取当前会话的ID
 *
 * @returns 返回SessionId
 */
export const useGetSessionId = () => {
    return useAtomValue(curSessionIdAtom);
};

export const useGetInputData = () => {
    const inputData = useAtomValue(inputDataAtom);

    return {
        inputData
    };
};

export const useGetPopData = () => {
    const popData = useAtomValue(popDataAtom);

    return {
        popData
    };
};

/**
 * 更新用户信息
 *
 * @param {UserDataType} userInfo  用户信息
 */
export const useUpdateUserData = () => {
    const updateUserData = useSetAtom(userDataAtom);

    return {
        updateUserData
    };
};
