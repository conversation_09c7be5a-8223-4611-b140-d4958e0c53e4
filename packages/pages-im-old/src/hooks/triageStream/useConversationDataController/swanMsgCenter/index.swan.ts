import {API_HOST} from '../../../../models/apis/host';
import {ONLINE_HOST} from '../../../../constants/common';

import {getMsgRiskControlAtom} from '../../../../store/triageStreamAtom/msg';

import type {DispatchSwanimActionArgs, SyncSwanimActionArgs, SendSwanMsgOps} from './index.d';

const ONLINE_PUID = '17592364149226';
const TEST_PUID = '17592186946521';

export const failedMsgPrefix = '[发送失败]';

export const transformMsg = ({msg, type}: {msg: string; type: number}): string => {
    switch (type) {
        case 1:
            return msg;
        case 2:
            return '[图片]';
        case 3:
            return '[语音]';
        case 4:
            return '[视频]';
        case 7:
            return msg;
        default:
            return '[消息]';
    }
};

/**
 * 手百消息中心消息写入；
 * @link https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/bYw-fpFT3G/t6a80egJDi/te97jQ8-SXjuVs#anchor-2ee59eb0-321f-11ef-924b-d31258bbdff5
 * @link https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/xjYiDpu--c/RkUKORH3Hb/RkPd_mVB85l8Ll
 * @description 手百消息中心消息写入；
 * @param arg
 * @returns {Promise<DispatchSwanimActionArgs>}
 */
export const dispatchSwanimAction = (arg: DispatchSwanimActionArgs) => {
    return new Promise((resolve, reject) => {
        try {
            const {content, contentType} = arg?.msgData || {};
            if (!content || !contentType) {
                reject(new Error('msgData is invalid'));
                return;
            }
            // eslint-disable-next-line no-console
            console.info('dispatchSwanimAction arg', arg);
            const text = transformMsg({
                msg: typeof content === 'string' ? content : content.text,
                type: contentType
            });

            sendSwanMsg(text, {
                onResolve: res => {
                    resolve({
                        ...(res || {}),
                        taskType: 'swanMsgCenter',
                        msgData: arg.msgData
                    });
                },
                onReject: err => {
                    reject({
                        taskType: 'swanMsgCenter',
                        msgData: arg.msgData,
                        error: err
                    });
                }
            });
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error('dispatchSwanimAction error', error);
            reject({
                taskType: 'swanMsgCenter',
                msgData: arg.msgData,
                error: error
            });
        }
    });
};

// 双写逻辑场景：（聊天群聊：11474384 ）
// 业务： FE: wanghaoyu08; RD: jiangchuan02; PM: chenxuewu;
// 消息中心：RD：jiangzhongyu；客户端：wangmeng50；
// 1. 业务成功，手百成功
// -- 无需特殊处理

// 2. 业务失败，手百成功

// 2.1 正常失败
// -- 业务前端小程序在线发送 [发送失败] + 发送内容 覆盖；
// 2.2 业务命中风控，手百成功；
// -- 业务后端离线发送【您发送的内容包含敏感信息......】覆盖；
// -- 业务前端小程序在线发送【您发送的内容包含敏感信息......】覆盖；

// 3. 业务成功，手百失败
// -- 暂不处理 C2B 消息，业务后端会同步 B2C 消息自然覆盖；

/**
 * 同步手百消息中心消息状态；
 * @description 同步手百消息中心消息状态；
 * @param arg
 * @returns
 */
export const syncSwanMsgStatus = (
    arg: SyncSwanimActionArgs
): Promise<{status: 'success' | 'rejected'}> => {
    return new Promise((resolve, reject) => {
        try {
            const {sseRes, swanImRes} = arg;
            console.error('syncSwanMsgStatus arg sseRes', sseRes);
            console.error('syncSwanMsgStatus arg swanImRes', swanImRes);

            console.info(
                '进入分支',
                sseRes.status === 'fulfilled' && swanImRes.status === 'fulfilled'
            );
            if (sseRes.status === 'fulfilled' && swanImRes.status === 'fulfilled') {
                // 双写请求都成功；
                const msgRiskControl = getMsgRiskControlAtom();

                console.info('msgRiskControl', msgRiskControl);

                if (msgRiskControl?.hitRisk) {
                    console.error('syncSwanMsgStatus 业务风控拦截', swanImRes.value);
                    sendSwanMsg(msgRiskControl.riskDesc, {
                        onResolve: () => {
                            resolve({
                                status: 'rejected'
                            });
                        },
                        onReject: err => {
                            reject(err);
                        }
                    });
                    return;
                }

                resolve({
                    status: 'success'
                });
            } else if (sseRes.status === 'fulfilled' && swanImRes.status === 'rejected') {
                // 手百消息中心双写失败， 暂不处理业务后端会自动同步 B2C 消息覆盖；
                console.error('syncSwanMsgStatus 手百消息中心双写失败', arg);

                resolve({
                    status: 'rejected'
                });
            } else if (sseRes.status === 'rejected' && swanImRes.status === 'fulfilled') {
                // 业务双写失败；
                console.error('syncSwanMsgStatus 业务双写失败', arg);
                console.error(
                    'syncSwanMsgStatus 业务双写失败，消息内容',
                    swanImRes.value.content.text
                );

                sendSwanMsg(failedMsgPrefix + swanImRes.value.content.text, {
                    onResolve: () => {
                        resolve({
                            status: 'rejected'
                        });
                    },
                    onReject: err => {
                        reject(err);
                    }
                });
            }
        } catch (error) {
            reject(error);
        }
    });
};

/**
 * 更新手百消息中心消息状态；（当前场景暂不需要）
 * @param arg
 * @returns
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const updateSwanMsgStatus = (arg: {
    msgId: string;
    type: 1 | 2; // 1： IM发送成功，业务发送失败 2：IM发送失败，业务发送成功
}) => {
    return new Promise((resolve, reject) => {
        try {
            const PAID = API_HOST && ONLINE_HOST.includes(API_HOST) ? ONLINE_PUID : TEST_PUID;

            const {msgId, type} = arg;
            // eslint-disable-next-line no-undef
            swan.imActionDispatcher({
                params: {
                    funcType: 2002,
                    msgId,
                    targetId: PAID,
                    category: 0, // 会话类型（单聊是0，群聊是1）
                    type
                },
                success: res => {
                    // eslint-disable-next-line no-console
                    console.info('updateSwanMsgStatus 同步手百消息中心成功', res);
                    const {errCode, data} = res;
                    if (+errCode === 0) {
                        resolve(data);
                    } else {
                        reject(data);
                    }
                },
                fail: err => {
                    // eslint-disable-next-line no-console
                    console.error('updateSwanMsgStatus 同步手百消息中心失败', err);
                    reject(err);
                }
            });
        } catch (err) {
            console.error('updateSwanMsgStatus 同步手百消息中心报错', err);
            reject(err);
        }
    });
};

/**
 *
 * @description 手百消息中心消息发送；
 * @param text
 * @param onResolve
 * @param onReject
 * @returns
 */
export const sendSwanMsg = (text: string, ops?: SendSwanMsgOps) => {
    return new Promise((resolve, reject) => {
        const {onResolve, onReject} = ops || {};

        try {
            const PAID = API_HOST && ONLINE_HOST.includes(API_HOST) ? ONLINE_PUID : TEST_PUID;

            const params = {
                funcType: 2000, // 自定义消息发送；
                content: JSON.stringify({
                    text,
                    sys_type: 3,
                    ext: {
                        biz: 'jkcbot' // 复用之前标识字段，新增 cbot 值；
                    }
                }),
                msgType: 28, // 固定按照自定义消息发送；
                paid: PAID, // 待申请的 PAID，非经常变动数据，暂前端写死；后续消息一致性时优化为后端获取；
                paType: 7, // 待申请
                subPaType: 32 // 待申请
            };

            // eslint-disable-next-line no-console
            console.info('手百消息中心双写 sendSwanMsg params：', params);

            // eslint-disable-next-line no-undef
            swan.imActionDispatcher({
                params,
                success: res => {
                    // 成功回调；
                    // eslint-disable-next-line no-console
                    console.info('dispatchSwanimAction success', res);
                    onResolve?.(res);

                    resolve({
                        ...res
                    });
                },
                fail: err => {
                    console.error('dispatchSwanimAction fail', err);
                    onReject?.(err);
                    reject({
                        error: err
                    });
                }
            });
        } catch (error) {
            onReject?.(error);
            console.error('sendSwanMsg error', error);
            reject(error);
        }
    });
};

// function safeParseJSON(str) {
//     try {
//         return JSON.parse(str);
//     } catch (e) {
//         console.error('Failed to parse JSON:', e);
//         return {};
//     }
// }
