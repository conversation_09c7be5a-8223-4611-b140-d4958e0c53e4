import {useCallback, default as React} from 'react';
import {useAtomValue, useSet<PERSON>tom} from 'jotai';

import {
    updateCurSessionMsgIdsAtom,
    createTriageStreamMsgAtom,
    updateTriageStreamMsgAtom,
    updateTriageStreamMsgDataByKey
} from '../../../store/triageStreamAtom/msg';
import {
    curSessionIdAtom,
    lastMsgId<PERSON>tom,
    updateDataFor<PERSON>bc<PERSON>tom
} from '../../../store/triageStreamAtom';

import {
    ONLINE_HOST as APP_ONLINE_HOST,
    BUCKET_NAME as APP_BUCKET_NAME
} from '../../../constants/common';
import {API_HOST as APP_API_HOST} from '../../../models/apis/host';
import {
    MSG_CARDID_ENUM,
    MSG_CARDID_ENUM_STREAM,
    type MSG_CARDID_TYPE
} from '../../../constants/msg';

import {uploadFileToBos} from '../../../utils/basicAbility/upload';

import type {MsgId} from '../../../typings';
import type {MsgItemType} from '../../../store/triageStreamAtom/index.type.ts';
import type {InputImgMap} from '../../../models/services/triageStream/sse/index.d';
import type {AgreementDataType} from '../../../models/services/triageStream/index.d';

import {convertStreamMsgProtocol, convertSystemMsg} from '../msgUtils';
import {useGetInputData} from '../pageDataController';

import type {CreateConversationArgs, RichTextContent} from '../index.d';

/**
 * @module useMessageManager
 * @description
 * 该 Hook 负责会话中消息的创建、格式化、状态更新以及发送前的预处理（如图片上传和格式转换）。
 * 它处理用户发送的消息、系统生成的"思考中"消息，并提供SSE连接成功时更新消息状态的回调。
 *
 * @returns {object} 返回一个包含以下方法的对象：
 * - `mockUserMsg`: (Function) 创建并假上墙用户发送的消息。
 *   负责构建消息协议、更新Jotai状态、更新最后消息ID等。
 *   接受消息对象、消息键及一个用于更新外部lastMsgId的ref作为参数。
 * - `addThinkMsg`: (Function) 创建并显示一个"思考中"的过渡消息。
 *   接受消息键和lastMsgId ref作为参数。
 * - `onSEEConnect`: (Function) 当SSE连接成功建立时，用于更新对应用户消息状态为"成功"的回调。
 *   接受包含消息内容和消息键的对象作为参数。
 * - `convertMsgToSSE`: (Function) 在消息通过SSE发送前对其进行转换。
 *   例如，处理图片上传并将图片URL作为消息内容，或确保文本消息格式正确。
 *   接受原始消息对象作为参数。
 * - `updateUserMsgStatusOnCancel`: (Function) 当SSE被取消时，用于更新特定用户消息状态。
 *   例如，如果取消操作使得该消息变为已处理或成功。接受消息键和目标状态作为参数。
 */
export const useMessageManager = () => {
    const sessionId = useAtomValue(curSessionIdAtom);
    const updateLastMsgId = useSetAtom(lastMsgIdAtom);
    const {inputData} = useGetInputData();

    const bucketConfName =
        APP_API_HOST && APP_ONLINE_HOST.indexOf(APP_API_HOST) > -1
            ? APP_BUCKET_NAME[2]
            : `${APP_BUCKET_NAME[2]}-test`;

    const mockUserMsg = useCallback(
        (
            msg: CreateConversationArgs['msg'],
            msgKey: string,
            currentLastMsgIdRef: React.MutableRefObject<MsgId>,
            ops?: {
                agreementInfo?: AgreementDataType;
                onAgreementInfoCallback?: () => void;
            }
        ): MsgItemType<unknown> | undefined => {
            const ext = {
                sessionId,
                msgKey
            };

            let msgDataProtocol: MsgItemType<unknown> | undefined;
            const msgCardIdMap: {
                [k in (typeof msg)['type']]: MSG_CARDID_TYPE;
            } = {
                text: MSG_CARDID_ENUM.ImText,
                image: MSG_CARDID_ENUM.ImImage,
                richText: MSG_CARDID_ENUM.ImRichText
            };
            if (!sessionId) return undefined;
            msgCardIdMap[msg.type] &&
                (msgDataProtocol = convertStreamMsgProtocol({
                    ext: {
                        sessionId: ext?.sessionId || '',
                        msgKey: ext?.msgKey
                    },
                    cardId: msgCardIdMap[msg.type],
                    content: {
                        value: msg.content!,
                        ...(msg.origin ? {origin: msg.origin} : {})
                    }
                }));

            if (msgDataProtocol && ops?.agreementInfo) {
                const sysMsgKey = `mock_sys_${msgKey}`;
                const sysMsgData = convertSystemMsg({
                    content: ops.agreementInfo,
                    sessionId,
                    msgKey
                });

                ops?.onAgreementInfoCallback?.();
                updateCurSessionMsgIdsAtom([sysMsgKey], {
                    type: 'push'
                });
                createTriageStreamMsgAtom(`${sessionId}_${sysMsgKey}`, sysMsgData);
            }
            if (msgDataProtocol) {
                updateCurSessionMsgIdsAtom([msgKey], {
                    type: 'push'
                });
                createTriageStreamMsgAtom(`${sessionId}_${msgKey}`, msgDataProtocol);
                updateLastMsgId(msgKey);
                updateDataForUbcAtom({
                    lastMsgId: msgKey
                });
                currentLastMsgIdRef.current = msgKey;
            }
            return msgDataProtocol;
        },
        [sessionId, updateLastMsgId]
    );

    const addThinkMsg = useCallback(
        (msgKey: MsgId, currentLastMsgIdRef: React.MutableRefObject<MsgId>): string => {
            if (!sessionId) return '';
            const ext = {
                sessionId,
                msgKey
            };
            const data = convertStreamMsgProtocol({
                ext: {
                    sessionId: ext?.sessionId || '',
                    msgKey: ext?.msgKey
                },
                cardId: MSG_CARDID_ENUM_STREAM.ImThinking,
                content: {value: inputData?.loadingTips || ''}
            });
            if (data) {
                updateCurSessionMsgIdsAtom([msgKey], {
                    type: 'push'
                });
                createTriageStreamMsgAtom(`${sessionId}_${msgKey}`, {
                    ...data,
                    meta: {
                        ...data.meta,
                        localExt: {
                            dataSource: 'mock',
                            insertType: 'push',
                            needScrollToBottom: true
                        }
                    }
                });
                updateDataForUbcAtom({
                    lastMsgId: msgKey
                });
                currentLastMsgIdRef.current = msgKey;
            }
            return msgKey;
        },
        [inputData?.loadingTips, sessionId]
    );

    const onSEEConnect = useCallback(
        (arg: {msgContent: MsgItemType<unknown> | undefined; msgKey: string}) => {
            const {msgContent, msgKey} = arg;
            if (!msgContent || !sessionId) return;

            const updatedMsg: MsgItemType<unknown> = {
                ...msgContent,
                meta: {
                    ...msgContent?.meta,
                    localMsgStatus: 'success'
                }
            };
            updateTriageStreamMsgAtom(`${sessionId}_${msgKey}`, updatedMsg, {
                _debugSymbol: 'onSEEConnect'
            });
        },
        [sessionId]
    );

    const updateUserMsgStatusOnCancel = useCallback(
        (msgKey: string, status: 'success' | 'rejected' | 'aborted') => {
            if (msgKey && sessionId) {
                updateTriageStreamMsgDataByKey(`${sessionId}_${msgKey}`, 'localMsgStatus', status);
            }
        },
        [sessionId]
    );

    const convertMsgToSSE = useCallback(
        async (
            msg: CreateConversationArgs['msg']
        ): Promise<{content: string | InputImgMap; contentType?: number}> => {
            const {type} = msg;
            if (type === 'image') {
                const picData = await uploadFileToBos((msg as any).preData || [], {
                    count: 1,
                    bucketConfName
                });
                return {
                    content: picData[0].fileName || '',
                    ...(msg.contentType ? {contentType: msg.contentType} : {contentType: 2})
                };
            }

            if (type === 'richText') {
                console.info('convertMsgToSSE msg------', msg);
                const {text = '', images: imgList, ...extParams} = msg.content as RichTextContent;

                const images: InputImgMap['images'] = [] as unknown as InputImgMap['images'];

                imgList?.forEach(item => {
                    if (item.fileName) {
                        images.push({
                            value: item.fileName,
                            path: item.path
                        });
                    }
                });
                return {
                    content: {
                        ...(extParams || {}),
                        text,
                        images
                    },
                    ...(msg.contentType ? {contentType: msg.contentType} : {contentType: 7})
                };
            }

            return {
                content: msg.content || '',
                ...(msg.contentType ? {contentType: msg.contentType} : {contentType: 1})
            };
        },
        [bucketConfName]
    );

    return {
        mockUserMsg,
        addThinkMsg,
        onSEEConnect,
        convertMsgToSSE,
        updateUserMsgStatusOnCancel
    };
};
