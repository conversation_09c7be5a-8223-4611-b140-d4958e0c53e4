import {useCallback} from 'react';
import {useAtomValue, useSetAtom} from 'jotai';

import {createTriageStreamMsgAtom, updateCurSessionMsgIdsAtom} from '../../store/docImAtom/msg';
import {
    userData<PERSON>tom,
    inputData<PERSON>tom,
    lastMsgIdAtom,
    curSessionIdAtom,
    curWxLoginDataAtom,
    sessionCapsulesToolsAtom,
    renderSessionCapsulesAtom,
    adjectiveDirectedSkuMsgIdAtom,
    imageSourceAtom
} from '../../store/docImAtom';
import {
    adjectiveRecommendExpertMsgIdAtom,
    adjectiveRecommendUnDirectMsgIdAtom
} from '../../store/docImAtom/speCardAtom';

import {MSG_CARDID_ENUM_STREAM, MSG_CARDID_ENUM_STREAM_TYPE} from '../../constants/msg';

import type {MsgId} from '../../typings';
import type {MsgItemType} from '../../store/docImAtom/index.type';
import {updateSpecialCardAdjectiveMsgId} from './msgUtils';

/**
 * 自定义Hook，用于插入消息
 *
 * @returns 包含插入消息功能的对象
 */
export const useInsertMsg = () => {
    const sessionId = useAtomValue(curSessionIdAtom);
    const updateLastMsgId = useSetAtom(lastMsgIdAtom);

    const insertMsg = useCallback(
        (msgId: MsgId, data: MsgItemType<unknown>, curSessionId?: string) => {
            if (!sessionId && !curSessionId) return;

            updateCurSessionMsgIdsAtom([msgId], {
                type: 'push'
            });

            createTriageStreamMsgAtom(`${sessionId || curSessionId || ''}_${msgId}`, {
                ...data,
                meta: {
                    ...data?.meta,
                    localMsgStatus: 'success',
                    localExt: {
                        dataSource: 'conversation',
                        insertType: 'push',
                        needScrollToBottom: true
                    }
                }
            });
            updateLastMsgId(msgId);

            const cardId = data?.data?.content?.cardId || '';
            // 更新有效定向SKU消息的ID；
            if (
                (cardId as MSG_CARDID_ENUM_STREAM_TYPE) ===
                MSG_CARDID_ENUM_STREAM.ImCollectedInfoAndSku
            ) {
                updateSpecialCardAdjectiveMsgId(cardId as MSG_CARDID_ENUM_STREAM_TYPE, msgId, {
                    type: 'push'
                });
            }
        },
        [sessionId, updateLastMsgId]
    );

    return {
        insertMsg
    };
};

/**
 * 获取有效定向SKU消息的ID
 *
 * @returns 包含有效定向SKU消息ID的对象
 */
export const useGetAdjectiveDirectedSkuMsgId = () => {
    const adjectiveDirectedSkuMsgId = useAtomValue(adjectiveDirectedSkuMsgIdAtom);

    return {
        adjectiveDirectedSkuMsgId: [...adjectiveDirectedSkuMsgId].pop() || ''
    };
};

/**
 * 获取当前会话中的胶囊工具
 *
 * @returns {CapsulesToolsType[]} 返回包含会话中的胶囊工具的对象
 */
export const useGetCapsulesTools = () => {
    const sessionCapsulesTools = useAtomValue(sessionCapsulesToolsAtom);
    const renderSessionCapsules = useAtomValue(renderSessionCapsulesAtom);

    return {
        sessionCapsulesTools,
        renderSessionCapsules
    };
};

/**
 * 使用自定义钩子获取用户数据
 *
 * @returns {UserDataType} 返回包含用户数据的对象
 */
export const useGetUserData = () => {
    const userData = useAtomValue(userDataAtom);

    return {
        userData
    };
};

/**
 * 获取最后一条消息的ID
 *
 * @returns 返回包含最后一条消息ID的对象
 */
export const useGetLastMsgId = () => {
    const lastMsgId = useAtomValue(lastMsgIdAtom);

    return {
        lastMsgId
    };
};

/**
 * 获取当前会话的ID
 *
 * @returns 返回SessionId
 */
export const useGetSessionId = () => {
    return useAtomValue(curSessionIdAtom);
};

/**
 * 获取当前会话的controls gotowxlogin数据，用于微信登录
 *
 * @returns gotowxlogin数据
 */
export const useGetWxLoginData = () => {
    return useAtomValue(curWxLoginDataAtom);
};

export const useGetInputData = () => {
    const inputData = useAtomValue(inputDataAtom);

    return {
        inputData
    };
};

/**
 * 更新用户信息
 *
 * @param {UserDataType} userInfo  用户信息
 */
export const useUpdateUserData = () => {
    const updateUserData = useSetAtom(userDataAtom);

    return {
        updateUserData
    };
};

/**
 * 获取上传图片来源
 */
export const useGetImageSource = () => {
    const imageSource = useAtomValue(imageSourceAtom);

    return {
        imageSource
    };
};

/**
 * 设置图片上传来源
 */
export const useSetImageSource = () => {
    const setImageSource = useSetAtom(imageSourceAtom);

    return {
        setImageSource
    };
};

/**
 * 获取有效的 AI 推荐专家消息的 ID
 *
 * @returns 包含有效的 AI 推荐专家消息 ID 的对象
 */
export const useGetAdjectiveRecommendExpertMsgId = () => {
    const adjectiveRecommendExpertMsgId = [
        ...(useAtomValue(adjectiveRecommendExpertMsgIdAtom) || [])
    ];

    return {
        adjectiveRecommendExpertMsgId: adjectiveRecommendExpertMsgId?.pop() || ''
    };
};

/**
 * 获取有效的 AI 推荐未定向消息的 ID
 *
 * @returns 包含有效的 AI 推荐未定向消息 ID 的对象
 */
export const useGetAdjectiveRecommendUnDirectMsgId = () => {
    const adjectiveRecommendUnDirectMsgId = [
        ...(useAtomValue(adjectiveRecommendUnDirectMsgIdAtom) || [])
    ];

    return {
        adjectiveRecommendUnDirectMsgId: adjectiveRecommendUnDirectMsgId?.pop() || ''
    };
};
