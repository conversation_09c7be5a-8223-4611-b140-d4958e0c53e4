import type {MSG_CARDID_TYPE, MSG_CARDID_ENUM_STREAM_TYPE} from '../../constants/msg';

import type {MsgId, Qid, InteractionInfo} from '../../typings';
import type {SessionId, GetMsgListTransParams} from '../../store/docImAtom/index.type';
import {type FkParamsVal} from '../../utils/generalFunction/riskControler';
import type {SceneTypeOfParams} from '../../models/services/docIm/sse/index.d';

export {SceneTypeOfParams};

export interface CreateConversationArgs {
    msg: {
        type: 'text';
        content: string;
        sceneType: SceneTypeOfParams;
    };
    withOutMsg?: boolean;
    withOutThinkingMsg?: boolean;
    ctrlData?: {
        firstCall?: boolean;
    };
    transData?: GetMsgListTransParams;
}

export interface ConvertParamsType {
    cardId: MSG_CARDID_TYPE | MSG_CARDID_ENUM_STREAM_TYPE;
    content?: {
        value: string;
    };
    ext: {
        msgKey: MsgId;
        sessionId: SessionId;
    };
}

export interface AntiPassDataType {
    refreshParams: {
        sessionId?: string;
        qid?: Qid;
    };
    data: {
        interaction: string;
        interactionInfo: InteractionInfo;
    };
    fkParams: FkParamsVal;
}
