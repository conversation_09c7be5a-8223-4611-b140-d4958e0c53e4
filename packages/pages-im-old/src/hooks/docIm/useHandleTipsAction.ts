// Author: z<PERSON><PERSON>yu03
// Date: 2025-05-23 15:40:57
// Description: 拍照工具开关hook
import {useCallback, useState} from 'react';
import {useAtomValue, useSetAtom} from 'jotai';
import {tipsDataAtom} from '../../store/docImAtom';

import type {SceneTypeOfParams} from '../../models/services/docIm/sse/index.d';

export const useHandleTipsAction = () => {
    const [open, setOpen] = useState(false);
    const [sceneType, setSceneType] = useState<SceneTypeOfParams | undefined>();

    const openUploadPopup = useCallback((sceneTypeVal: SceneTypeOfParams) => {
        setOpen(true);
        setSceneType(sceneTypeVal);
    }, []);

    const closeUploadPopup = useCallback(() => {
        setOpen(false);
        setSceneType(undefined);
    }, []);

    return {
        open,
        openUploadPopup,
        closeUploadPopup,
        sceneType
    };
};

export const useSetTipsData = () => {
    const setTipsData = useSetAtom(tipsDataAtom);

    return {
        setTipsData
    };
};

export const useGetTipsData = () => {
    const tipsData = useAtomValue(tipsDataAtom);

    return tipsData;
};
