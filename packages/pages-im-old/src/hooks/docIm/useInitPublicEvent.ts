import {useCallback, useEffect} from 'react';
import {eventCenter} from '@tarojs/taro';

import {PUBLIC_SEND_MSG_EVENT} from '../../constants/msg';

import {useConversationDataController} from './useConversationDataController';

import type {CreateConversationArgs} from './index.d';

export const useInitPublicSendMsgEvent = () => {
    const {createConversation} = useConversationDataController();

    const handlePublicSendMsg = useCallback(
        (ops: {args: CreateConversationArgs; _symbol: string; sourcePageType: 'docIm'}) => {
            const {args, _symbol, sourcePageType} = ops;

            if (!_symbol) {
                console.error('[MSG Event] 触发 handlePublicSendMsg 错误：缺少 _symbol 参数');
                return;
            }

            if (sourcePageType !== 'docIm') {
                // eslint-disable-next-line no-console
                console.warn(
                    '[MSG Event] 触发 handlePublicSendMsg 错误：非医生分身页面，不处理该事件'
                );
                return;
            }

            createConversation(args);

            // eslint-disable-next-line no-console
            console.info(
                `[MSG Event]${_symbol || 'unknown'} 触发 handlePublicSendMsg 参数：`,
                args
            );
        },
        [createConversation]
    );

    useEffect(() => {
        eventCenter.on(PUBLIC_SEND_MSG_EVENT, ops => handlePublicSendMsg(ops));

        return () => {
            eventCenter.off(PUBLIC_SEND_MSG_EVENT);
        };
    }, [handlePublicSendMsg]);
};
