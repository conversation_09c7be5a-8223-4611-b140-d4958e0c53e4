import {useAtomValue, useSetAtom} from 'jotai';
import {hideKeyboard} from '@tarojs/taro';

import {curSessionMsgIdsAtom, docImMsgAtomFamily} from '../../store/docImAtom/msg';
import {
    curSessionIdAtom,
    textareaHoldAtom,
    textareaFocusAtom,
    getCurSessionIdAtom
} from '../../store/docImAtom';
import {textareaPlaceholder} from '../../store/docImAtom/index';

import type {MsgId} from '../../typings';
import type {SessionId, MsgItemType} from '../../store/docImAtom/index.type';

/**
 * 消息数据集控制器自定义 Hook
 *
 * @param msgId 消息ID
 * @returns 包含更新消息数据功能的对象
 */
export const useMsgDataSetController = ({msgId}: {msgId: MsgId}) => {
    const sessionId = useAtomValue(curSessionIdAtom);
    const dispatch = useSetAtom(docImMsgAtomFamily(`${sessionId || ''}_${msgId}`));

    /**
     * 更新这条消息数据
     *
     * @param {MsgItemType} msgData  消息数据类型
     */
    const updateMsgData = (msgData: MsgItemType<unknown>) => {
        dispatch({
            type: 'update',
            payload: msgData // 消息数据
        });
    };

    /**
     * 更新消息数据中的 data 部分
     *
     * @param {MsgItemType['data']} data 消息数据中的 data 部分
     */
    const updateMsgDataOfData = (data: MsgItemType<unknown>['data']) => {
        dispatch({
            type: 'updateDataOfData',
            payload: data // 消息数据中的 data 部分；
        });
    };

    return {
        updateMsgData,
        updateMsgDataOfData
    };
};

/**
 * 获取消息数据的控制器
 *
 * @param id 消息类型标识
 * @returns 包含数据对象的对象
 */
export const useMsgDataGetController = ({msgId}: {msgId: MsgId}) => {
    const sessionId = getCurSessionIdAtom();

    // 获取数据，数据索引为 sessionId 拼接 msgId；
    // Tips: 当没有 sessionId 时，会返回 undefined，创建的 atom 后续会销毁；
    const data = useAtomValue(docImMsgAtomFamily(`${sessionId || ''}_${msgId}`));

    return {
        data
    };
};

/**
 * 获取会话消息ID的钩子函数
 *
 * @param _id 会话ID
 * @returns 包含消息ID的对象
 */
export const useGetSessionMsgIds = (_id?: SessionId) => {
    const msgIds = useAtomValue(curSessionMsgIdsAtom);

    return {
        msgIds
    };
};

/**
 * 更改triage页面是否支持textarea的hold
 *
 * @param action 页面的动作是否支持hold
 * @returns 是否支持hold
 */

export const useGetTextareaHoldType = () => {
    const setHold = useSetAtom(textareaHoldAtom);

    const updateTriageTextareaHold = () => {
        setHold(true);
    };

    const updateTriageTextareaNotHold = () => {
        setHold(false);
    };

    return {
        updateTriageTextareaHold,
        updateTriageTextareaNotHold
    };
};

/**
 * 更改triage页面是否支持textarea的focus
 *
 * @param action 页面的动作是否支持focus
 * @returns 是否支持focus
 */
export const useGetTextareaFocusType = () => {
    const setFocus = useSetAtom(textareaFocusAtom);

    const updateTriageTextareaFocus = () => {
        setFocus(true);
    };

    const updateTriageTextareaNotFocus = () => {
        setFocus(false);

        if (process.env.TARO_ENV === 'swan') {
            hideKeyboard();
        }
    };

    return {
        updateTriageTextareaFocus,
        updateTriageTextareaNotFocus
    };
};

export const useGetTextareaFocusStatus = () => {
    const focusStatus = useAtomValue(textareaFocusAtom);

    return {
        focusStatus
    };
};

/**
 *
 * @returns 获取textarea的placeholder
 */
export const useGetTextareaPlaceholder = () => {
    const placeholder = useAtomValue(textareaPlaceholder);

    return {
        placeholder
    };
};

/**
 *
 * @returns 设置textarea的placeholder
 */
export const useSetTextareaPlaceholder = () => {
    const setPlaceholder = useSetAtom(textareaPlaceholder);

    return {
        setPlaceholder
    };
};
