/**
 * 图片上传 hook
 */
import {useState, useCallback} from 'react';
import {IPicProps} from '../../typings/upload';
import {SceneTypeOfParams} from '../../models/services/triageStream/sse/index.d';
import {useConversationDataController} from '../triageStream/useConversationDataController';

export const useImageUpload = () => {
    const {createConversation} = useConversationDataController();
    const [popupOpenStatus, setPopupOpenStatus] = useState(false);
    /**
     * 关闭图片上传弹窗
     */
    const closeUploadPopup = useCallback(() => {
        setPopupOpenStatus(false);
    }, []);

    /**
     * 处理图片选择
     * @param res 选择的图片列表
     * @param sceneType 场景类型
     */
    const onSelectedPics = useCallback(
        (res: IPicProps[], {sceneType}: {sceneType: SceneTypeOfParams}) => {
            try {
                const [first] = res;

                if (first?.path && res?.length) {
                    createConversation({
                        msg: {
                            type: 'image',
                            content: first.path,
                            origin: first.path,
                            preData: res,
                            sceneType
                        }
                    });
                }
            } catch (error) {
                console.error('图片上传处理失败:', error);
            }
        },
        [createConversation]
    );

    /**
     * 打开图片上传弹窗
     */
    const openUploadPopup = useCallback(() => {
        setPopupOpenStatus(true);
    }, []);

    return {
        popupOpenStatus,
        closeUploadPopup,
        onSelectedPics,
        openUploadPopup
    };
};
