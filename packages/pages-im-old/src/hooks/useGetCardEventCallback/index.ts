import {useCallback} from 'react';
import {getCurrentPages, showToast} from '@tarojs/taro';

import {API_HOST} from '../../models/apis/host';

import httpRequest from '../../utils/basicAbility/comonRequest/common';
import {ubcCommonViewSend, ubcCommonClkSend} from '../../utils/generalFunction/ubc';
import {navigate, OpenType, UrlParams} from '../../utils/basicAbility/commonNavigate';

import type {InteractionInfo} from '../../typings';

/**
 *
 * @description 获取卡片事件处理回调函数
 */
export const useGetCardEventCallback = () => {
    /**
     *
     * @description 发送异步请求通用回调
     */
    const onRequest = useCallback(params => {
        const {info} = params || {}; // Removed unused msgData

        return new Promise((res, rej) => {
            (async () => {
                try {
                    const {url, method, params: reqParams, contentType = 'application/json'} = info;
                    if (!url) {
                        rej('onRequest 方法调用出错：url 不存在');
                        return;
                    }

                    const respUrl = url.includes('http') ? url : `${API_HOST}${url}`;
                    const [error, resp] = await httpRequest<unknown>({
                        url: respUrl,
                        method: method ? method.toUpperCase() : 'GET',
                        data: reqParams,
                        contentType
                    });

                    if (error) {
                        if (Array.isArray(error) && error.length && error[0].toast) {
                            showToast({
                                icon: 'none',
                                title: error[0].toast
                            });
                        } else {
                            console.error(
                                'useGetCardEventCallback onRequest httpRequest 失败：',
                                error
                            );
                        }
                        rej(error);
                    } else {
                        res(resp);
                    }
                } catch (err) {
                    console.error('useGetCardEventCallback onRequest 出错：', err);
                    rej(err);
                }
            })();
        });
    }, []);

    /**
     *
     * @description 外跳链接通用回调
     */
    const onOpenLink = useCallback(async (params: {info: InteractionInfo}) => {
        try {
            const {info} = params;
            let openType = 'navigate';
            const miniAppName = '';
            const _url = info?.url || '';
            if (process.env.TARO_ENV === 'swan' || process.env.TARO_ENV === 'weapp') {
                const curPage = getCurrentPages().pop();
                const curRoute = curPage?.route || '';
                const regex = /pages\/(.*?)(?=\?|$)/;
                const [res] = _url?.match(regex) || [];
                if (_url && curRoute && res === curRoute && openType !== 'otherMiniApp') {
                    openType = 'redirect';
                }
            }

            navigate({
                url: _url,
                openType: openType as OpenType,
                params: info?.params as UrlParams,
                ...(miniAppName ? {miniAppName} : {})
            });
        } catch (err) {
            console.error('onOpenLink 出错：', err);
        }
    }, []);

    /**
     *
     * @description 发送ubc日志
     */
    const onSendUbc = useCallback((params: {info: InteractionInfo}) => {
        const {info} = params;
        const value = info?.params?.value as string;
        const ext = info?.params?.ext as {[k in string]: string | number};
        const type = info?.params?.type as string;
        // type有值且为view时，发送曝光埋点，默认走点击埋点
        if (type && type === 'view') {
            ubcCommonViewSend({
                value: value || '',
                ext: ext || {}
            });
        } else {
            ubcCommonClkSend({
                value: value || '',
                ext: ext || {}
            });
        }
    }, []);

    return {
        onOpenLink,
        onSendUbc,
        onRequest
    };
};
