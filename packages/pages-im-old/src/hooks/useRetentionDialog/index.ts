import {useState, useCallback} from 'react';
import {getStorageSync, setStorageSync, hideToast} from '@tarojs/taro';
import {useAtomValue} from 'jotai';

import {showBotEntranceCardAtom, npsConfigAtom} from '../../store/triageStreamAtom/otherData';
import {isEmpty} from '../../utils';
import {getNpsInfo} from '../../models/services/nps/getNpsInfo';
import {useGetSessionId} from '../triageStream/pageDataController';
import type {NpsInfoResponse} from '../../models/services/nps/getNpsInfo/index.d';
import {setNpsStorageSync, getNpsStorageInfoSync} from '../../utils/npsStorage';

export const useRetentionDialog = (storageKey: string = 'aiHealthManagerPop-realtime') => {
    // 是否展示挽留弹窗
    const [showDialog, setShowDialog] = useState(false);
    // 是否展示
    const [showNpsPop, setShowNpsPop] = useState(false);
    // 是否阻止返回
    const [backDetain, setBackDetain] = useState(false);
    // 返回状态和导航函数
    const [backState, setBackState] = useState('');
    const [naviFunc, setNaviFunc] = useState<{
        navigateBack?: () => void;
        navigateHome?: () => void;
            }>({});

    // nps popup 返回值
    const [popData, setPopData] = useState<NpsInfoResponse>();

    const isShowBotEntranceCard = useAtomValue(showBotEntranceCardAtom);
    const npsConfig = useAtomValue(npsConfigAtom);
    const curSessionId = useGetSessionId();

    /**
     * 初始化nps挽留弹窗逻辑（检查是否需要展示）
     */
    const initNpsPopupCheck = useCallback(
        npsStorage => {
            if (npsConfig && !isEmpty(npsConfig.showExitSurveyOnBack)) {
                const currentDate = new Date(new Date().toLocaleDateString()).getTime();
                // 不存在或问卷曝光时间大于24h，展示问卷弹窗
                if (
                    !npsStorage ||
                    currentDate - npsStorage?.timestamp > 24 * 60 * 60 * 1000 ||
                    !npsStorage?.availableAppraiseIds?.length
                ) {
                    setBackDetain(true);
                }
            }
        },
        [npsConfig]
    );

    // 24小时内是否曝光过问卷弹窗
    const isNpsPopShowed = useCallback(npsStorage => {
        const currentDate = new Date(new Date().toLocaleDateString()).getTime();
        if (npsStorage && currentDate - npsStorage.timestamp < 24 * 60 * 60 * 1000) {
            return true;
        }
        return false;
    }, []);

    /**
     * 初始化挽留弹窗逻辑（检查是否需要展示）
     */
    const initRetentionCheck = useCallback(
        (isNpsBack?: 0 | 1, backFunc?: (arg0?: boolean) => void) => {
            try {
                const npsStorage = getNpsStorageInfoSync({
                    triggerType: 1,
                    appraiseIdArr: npsConfig?.showExitSurveyOnBack
                });

                initNpsPopupCheck(npsStorage);
                const lastTimeStamp = getStorageSync(storageKey);
                const currentDate = new Date(new Date().toLocaleDateString()).getTime();
                if (!lastTimeStamp || lastTimeStamp < currentDate) {
                    if (isShowBotEntranceCard === '1' && !isNpsPopShowed(npsStorage)) {
                        // 展示问卷弹窗小于24小时，阻止返回
                        setBackDetain(true);
                        setStorageSync(storageKey, new Date().getTime());
                        backFunc?.(isNpsBack === 1);
                        return;
                    }
                }

                backFunc?.();
            } catch (err) {
                console.error('存储操作失败:', err);
                backFunc?.();
            }
        },
        [
            npsConfig?.showExitSurveyOnBack,
            initNpsPopupCheck,
            storageKey,
            isShowBotEntranceCard,
            isNpsPopShowed
        ]
    );

    /**
     * 关闭nps调研弹窗
     */
    const closeNpsPopup = useCallback(() => {
        setShowNpsPop(false);
        setBackDetain(false);
    }, []);

    const handleNoNps = useCallback(
        (eventName, navigateBack, navigateHome) => {
            hideToast();
            closeNpsPopup();
            initRetentionCheck(1, (isShowDialog?: boolean) => {
                if (isShowDialog) {
                    setShowDialog(true);
                    return;
                }

                const naviFunc = {
                    navigateBack,
                    navigateHome
                };
                naviFunc[eventName || 'navigateBack']?.();
            });
        },
        [closeNpsPopup, initRetentionCheck]
    );

    /**
     * 处理返回时的挽留逻辑
     */
    const onBackDetain = useCallback(
        async (eventName: string, navigateBack: () => void, navigateHome: () => void) => {
            // 判断nps是否存在
            const npsStorage = getNpsStorageInfoSync({
                triggerType: 1,
                appraiseIdArr: npsConfig?.showExitSurveyOnBack
            });

            setBackState(eventName);
            setNaviFunc({navigateBack, navigateHome});

            if (!npsConfig?.showExitSurveyOnBack?.length) {
                setShowDialog(true);
                return;
            }

            if (!npsStorage || npsStorage?.availableAppraiseIds?.length) {
                const appraiseIdsParams =
                    npsStorage?.availableAppraiseIds || npsConfig?.showExitSurveyOnBack || [];
                const availableAppraiseIds = appraiseIdsParams
                    ?.map(item => item?.appraiseId)
                    ?.join(',');
                try {
                    const [err, data] = await getNpsInfo({
                        triggerType: 1,
                        sessionId: curSessionId,
                        appraiseIds: availableAppraiseIds || ''
                    });
                    hideToast();

                    if (!err && data?.data?.hasNps === 1) {
                        setShowNpsPop(true);
                        setPopData(data?.data);

                        setNpsStorageSync({
                            triggerType: 1,
                            curAppraiseId: data?.data?.appraiseId
                        });
                    } else {
                        handleNoNps(eventName, navigateBack, navigateHome);
                    }
                } catch (err) {
                    handleNoNps(eventName, navigateBack, navigateHome);
                }
            }
        },
        [curSessionId, handleNoNps, npsConfig?.showExitSurveyOnBack]
    );

    /**
     * 关闭挽留弹窗
     * @param isStay 是否停留在当前页面
     */
    const closeDialog = useCallback(
        (isStay: boolean) => {
            setShowDialog(false);
            setBackDetain(false);

            if (!isStay && typeof naviFunc[backState] === 'function') {
                setTimeout(() => {
                    naviFunc[backState]?.();
                }, 300);
            }
        },
        [backState, naviFunc]
    );

    return {
        showDialog,
        showNpsPop,
        backDetain,
        npsPopData: popData,
        onBackDetain,
        closeDialog,
        closeNpsPopup,
        initRetentionCheck
    };
};
