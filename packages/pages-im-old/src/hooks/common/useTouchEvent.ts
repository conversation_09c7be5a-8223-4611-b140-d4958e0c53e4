import {useRef, MouseEvent} from 'react';
import {ITouchEvent} from '@tarojs/components';
import Taro from '@tarojs/taro';

export type UseTouchesOptions = {
    onTouchStart?: (e: MouseTouchEvent) => void;
    onTouchMove?: (e: MouseTouchEvent, touchState?: TouchState) => void;
    onTouchEnd?: (e: MouseTouchEvent) => void;
};

export type UseTouchesParams = {

    /** 都阻止 */
    isStopEvent?: boolean;

    /** 是否阻止事件冒泡 */
    isStopPropagation?: boolean;

    /** 是否阻止事件默认行为 */
    isPreventDefault?: boolean;

    /** 是否禁用事件 */
    isDisable?: {

        /** 禁用所有事件 */
        all?: boolean;
        onTouchStart?: boolean;
        onTouchMove?: boolean;
        onTouchEnd?: boolean;
    };
} & IsTouchEvent;
type IsTouchEvent = {

    /** 是否需要监听 onMouseUp 注意：会导致 onTouchEnd 触发两次 */
    isOnMouseUp?: boolean;

    /** 是否需要监听 OnTouchCancel 注意：会导致 onTouchEnd 触发两次 */
    isOnTouchCancel?: boolean;
};
export type UseTouchEventParams = UseTouchesOptions & UseTouchesParams;

/** 绑定手指触摸或鼠标事件 */
export default function useTouchEvent(options: UseTouchEventParams = {}) {
    const touch = useTouch();
    const optionsRef = useLatest(options);

    const onTouchStart = (e: MouseTouchEvent) => {
        if (options.isDisable?.all || options.isDisable?.onTouchStart) return;
        onStopEvent(e);
        touch.start(e);
        if (!isMobile()) {
            document.addEventListener('mousemove', onTouchMove, true);
            document.addEventListener('mouseup', onTouchEnd, true);
        }
        optionsRef.current.onTouchStart?.(e);
    };
    const onTouchMove = (e: MouseTouchEvent) => {
        if (options.isDisable?.all || options.isDisable?.onTouchMove) return;
        onStopEvent(e);
        touch.move(e);
        optionsRef.current.onTouchMove?.(e, touch.info);
    };
    const onTouchEnd = (e: MouseTouchEvent) => {
        if (options.isDisable?.all || options.isDisable?.onTouchEnd) return;
        onStopEvent(e);
        touch.move(e);
        if (!isMobile()) {
            document.removeEventListener('mousemove', onTouchMove, true);
            document.removeEventListener('mouseup', onTouchEnd, true);
        }
        optionsRef.current.onTouchEnd?.(e);
    };
    const onStopEvent = (e: MouseTouchEvent) => {
        if (options.isStopEvent || options.isStopPropagation) {
            e.stopPropagation();
        }
        if (options.isStopEvent || options.isPreventDefault) {
            e.preventDefault();
        }
    };

    return {
        ...touch,
        onTouchFn: onTouchMouse({
            onTouchStart,
            onTouchMove,
            onTouchEnd,
            isOnMouseUp: options.isOnMouseUp,
            isOnTouchCancel: options.isOnTouchCancel
        })
    };
}

/** 处理鼠标或手指触摸事件 */
export const onTouchMouse = ({
    onTouchStart,
    onTouchMove,
    onTouchEnd,
    isOnMouseUp,
    isOnTouchCancel
}: UseTouchesOptions & IsTouchEvent) => {
    if (!isMobile()) {
        return {
            onMouseDown: onTouchStart,
            ...(isOnMouseUp ? {onMouseUp: onTouchEnd} : null)
        };
    }

    return {
        onTouchStart,
        onTouchMove,
        onTouchEnd,
        ...(isOnTouchCancel ? {onTouchCancel: onTouchEnd} : null)
    };
};

/** --- useLatest --- */
function useLatest<T>(value: T) {
    const ref = useRef(value);
    ref.current = value;

    return ref;
}

/** --- useTouch --- */
const MIN_DISTANCE = 10;

export type TouchDirection = '' | 'top' | 'bottom' | 'left' | 'right';
export type TouchState = {

    /** x的起始的位置 */
    startX: number;

    /** y的起始的位置 */
    startY: number;

    /** x的偏移量 */
    deltaX: number;

    /** y的偏移量 */
    deltaY: number;

    /** x的位移 正数 */
    offsetX: number;

    /** y的位移 正数 */
    offsetY: number;

    /** 当前移动的方向 */
    direction: TouchDirection;

    /** 触摸开始到结束的时间 */
    time: number;

    /** 起始的由始至终的x值 */
    preX: number;

    /** 起始的由始至终的y值 */
    preY: number;

    /** 由始至终的x值 */
    x: number;

    /** 由始至终的y值 */
    y: number;
};

function getDirection(x: number, y: number) {
    const _absX = Math.abs(x);
    const _absY = Math.abs(y);
    if (_absX > _absY && _absX > MIN_DISTANCE) {
        return x > 0 ? 'right' : 'left';
    }
    if (_absY > _absX && _absY > MIN_DISTANCE) {
        return y > 0 ? 'bottom' : 'top';
    }

    return '';
}
const useTouch = () => {
    const state = useRef<TouchState>({
        preX: 0,
        preY: 0,
        x: 0,
        y: 0,
        startX: 0,
        startY: 0,
        deltaX: 0,
        deltaY: 0,
        offsetX: 0,
        offsetY: 0,
        direction: '',
        time: 0
    });

    /** 触摸开始时间 */
    const startTime = useRef(0);

    const setState = (options: Partial<TouchState>) => {
        Object.keys(options).forEach(key => {
            state.current[key] = options[key];
        });
    };

    const reset = () => {
        setState({
            deltaX: 0,
            deltaY: 0,
            offsetX: 0,
            offsetY: 0,
            direction: ''
        });
    };

    const changeEvent = (event: ITouchEvent | MouseTouchE) => {
        // changedTouches 是 touchEnd 的值
        return (
            (event as ITouchEvent)?.touches?.[0]
            ?? (event as ITouchEvent)?.changedTouches?.[0]
            ?? (event as MouseTouchE)
        );
    };

    const start = (event: ITouchEvent | MouseTouchE) => {
        reset();
        const touch = changeEvent(event);
        setState({
            startX: touch.clientX,
            startY: touch.clientY,
            preX: state.current.x,
            preY: state.current.y
        });
        startTime.current = Date.now();
    };

    const move = (event: ITouchEvent | MouseTouchE) => {
        const touch = changeEvent(event);
        // Fix: Safari back will set clientX to negative number
        const {preX, preY, startX, startY, direction} = state.current;
        const deltaX = touch.clientX < 0 ? 0 : touch.clientX - startX;
        const deltaY = touch.clientY - startY;
        const offsetX = deltaX;
        const offsetY = deltaY;
        const time = Date.now() - startTime.current;

        setState({
            x: preX + deltaX,
            y: preY + deltaY,
            deltaX,
            deltaY,
            offsetX,
            offsetY,
            time,
            direction: !direction ? getDirection(offsetX, offsetY) : direction
        });
    };

    return {
        info: state.current,
        move,
        start,
        reset
    };
};

/** --- 一些其他的函数和类型 --- */
/** 判断是移动端还是pc端 */
export const isMobile = () => {
    // h5
    if (Taro.getEnv() === Taro.ENV_TYPE.WEB) {
        return navigator.userAgent.match(
            // eslint-disable-next-line max-len
            /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
        );
    }

    return true;
};

/** 鼠标事件 */
export type MouseEventType = MouseEvent<HTMLDivElement, globalThis.MouseEvent> | globalThis.MouseEvent;

/** 鼠标或手指事件 */
export type MouseTouchEvent = MouseEventType | ITouchEvent;

/** 处理鼠标或手指的事件 */
export const handleMouseOfTouch = (e: MouseTouchEvent) => {
    const target = !isMobile()
        ? (e as MouseEventType)
        : (e as ITouchEvent).touches[0] || (e as ITouchEvent).changedTouches[0];

    return {
        pageX: target.pageX,
        pageY: target.pageY,
        clientX: target.clientX,
        clientY: target.clientY,
        screenX: target.screenX,
        screenY: target.screenY
    };
};

/** 返回的类型 */
export type MouseTouchE = {
    pageX: number;
    pageY: number;
    clientX: number;
    clientY: number;
    screenX: number;
    screenY: number;
};
