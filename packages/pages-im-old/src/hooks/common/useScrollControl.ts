import {eventCenter} from '@tarojs/taro';
import {useCallback} from 'react';

/**
 * 滚动控制事件类型
 */
export type ScrollControlEvent = {
    /** 是否启用滚动 */
    enabled?: boolean;
    /** 消息ID */
    msgId?: string;
    /** 标识符 用于debug */
    symbol?: string;
};

/**
 * 滚动控制Hook
 * 提供统一的滚动控制接口
 * @param pageControl 页面控制标识，默认为'triageStreamScrollControl' 定向为'docImScrollControl'
 */
export const useScrollControl = (pageControl = 'triageStreamScrollControl') => {
    /**
     * 统一的滚动控制方法
     * @param options 滚动控制选项
     */
    const controlScroll = useCallback(
        (options: ScrollControlEvent) => {
            eventCenter.trigger(pageControl, options);
        },
        [pageControl]
    );

    /**
     * 监听滚动事件
     * @param callback 滚动事件回调
     */
    const onScroll = useCallback(
        (callback: (event: ScrollControlEvent) => void) => {
            const listener = (event: ScrollControlEvent) => {
                callback(event);
            };
            eventCenter.on(pageControl, listener);
            return () => {
                eventCenter.off(pageControl, listener);
            };
        },
        [pageControl]
    );

    /**
     * 滚动到底部
     * @param symbol 标识符，可选
     */
    const scrollToBottom = useCallback(
        (symbol?: string) => {
            controlScroll({
                enabled: true,
                symbol
            });
        },
        [controlScroll]
    );

    /**
     * 滚动到指定消息
     * @param msgId 消息ID
     * @param symbol 标识符，可选
     */
    const scrollToMessage = useCallback(
        (msgId: string, symbol?: string) => {
            controlScroll({
                enabled: true,
                msgId: `msg-item-${msgId}`,
                symbol
            });
        },
        [controlScroll]
    );

    /**
     * 滚动到指定元素
     * @param elementId 元素ID
     * @param symbol 标识符，可选
     */
    const scrollToElement = useCallback(
        (elementId: string, symbol?: string) => {
            controlScroll({
                enabled: true,
                msgId: elementId,
                symbol
            });
        },
        [controlScroll]
    );

    /**
     * 禁用滚动
     */
    const disableScroll = useCallback(
        (symbol?: string) => {
            controlScroll({
                enabled: false,
                symbol
            });
        },
        [controlScroll]
    );

    return {
        controlScroll,
        scrollToBottom,
        scrollToMessage,
        scrollToElement,
        disableScroll,
        onScroll
    };
};
