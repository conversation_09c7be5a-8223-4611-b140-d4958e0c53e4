/**
 * @description 管理 bdfile 本地资源地址与实际可访问地址的映射关系。

 * 用例场景：
 * - 将上传后的本地临时地址（如 bdfile://xxx）映射为真实的资源 URL
 * - case: https://console.cloud.baidu-int.com/devops/icafe/issue/tyxj-26203/show?from=page&source=icode-commit-message-owner
 */

import {atom} from 'jotai';

import {triageStreamAtomStore} from '../triageStreamAtom'; // 统一 store

export interface IBdFileMap {
    [bdFileUrl: string]: string; // key：bdfile 本地地址，value：真实 URL
}

// bdFile → 真实地址 映射 Atom
export const bdFileMapAtom = atom<IBdFileMap>({});
bdFileMapAtom.debugLabel = 'bdFileMapAtom';

/**
 * 设置单个 bdfile 映射
 *
 * @param key - 本地 bdfile 地址
 * @param url - 实际地址
 */
export const setBdFileMapAtom = (map: Record<string, string>) => {
    const state = triageStreamAtomStore.get(bdFileMapAtom);

    const merged: IBdFileMap = {
        ...state,
        ...map
    };

    triageStreamAtomStore.set(bdFileMapAtom, merged);

    return bdFileMapAtom;
};

/**
 * 获取 bdfile 对应的真实地址
 *
 * @param key - 本地地址
 * @returns string | undefined
 */
export const getBdFileMapAtom = () => {
    return triageStreamAtomStore.get(bdFileMapAtom);
};
