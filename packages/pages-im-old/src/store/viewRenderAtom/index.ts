import type {Atom} from 'jotai';
import {atomWithReset} from 'jotai/utils';

import type {InteractionType, ICardProps, InteractionInfo} from '../../typings';

export interface ViewRenderModalStore {
    modalState: 0 | 1;
    interaction: InteractionType;
    interactionInfo: InteractionInfo;
    cardData: ICardProps<unknown>;
    msgData?: any; // TODO: 需要定义类型，待优化；@wanghaoyu08
}

export const defaultModalState: ViewRenderModalStore = {
    interaction: '',
    interactionInfo: {},
    modalState: 0,
    cardData: {
        cardId: ''
    },
    msgData: {}
} as unknown as ViewRenderModalStore;

export const modalAtom = atomWithReset<ViewRenderModalStore>(defaultModalState);
type JotaiStore = {
    get: <Value>(atom: Atom<Value>) => Value;
    set: <Value>(atom: Atom<Value>, value: Value) => void;
};

/**
 * @description 初始化 modalAtom 到指定的 store
 */
export const initializeModalAtom = (store: JotaiStore) => {
    store.set(modalAtom, store.get(modalAtom));
};

/**
 * @description 重置 modalAtom 到默认状态
 */
export const resetModalAtom = (store: JotaiStore) => {
    store.set(modalAtom, defaultModalState);
};
