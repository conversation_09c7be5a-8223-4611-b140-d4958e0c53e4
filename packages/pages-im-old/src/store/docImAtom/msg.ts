import {atom} from 'jotai';
import {atomFamily, atomWithReducer} from 'jotai/utils';

import type {MsgId} from '../../typings';
import type {CreateConversationArgs} from '../../hooks/docIm/index.d';

import {docImMsgAtomReducer} from './msgAtomReducer';
import type {MsgItemType, MsgAtomEventAction, TriageStreamMsgAtomSymbolType} from './index.type';

import {docImAtomStore} from './index';

type CurSessionMsgIdsReducerActionType = 'push' | 'unshift' | 'delete' | 'reset';
interface CurSessionMsgIdsReducerAction {
    type: CurSessionMsgIdsReducerActionType;
    payload: MsgId[];
}

/**
 * 消息ID数组的Reducer函数，用于处理当前会话的消息ID列表的变更。
 *
 * @param state 当前状态，即当前会话的消息ID数组。
 * @param action 包含操作类型和操作数据的动作对象。
 * @returns 返回更新后的消息ID数组。
 */
const curSessionMsgIdsReducer = (
    state: MsgId[],
    action: CurSessionMsgIdsReducerAction
): MsgId[] => {
    switch (action.type) {
        // Tips: 使用循环而非 Set 保证顺序；
        case 'push': {
            const existedSet = new Set(state);
            const nextState = [...state];

            for (const id of action.payload) {
                if (!existedSet.has(id)) {
                    existedSet.add(id);
                    nextState.push(id);
                }
            }

            return nextState;
        }
        case 'unshift': {
            const existedSet = new Set(state);
            const nextState = [...state];

            for (let i = action.payload.length - 1; i >= 0; i--) {
                const id = action.payload[i];
                if (!existedSet.has(id)) {
                    existedSet.add(id);
                    nextState.unshift(id);
                }
            }

            return nextState;
        }
        case 'delete': {
            const payloadSet = new Set(action.payload);

            return state.filter(id => !payloadSet.has(id));
        }

        case 'reset':
            return [];

        default:
            return state;
    }
};

// 当前会话的消息 ID 列表, 用于渲染卡片列表;（有序）
export const curSessionMsgIdsAtom = atomWithReducer<MsgId[], CurSessionMsgIdsReducerAction>(
    [],
    curSessionMsgIdsReducer
);
curSessionMsgIdsAtom.debugLabel = 'curSessionMsgIdsAtom';

// AI 诊前页面消息卡片 AtomFamily
export const docImMsgAtomFamily = atomFamily((_id: TriageStreamMsgAtomSymbolType) => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const atomInstance = atomWithReducer<MsgItemType<any> | undefined, MsgAtomEventAction>(
        // atom 初始状态
        undefined,
        // 对应 atom 的 Reducer 处理函数
        (state, action) => {
            if (!action) return state;

            switch (action.type) {
                case 'update': {
                    return action.payload;
                }
                // Tips: 更新消息数据的 data 字段，而非整个 data 对象；@wanghaoyu08
                case 'updateDataOfData': {
                    if (!state) return state;

                    return {
                        type: state?.type,
                        meta: state?.meta,
                        data: action.payload
                    };
                }
                // Tips: 处理流式消息动态追加内容逻辑；@wanghaoyu08
                case 'append':
                case 'end':
                    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                    // @ts-error
                    return state ? docImMsgAtomReducer(state, action) : action.payload;
                case 'endManually':
                    return state ? docImMsgAtomReducer(state, action) : undefined;
                case 'reset':
                    return undefined;
                default:
                    return state;
            }
        }
    );
    atomInstance.debugLabel = `docImMsgAtomFamily_${_id}`;

    return atomInstance;
});

/**
 * 创建用于处理分流消息的状态管理原子
 *
 * @param id 分流消息原子符号类型
 * @returns 返回一个处理分流消息的状态管理原子
 */
export const createTriageStreamMsgAtom = (
    id: TriageStreamMsgAtomSymbolType,
    data: MsgItemType<unknown>
) => {
    // Tips: 暂不考虑 atom 初始化时是否存在已有 atom 的情况，即便出现该情况也不会重复创建；
    const atomInstance = docImMsgAtomFamily(id);

    docImAtomStore.set(atomInstance, {
        type: 'append',
        payload: data
    });

    return atomInstance;
};

/**
 * 重置TriageStreamMsgAtom
 *
 * @param id TriageStreamMsgAtomSymbolType类型，标识需要重置的TriageStreamMsgAtom
 * @returns 重置后的TriageStreamMsgAtom实例
 */
export const resetTriageStreamMsgAtom = (id: TriageStreamMsgAtomSymbolType) => {
    const atomInstance = docImMsgAtomFamily(id);
    docImAtomStore.set(atomInstance, {
        type: 'reset',
        payload: null
    });

    return atomInstance;
};

/**
 * 结束分流消息原子函数
 *
 * @param id 分流消息原子符号类型
 * @returns 分流消息原子实例
 */
export const endTriageStreamMsgAtom = (id: TriageStreamMsgAtomSymbolType) => {
    const atomInstance = docImMsgAtomFamily(id);

    docImAtomStore.set(atomInstance, {
        type: 'endManually',
        payload: null
    });

    return atomInstance;
};

export const updateTriageStreamMsgAtom = (
    id: TriageStreamMsgAtomSymbolType,
    data: MsgItemType<unknown>,
    options?: {
        _debugSymbol: string;
        actionType?: 'update' | 'append' | 'end';
    }
) => {
    const atomInstance = docImMsgAtomFamily(id);

    const action = options?.actionType || data?.data?.action;

    docImAtomStore.set(atomInstance, {
        type: action,
        payload: data
    });

    return atomInstance;
};

/**
 * 更新当前会话消息ID的Atom
 *
 * @param ids 需要更新的消息ID数组
 * @param ops 包含操作类型的对象，类型为 CurSessionMsgIdsReducerActionType
 */
export const updateCurSessionMsgIdsAtom = (
    ids: MsgId[],
    ops: {type: CurSessionMsgIdsReducerActionType}
) => {
    docImAtomStore.set(curSessionMsgIdsAtom, {
        type: ops.type,
        payload: ids
    });
};

export const docImResendMsgArgAtom = atomFamily((id: MsgId) => {
    const resendMsgArgAtom = atom<CreateConversationArgs>();
    resendMsgArgAtom.debugLabel = `docImResendMsgArgAtom_${id}`;
    return resendMsgArgAtom;
});

/**
 * 创建用于存储会话重发消息参数的Atom
 *
 * @param id 消息ID
 * @param data 会话重发消息参数
 * @returns 返回一个用于存储会话重发消息参数的Atom
 */
export const createResendMsgArgAtom = (id: MsgId, data: CreateConversationArgs) => {
    // Tips: 暂不考虑 atom 初始化时是否存在已有 atom 的情况，即便出现该情况也不会重复创建；
    const atomInstance = docImResendMsgArgAtom(id);

    docImAtomStore.set(atomInstance, data);

    return atomInstance;
};

/**
 * 获取分流消息原子中指定 key 的值
 *
 * @param id 分流消息原子符号类型
 * @returns 分流消息原子数据
 */

export const getDocImMsgDataByKey = (id: TriageStreamMsgAtomSymbolType) => {
    const atomInstance = docImMsgAtomFamily(id);
    const currentState = docImAtomStore.get(atomInstance);

    return currentState;
};
