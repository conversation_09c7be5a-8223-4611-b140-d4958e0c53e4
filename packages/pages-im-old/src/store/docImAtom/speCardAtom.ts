/**
 * @file 服务卡片相关 atoms
 * <AUTHOR>
 */
import {atom} from 'jotai';
import type {MsgId} from '../../typings';

import {docImAtomStore, adjectiveDirectedSkuMsgIdAtom} from './index';

interface IUpdataAjectiveMsgParams {
    msgId: MsgId;
    type?: 'unshift' | 'push';
}

/**
 * 更新有效定向 SKU 消息 ID
 *
 * @param msgId - 消息 ID
 * @param type - 消息类型
 * @returns 返回更新后的 AI 推荐专家消息 ID
 */
export const updateAdjectiveDirectedSkuMsgIdAtom = ({
    msgId,
    type = 'push'
}: IUpdataAjectiveMsgParams) => {
    const arr = docImAtomStore.get(adjectiveDirectedSkuMsgIdAtom) || [];

    // 使用展开运算符创建新数组，确保引用变化
    const newArr = type === 'unshift' ? [msgId, ...arr] : [...arr, msgId];

    return docImAtomStore.set(adjectiveDirectedSkuMsgIdAtom, newArr);
};

/**
 * 获取有效定向 SKU 消息 ID
 *
 * @returns 返回有效定向 SKU 消息 ID数组
 */
export const getAdjectiveDirectedSkuMsgId = () => {
    return docImAtomStore.get(adjectiveDirectedSkuMsgIdAtom);
};

/**
 * 更新欢迎模块展示状态
 */
export const imWelcomeMouleDisplayStatusAtom = atom<boolean>(false);
imWelcomeMouleDisplayStatusAtom.debugLabel = 'imWelcomeMouleDisplayStatusAtom';

export const updateImWelcomeMouleDisplayStatus = (status: boolean) => {
    docImAtomStore.set(imWelcomeMouleDisplayStatusAtom, status);
};

export const imWelcomeMouleMsgIdsAtom = atom<MsgId[]>([]);
imWelcomeMouleMsgIdsAtom.debugLabel = 'imWelcomeMouleMsgIdsAtom';

export const updateImWelcomeMouleMsgIdsAtom = (msgId: MsgId, type: 'unshift' | 'push') => {
    const arr = [...(docImAtomStore.get(imWelcomeMouleMsgIdsAtom) || [])];
    if (type === 'unshift') {
        arr?.unshift(msgId);
    } else {
        arr?.push(msgId);
    }

    return docImAtomStore.set(imWelcomeMouleMsgIdsAtom, arr);
};

export const getImWelcomeMouleMsgIdsAtom = () => {
    return docImAtomStore.get(imWelcomeMouleMsgIdsAtom);
};

/**
 * 更新 AI 推荐未定向消息 ID
 */
export const adjectiveRecommendUnDirectMsgIdAtom = atom<MsgId[]>([]);
adjectiveRecommendUnDirectMsgIdAtom.debugLabel = 'adjectiveRecommendUnDirectMsgIdAtom';

export const adjectiveRecommendExpertMsgIdAtom = atom<MsgId[]>([]);
adjectiveRecommendExpertMsgIdAtom.debugLabel = 'adjectiveRecommendExpertMsgIdAtom';
