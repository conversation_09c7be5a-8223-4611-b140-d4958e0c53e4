import {MSG_CARDID_ENUM} from '../../constants/msg';

import type {InteractionType, MsgId} from '../../typings';

import {
    PosterRole,
    type SessionId,
    type ICardProps,
    type ActionInfo,
    type PosterRoleType,
    type InteractionInfo,
    type SearchReferences,
    type MsgItemType,
    type MsgMetaData,
    type AttributeStatus,
    type MsgInstanceData
} from '../../typings/msg.type';

export type {
    SessionId,
    MsgItemType,
    MsgMetaData,
    PosterRoleType,
    AttributeStatus,
    MsgInstanceData,
    SearchReferences
};
export {PosterRole};

export type MsgValueType = ICardProps<unknown> | undefined;

export type TriageStreamMsgAtomSymbolType = `${SessionId}_${MsgId}`;

export type MsgAtomEventAction =
    | {type: 'update'; payload: MsgItemType<unknown>}
    | {type: 'updateDataOfData'; payload: MsgItemType<unknown>['data']}
    | {type: 'append'; payload: MsgItemType<unknown>}
    | {type: 'end'; payload: MsgItemType<unknown>}
    | {type: 'endManually'; payload: null}
    | {type: 'reset'; payload: null};

export type TextareaActionType = {
    type: 'hold' | 'notHold';
    payload: boolean;
};

export interface WxLoginData {
    gotoWxLogin: number;
    gotoWxLoginAction: {
        interaction: InteractionType;
        interactionInfo: InteractionInfo;
    };
}

export interface ImFlowData {
    list: {
        isFinish: boolean;
        sectionId: string;
        content: string;
        type: 'markdown';
    }[];
    quickReply?: {
        content: string;
    }[];
}

type ContentListType<T extends string> = T extends 'img'
    ? {
          desc: string;
          small: string;
      }[]
    : string[];

type ContentItem<T extends 'text' | 'img' | 'title'> = {
    type: T;
    data: {
        value: string;
        list?: ContentListType<T>;
    };
};

export interface CapsulesToolsType {
    type: string;
    text: string;
    icon: string;
    needLogin?: boolean;
    // TODO: 通用 actionInfo 声明待收敛；@wanghaoyu08
    actionInfo: ActionInfo;
    instruction?: {
        title: string;
        content: (ContentItem<'text'> | ContentItem<'img'> | ContentItem<'title'>)[];
    };
    bubbleInfo?: {
        icon: string;
        text: string;
        showTime: number;
    };
}

export interface UserDataType {
    avatar: string;
    name: string;
    isLogin: boolean;
}

export interface InputDataType {
    type?: string;
    bottomTips?: string;
    loadingTips?: string;
    uploadImg?: number;
    uploadImgInstruction?: CapsulesToolsType['instruction'] | undefined;
}

export interface DataForUbcType {
    product_info: {
        sessionId?: SessionId;
        msgId?: string;
        lastMsgId?: string;
        rounds?: number;
        cardId?: string;
        showPosterRole?: PosterRoleType;
    };
}

export interface SSEResponseType {
    data: {
        toolData?: {
            capsules: {
                list: CapsulesToolsType[];
                md5: string;
            };
        };
        message?: MsgItemType<unknown>[];
        ctrlData?: {
            firstToken?: boolean;
        };
    };
    status: number;
}

export interface OngoingToastType {
    actionInfo: {
        value: string;
        disabled: boolean;
        interaction: InteractionType;
        interactionInfo: InteractionInfo;
    };
    hasOngoing: boolean;
}
export interface TitleInfoType {
    avatar: string;
    title: string;
    actionInfo: {
        interaction: InteractionType;
        interactionInfo: InteractionInfo;
    };
}

export interface ViewOrderInfoType {
    avatar: string;
    title: string;
    actionInfo: {
        interaction: InteractionType;
        interactionInfo: InteractionInfo;
    };
    entrys?: []; // TODO: 6月底冲刺项目后端viewOrderEntrys上线后可删除
}

export interface ViewOrderEntrysType {
    avatar: string;
    title: string;
    entrys: {
        value: string;
        disabled: boolean;
        interaction: InteractionType;
        interactionInfo: InteractionInfo;
    }[];
    actionInfo?: {
        // TODO: 6月底冲刺项目后端viewOrderEntrys上线后可删除
        interaction: InteractionType;
        interactionInfo: InteractionInfo;
    };
}
export interface TitleDataType {
    title: string;
}

export const HideLoadingMsg = {
    // MSG_CARDID_ENUM['ImImage'] : 'ImImage'
    [MSG_CARDID_ENUM['ImImage']]: 'ImImage'
} as const;

export interface StatusDataType {
    topTips?: {
        progress: unknown;
        orderGuideTip: {
            hasOngoing: boolean;
            ongoingToast?: OngoingToastType;
            titleInfo?: TitleInfoType;
            viewOrderInfo?: ViewOrderInfoType;
            viewOrderEntrys?: ViewOrderEntrysType;
        };
    };
    popTips?: {
        isShowBotEntranceCard: string;
    };
}

export interface GetMsgListTransParams {
    docCardAdded: number;
    flagIsCurrentTalkEnd: number;
    flagNewGeneratedSession: number;
    flagUserPreUseSessionID: number;
}
