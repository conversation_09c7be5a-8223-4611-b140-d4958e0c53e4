/**
 * @file globalData 全局缓存
 * <AUTHOR>
 */
// import Taro from '@tarojs/taro';

// eslint-disable-next-line
export enum PageNetworkStatus {
    /**
     * 初始状态
     */
    init = 1,

    /**
     * 加载中
     */
    loading = 2,

    /**
     * 加载失败
     */
    error = 3,

    /**
     * 加载成功
     */
    success = 4
}

const globalData = {
    /**
     * 接口请求Task
     */
    requestTask: {
        requestKey: null
    },

    /**
     * 日志
     */
    log: {
        lid: '',
        referlid: '',
        pd: '',
        applid: ''
    },

    /**
     * 性能统计
     */
    m: {
        navigateStart: 0,
        invokeStart: 0
    },

    /**
     * 实验SID值（需在业务端根据后端透传然后赋值）
     */
    sid: '',

    /**
     * 接口请求网络状态
     */
    network: {
        status: PageNetworkStatus.init
    },

    /**
     * App版本
     */
    appVersion: '',

    /**
     * 设备信息相关
     */
    deviceIds: {
        baidu_id: '',
        cuid: '',
        sid: ''
    }
};

// let app = Taro.getApp();
// app.globalData = globalData;

function set(key, val: unknown) {
    globalData[key] = val;

    return globalData;
}

function get(key) {
    if (Array.isArray(key)) {
        return key.map(__key => globalData[__key]);
    }

    return globalData[key];
}

export default {
    set,
    get
};

export {globalData};
