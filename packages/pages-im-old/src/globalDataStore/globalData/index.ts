import { GLOBAL_DATA } from './DEFAULT_DATA';

function set(key, val: unknown) {
    if (process.env.TARO_ENV === 'swan') {
        // swan 小程序使用getApp， 便于mars和taro globalData通信
        const app = getApp({
            allowDefault: true
        });
        if (app && app.globalData) {
            getApp({ allowDefault: true }).globalData[key] = val;
        }
    } else {
        // 微信和h5只需要管理本服务的globalData即可
        GLOBAL_DATA[key] = val;
    }
}

function get(key) {
    if (process.env.TARO_ENV === 'swan') {
        const app = getApp({
            allowDefault: true
        });
        if (app && app.globalData) {
            return getApp({ allowDefault: true }).globalData[key];
        }
    }

    return GLOBAL_DATA[key];
}

export default {
    set,
    get
};

export { GLOBAL_DATA };
