// 维护全局管理变量
const GLOBAL_DATA = {
    // 测试期间使用
    yuanzhentest: '',
    // 日志全局字段
    log: {
        lid: '',
        applid: '',
        referlid: '',
        pd: 'med'
    },
    m: {
        navigateStart: 0,
        invokeStart: 0
    },
    city: {
        realName: '',
        realCode: '',
        name: '', // 实际选择地址
        code: '', // 城市编号
        lat: '',
        lng: '',
        province: '',
        district: '',
        placeName: ''
    },
    // 带详细坐标的地址
    medDetailAddress: {
        realProvince: '',
        realName: '',
        realCode: '',
        name: '', // 实际选择地址
        code: '', // 城市编号
        lat: '',
        lng: '',
        province: '',
        district: '',
        placeName: '',
        contactId: '' // 收货人id
    },
    // 购药购物车数据
    gyTrolleys: [],
    // 购药去结算数据
    // 重构后
    gyToOrderConfirmSkus: [],
    appVersion: '2.0.0',
    // H5左上角房子屏蔽
    hdhome: false,
    // 登录改造兼容button方式
    canIUseLoginButton: false,
    // 实验SID值（需在业务端根据后端透传然后赋值）
    sid: '',
    sf_ref: '', // 外部来源
    bd_vid: '', // 用商-转化字段
    searchID: '',
    clk_loc: '',
    // H5是否支持webp
    supportWebp: null,
    // 全局event监听集合
    wz_eventListeners: {}
};

// if (process.env.TARO_ENV === 'h5') {
//     const whitePages = require('./WHITE_PAGES').default || [];
//     GLOBAL_DATA.whitePages = whitePages;
// }

export { GLOBAL_DATA };

export const SESSION_STROAGE = {};

export const CACHE__STROAGE = {
    city: {
        realName: '',
        realCode: '',
        name: '', // 实际选择地址
        code: '', // 城市编号
        lat: '',
        lng: '',
        province: '',
        district: '',
        placeName: ''
    },
    // 带详细坐标的地址
    medDetailAddress: {
        realProvince: '',
        realName: '',
        realCode: '',
        name: '', // 实际选择地址
        code: '', // 城市编号
        lat: '',
        lng: '',
        province: '',
        district: '',
        placeName: '',
        contactId: '' // 收货人id
    },
    // 购药购物车数据
    gyTrolleys: [],
    // 重构后
    gyToOrderConfirmSkus: []
};
