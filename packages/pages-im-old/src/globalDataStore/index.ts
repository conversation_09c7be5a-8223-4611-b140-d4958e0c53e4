import storage from './storage';
import globalData from './globalData/index';
import sessionStorage from './sessionStorage';

const STORE_TYPE_GLOBAL = 1;
const STORE_TYPE_LOCAL = 2;
const STORE_TYPE_SESSION = 3;

/**
 * 状态管理，设置状态key值
 * @param key 存储的key值
 * @param val 存储的value
 * @param storeType 存储类型， 包含
 * 1:globalData,
 * 2: localStorage,
 * 3: sessionStorage
 */
function set(key, val: unknown, storeType = [1, 2]) {
    // 1: 存储globalData
    if (storeType.indexOf(STORE_TYPE_GLOBAL) > -1) {
        globalData.set(key, val);
    }
    // 2: 存储storage
    if (storeType.indexOf(STORE_TYPE_LOCAL) > -1) {
        storage.set(key, val);
    }
    // 3: 存储sessionStorage
    if (storeType.indexOf(STORE_TYPE_SESSION) > -1 && process.env.TARO_ENV === 'h5') {
        sessionStorage.set(key, val);
    }
}

/**
 * 获取全局状态，获取到优先级高的值，直接返回，后续不做处理，优先级是：
 * 在小程序环境下：globalData -> storage
 * 在h5环境下：globalData -> sessionStorage -> storage
 *
 */
function get(key) {

    if (globalData.get(key)) {
        return globalData.get(key);
    }

    if (process.env.TARO_ENV === 'h5' && sessionStorage.get(key)) {
        return sessionStorage.get(key);
    }

    const store = storage.get(key);
    if (store) {
        return store;
    }

    return '';
}

export default {
    set,
    get,
    storage,
    globalData,
    sessionStorage
};
