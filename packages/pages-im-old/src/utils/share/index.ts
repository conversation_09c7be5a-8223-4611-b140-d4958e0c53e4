export const initShare = (shareInfo: IShareProps) => {
    document.getElementsByTagName('title')[0].innerText = (shareInfo && shareInfo.title) || getTitle();

    const ua = getUserAgent();
    if (ua.isWeixinApp()) {
        httpRequest<IShareConfigProps>({
            url: `${API_HOST}/wx-user/auth/authorization`,
            method: 'POST',
            header: {
                'Content-Type': 'application/json'
            },
            data: {
                wxAppKey: 'netHospitalService',
                url: window.location.href
            },
            isNeedLogin: false,
            isFirstScreen: false
        }).then(res => {
            const [err, resp] = res;
            if (!err) {
                const { timestamp, nonceStr, signature, appId } = resp?.data || {};
                const config = appId
                    ? {
                        wx: true,
                        debug: false,
                        timestamp,
                        nonceStr,
                        signature,
                        appId
                    }
                    : {};
                if (config && config.wx) {
                    shareWX(shareInfo, config);
                }
            }
        });
    } else if (ua.isBaiduBox() || process.env.TARO_ENV === 'h5') {
        // 同时适配手百和h5分享
        shareBdBox(shareInfo);
    }
};