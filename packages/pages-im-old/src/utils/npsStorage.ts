/**
 * @file 处理nps storage逻辑
 * <AUTHOR>
 */

import {getStorageSync, setStorageSync} from '@tarojs/taro';
import {TriggerType} from '../models/services/nps/getNpsInfo/index.d';
import type {ExitSurveyOnBackItem} from '../store/triageStreamAtom/index.type';

// 获取 storage 参数
interface GetNpsStorageParams {
    storageKey?: string;
    triggerType?: TriggerType;
    appraiseIdArr?: ExitSurveyOnBackItem[];
}

// 设置 storage 参数
interface SetNpsStorageParams {
    storageKey?: string;
    createTime?: number;
    triggerType?: TriggerType;
    // npsConfig下发 triggerType对应的问卷id数组
    appraiseIds?: string[];
    // 本次需要更新的appraiseId
    curAppraiseId?: string;
    // 是否清除曝光次数
    isClearCount?: boolean;
}

interface AppraiseIdItem {
    id?: string;
    count?: number;
}

/**
 * 从存储数据中筛选可用的评价ID配置
 * @param configAppraiseIdArr 配置的评价ID数组
 * @param storageAppraiseIds 存储的评价ID数组
 * @returns 满足条件的评价ID配置数组
 */
const filterAvailableAppraiseIds = (
    configAppraiseIdArr: ExitSurveyOnBackItem[],
    storageAppraiseIds?: AppraiseIdItem[]
): ExitSurveyOnBackItem[] => {
    if (!storageAppraiseIds || !Array.isArray(storageAppraiseIds)) {
        return [...configAppraiseIdArr];
    }

    const appraiseMap = new Map(storageAppraiseIds.map(item => [item.id, item]));
    return configAppraiseIdArr.filter(config => {
        const item = appraiseMap.get(config.appraiseId);
        return item ? item?.count && item?.count < 3 : true;
    });
};
/**
 * 同步获取 NPS 调研信息
 *
 * @param args 参数对象
 * @param args.storageKey 存储键名，默认为 'npsFlag'
 * @param args.triggerType 触发类型
 * @param args.appraiseId 评估 ID
 * @returns NPS 调研信息或 null
 */
export const getNpsStorageInfoSync = (args: GetNpsStorageParams) => {
    const {storageKey = 'npsFlag', triggerType, appraiseIdArr} = args;
    try {
        const value = getStorageSync(storageKey);
        if (value) {
            // 返回创建时间
            // 返回当前appraiseId arr的曝光次数，存在多个问卷时，判断是否还有未曝光的appraiseId
            const data = JSON.parse(value);
            if (triggerType && appraiseIdArr) {
                const availableAppraiseIds = filterAvailableAppraiseIds(
                    appraiseIdArr,
                    data[`appraiseIds_${triggerType}`]
                );

                return {
                    ...data,
                    availableAppraiseIds
                };
            }

            return data;
        }
    } catch (error) {
        console.error('读取存储失败:', error);
        return null;
    }
};

/**
 * 同步设置 NPS 存储信息
 *
 * @param args 设置 NPS 存储信息的参数
 */
export const setNpsStorageSync = (args: SetNpsStorageParams): void => {
    const {storageKey = 'npsFlag', createTime, triggerType, curAppraiseId, isClearCount} = args;
    if (!triggerType || !curAppraiseId) {
        return;
    }

    // 读取旧存储值
    let oldStorageValue = getStorageSync(storageKey);
    if (oldStorageValue) {
        oldStorageValue = JSON.parse(oldStorageValue);
    }

    // 最新的appraiseId的曝光次数
    let newStorageAppraiseIds: AppraiseIdItem[] = [];

    const curAppraiseIdItem = oldStorageValue[`appraiseIds_${triggerType}`]?.find(
        item => item.id === curAppraiseId
    );
    if (!curAppraiseIdItem) {
        newStorageAppraiseIds.push({
            id: curAppraiseId,
            count: 1
        });
    } else {
        newStorageAppraiseIds = oldStorageValue[`appraiseIds_${triggerType}`]?.map(item => {
            const {id, count} = item || {};
            return {
                id,
                // 如果是当前appraiseId，如果是clearCount，则重置为1，否则+1
                count: id === curAppraiseId ? (isClearCount ? 1 : count + 1) : count
            };
        });
    }

    // 获取到之前的数据，更新对应id的曝光次数，并存储
    const result = {
        ...(oldStorageValue || {}),
        createTime: createTime || new Date().getTime(),
        ...(newStorageAppraiseIds?.length > 0
            ? {[`appraiseIds_${triggerType}`]: newStorageAppraiseIds}
            : {})
    };

    try {
        setStorageSync(`${storageKey}`, JSON.stringify(result));
    } catch (error) {
        console.error('存储操作失败:', error);
    }
};
