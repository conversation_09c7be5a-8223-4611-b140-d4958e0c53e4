interface ParseURLRes {
    pathname: string;
    params: {
        [k in string]: string;
    };
    paramsString: string;
}

/**
 *
 * @description url 解构
 * @param url
 * @returns
 */
export function parseURL(url: string): ParseURLRes {
    const match = url.match(/^((https?:\/\/[^/]+)\/)?([^?\s]*)(\?[^#\s]*)?(#[\s\S]*)?$/);

    if (match) {
        const [, , , path, query] = match;
        const params = {};
        let paramsString = query || '';

        if (paramsString) {
            paramsString = paramsString.substring(1);
            paramsString.split('&').forEach(param => {
                const [key, ...values] = param.split('='); // value 中可能不止一个 = 符号
                const value = values.join('='); // 将剩余的等号连接起来
                if (key) params[decodeURIComponent(key)] = decodeURIComponent(value || '');
            });
        }

        return {
            pathname: path,
            params,
            paramsString
        };
    }
    throw new Error('parseURL 出错');
}

/**
 *
 * @description 组装 url
 * @param path
 * @param params
 * @returns
 */
export function assembleUrl(path: string, params: { [k in string]: string | number }): string {
    const queryString = Object.entries(params)
        .map(([key, value]) => `${key}=${value}`)
        .join('&');

    return `${path}?${queryString}`;
}
