interface FormatPriceOpsType {
    significant?: number;
    multiple?: number;
    // 组合卡业务需要，开启后，假如价格只有分，则不会四舍五入为0
    safeMode?: boolean;
}

/**
 *
 * @description 价格格式化
 * @param price {number} 价格
 * @param ops {FormatPriceOpsType} 格式化参数(significant 有效位数, multiple 除10的倍数, 是否开启安全模式)
 * @returns {number}
 */
export const formatPrice = (price: number, ops?: FormatPriceOpsType): string => {
    try {
        const {significant = 2, multiple = 2, safeMode = false} = ops || {};

        // 当真实价格不为0且开启安全模式
        if (safeMode && price !== 0) {
            const divisor = Math.pow(10, multiple);
            const realPrice = price / divisor;

            // 假如被四舍五入为0，则向后寻找第一个有效小数位
            if (Number(realPrice.toFixed(significant)) === 0) {
                for (let i = significant + 1; i <= multiple; i++) {
                    const tmp = realPrice.toFixed(i);
                    if (Number(tmp) !== 0) {
                        return tmp;
                    }
                }
                return realPrice.toFixed(multiple);
            }
        }

        const isDivisible = price % Math.pow(10, multiple) === 0;

        if (isDivisible && significant) {
            return String(price / Math.pow(10, multiple));
        }
        const res = (price / Math.pow(10, multiple)).toFixed(significant);

        if (res.endsWith('0')) {
            return res.slice(0, -1);
        }

        return res;
    } catch (err) {
        console.error('formatPrice 执行出错:', err);

        return String(price);
    }
};
