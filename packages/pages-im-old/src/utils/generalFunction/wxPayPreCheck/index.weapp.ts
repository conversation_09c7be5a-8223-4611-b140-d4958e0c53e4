import { checkWxAccountBindingStatusBeforePayment } from '@common/utils/generalFunction/loginApi';

const { WX_APP_ENV } = process.env;

const overTimePromise = (): Promise<null> => {
    return new Promise(res => {
        setTimeout(() => {
            res(null);
        }, 8000);
    });
};

/**
 * 预检查支付接口是否可用
 *
 * @returns 返回一个Promise对象，当预检查成功时，resolve(null)，否则reject(err)
 */
const preCheckPromise = (): Promise<null> => {
    return new Promise(async (resolve, reject) => {
        try {
            if (!WX_APP_ENV) {
                reject('wxPayPreCheck 出错：WX_APP_ENV 不存在');

                return;
            }
            await checkWxAccountBindingStatusBeforePayment();

            resolve(null);
        } catch (err) {
            console.error('wxPayPreCheck 出错：', err);
            reject(err);
        }
    });
};

/**
 *
 * @description 支付前预校验微信账号绑定关系(check 时间超过 8s 直接跳过)
 * @returns
 */
export const wxPayPreCheck = (): Promise<null> => {
    return Promise.race([overTimePromise(), preCheckPromise()]);
};
