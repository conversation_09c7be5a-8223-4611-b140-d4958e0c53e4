import xaf from '@baidu/antifraud-xaf3';
import {isIOS} from '@baidu/health-utils';
import antifraud from '@baidu/antifraud-swan';
import searchFeUa from '@searchfe/user-agent';

import {getSystemInfo} from '../../taro/get_system_info/index';

import store from '../../../globalDataStore/index';
import {cookie4h5 as cookie} from '../../basicAbility/cookie';

import {GetFkParamsArgs, FkParamsVal} from './index.d';

export * from './index.d';

// 全局cookie key值
const COOKIE_KEY: {
    [k in string]: string;
} = (array => array.reduce((obj, cur) => ((obj[cur] = cur), obj), {}))([
    'BAIDUID',
    'IS_OPPO_WEBVIEW'
]);

/**
 *
 * @description h5获取基础参数 zid 依赖百度端能力 我们很大可能是获取不到的；（有可能获取不到所以没有 reject 始终 resolve）
 * @return {string}
 */
const getZid = ({getConf, baiduID}): Promise<string> => {
    return new Promise(resolve => {
        try {
            // APP内获取系统类型
            const app = store.get('isInApp') ? (isIOS() ? 'ios' : 'android') : '';

            const extParams = {
                aid: getConf.ak,
                ev: getConf.ev,
                a: baiduID,
                ...(app ? {app} : {})
            };

            xaf.hgzAs(extParams, v => {
                resolve(v);
            });
        } catch (error) {
            resolve('');
        }
    });
};

/**
 *
 * @description h5获取基础参数 jt 不依赖端能力 h5主要靠这个来防护
 * @return {Object}
 */
const getJt = ({getConf, baiduID}): Promise<string> => {
    return new Promise(resolve => {
        try {
            const extParams = {
                a: baiduID,
                aid: getConf.ak,
                biz: {
                    ev: getConf.ev,
                    baiduID
                },
                complete(res: {code: number; jt: string}) {
                    if (+res.code === 0) {
                        resolve(res.jt);
                    } else {
                        resolve('');
                    }
                }
            };

            xaf.report(extParams);
        } catch (error) {
            resolve('');
        }
    });
};

/**
 * 获取风控参数 (h5) 参考 http://wiki.baidu.com/pages/viewpage.action?pageId=357373756&preview=/357373756/1487995105/%E9%A3%8E%E6%8E%A7%20JS%20SDK%20%E4%BD%BF%E7%94%A8%E6%8C%87%E5%8D%97.pdf
 *
 * @param {Object} userConf 透传数据
 * userConf.aid 渠道号（安全实验室分配app key）
 * userConf.dataApp
 * userConf.ev 用户动作
 * @return {Object} Promise对象，获取风控参数
 */
export const getFkParamsAll = (userConf: GetFkParamsArgs): Promise<FkParamsVal> => {
    // eslint-disable-next-line no-async-promise-executor
    return new Promise(async (resolve, reject) => {
        try {
            const getConf = Object.assign(
                {
                    ev: 'cash_out'
                },
                userConf
            );

            let app = 'universe';
            // 百度健康app内 || 端内产品
            if (store.get('isInApp') || searchFeUa?.isBaiduboxOrBdapp?.()) {
                app = isIOS() ? 'ios' : 'android';
            }
            const obj = antifraud.getData();
            const baiduID = cookie(COOKIE_KEY.BAIDUID);
            xaf.init({
                aid: getConf.ak, // aid 即活动id，由昊天镜平台分配
                dataApp: getConf.dataApp
            });

            const jt = await getJt({getConf, baiduID});
            const zid = await getZid({getConf, baiduID});
            const {platform, system, model, brand, screenWidth, screenHeight} = getSystemInfo();

            resolve({
                aid: getConf.ak,
                z: zid,
                jt,
                ev: getConf && getConf.ev,
                to: obj && obj.Token,
                view: obj && obj.view,
                os: platform === 'ios' ? 1 : platform === 'android' ? 2 : 0,
                os_version: system,
                model,
                app,
                zid,
                mode: 2,
                brand: brand || model,
                reso: `'${screenWidth}x${screenHeight}'` || ''
            });
        } catch (err) {
            console.error('getFkParamsAll 出错：', err);
            reject(err);
        }
    });
};
