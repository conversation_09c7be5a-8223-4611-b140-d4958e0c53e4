import antifraud from '@baidu/antifraud-swan';

import { getSystemInfo } from '../../taro';

import store from '../../../globalDataStore/index';

import { GetFkParamsArgs, SwanFkInfo, CommonSysInfo, SystemInfo, FkParamsVal } from './index.d';

export * from './index.d';

/**
 * 获取基础参数
 *
 * @return {Object}
 */
const getBasicInfo = (): Promise<{info: CommonSysInfo; sys: SystemInfo}> => {
    return new Promise((resolve, reject) => {
        try {
            const sys = getSystemInfo();

            swan.getCommonSysInfo({
                success: info => {
                    resolve({
                        info: (info as CommonSysInfo),
                        sys: (sys as SystemInfo)
                    });
                },
                fail: err => {
                    reject(err);
                }
            });
        } catch (err) {
            reject(err);
        }
    });
};


/**
 * 获取核心数据
 *
 * @param {GetFkParamsArgs} userConf 后端透传数据
 * @return {Object}
 */
const getFkInfo = (userConf: GetFkParamsArgs): Promise<SwanFkInfo> => {
    return new Promise(async (resolve, reject) => {
        try {
            const { info, sys } = await getBasicInfo();
            const params = {
                c: info.cuid,
                aid: userConf.ak,
                ak: userConf.ak,
                appid: 3,
                app: sys.platform,
                ver: sys.version,
                vc: '',
                zidKey: info.zid,
                dataApp: userConf.dataApp,
                sc: 'marketing_activity'
            };
            antifraud.init(params);
            antifraud.hgzAs(
                {
                    o: userConf.o,
                    ev: '1'
                },
                zid => {
                    resolve({ zid, info, sys });
                }
            );
        } catch (err) {
            console.error('getFkInfo 出错：', err);
            reject(err);
        }
    });
};

/**
 * 获取风控参数 (小程序)
 *
 * @param {Object} userConf 后端透传数据
 * userConf.ak 渠道号（安全实验室分配app key）
 * userConf.dataApp
 * userConf.o 活动场景值
 * @return {Object} Promise对象，获取风控参数
 */
export const getFkParamsAll = (userConf: GetFkParamsArgs): Promise<FkParamsVal> => {
    return new Promise(async (resolve, reject) => {
        try {
            const { zid, info, sys } = await getFkInfo(userConf);
            antifraud.getDataV2(data => {
                resolve({
                    aid: userConf.ak,
                    caller: userConf.ak,
                    c: info.cuid,
                    i: info.imei,
                    z: zid,
                    app: sys.platform,
                    ver: sys.version,
                    model: sys.model,
                    brand: sys.brand,
                    ev: userConf.ev || 'cash_out',
                    to: data && data.Token,
                    vw: data && data.view,
                    view: data && data.view,
                    os_version: sys.system,
                    mode: 1,
                    zid,
                    v: store.get('appVersion') || '',
                    reso: `'${sys.screenWidth}x${sys.screenHeight}'` || '',
                    os: sys.platform === 'ios' ? 1 : sys.platform === 'android' ? 2 : 0
                });
            });
        } catch (err) {
            console.error('getFkParamsAll 出错：', err);
            reject(err);
        }
    });
};
