import xaf from '@baidu/xaf-we';
import { getCurrentPages } from '@tarojs/taro';

import { getSystemInfo } from '../../taro';
import { GetFkParamsArgs, FkParamsVal } from './index.d';

const systemInfo = getSystemInfo();

/**
 * 获取风控参数 (微信小程序)
 *
 * @param {Object} userConf 后端透传数据
 * userConf.ak 渠道号（安全实验室分配app key）
 * userConf.dataApp
 * userConf.o 活动场景值
 * @return {Object} Promise对象，获取风控参数
 */
export const getFkParamsAll = (userConf: GetFkParamsArgs): Promise<FkParamsVal> => {
    return new Promise(async (resolve, reject) => {
        try {
            // 初始化
            const currentPages = getCurrentPages();
            if (!currentPages?.length) {
                reject();
            }
            const pages = [...currentPages].pop()?.route;

            xaf.init({
                aid: userConf.ak,
                a: '', // passid或者其他业务方用户id
                o: '', // openid
                u: '', // unionid
                pages
            });

            let ev = userConf.ev || 'cash_out';
            if (ev && !ev.startsWith('wx_')) {
                ev = `wx_${ev}`;
            }

            const data = await xaf.report();
            resolve({
                aid: userConf.ak,
                app: 'universe',
                js_env: 'wx',
                jt: data.jt,
                ev,
                model: systemInfo.model,
                brand: systemInfo.brand,
                ver: systemInfo.version,
                reso: `'${systemInfo.screenWidth}x${systemInfo.screenHeight}'` || ''
            });
        } catch (err) {
            console.error('wx getFkParamsAll 出错：', err);
            reject(err);
        }
    });
};
