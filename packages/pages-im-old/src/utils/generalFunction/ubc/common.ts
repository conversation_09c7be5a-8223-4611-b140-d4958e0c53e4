/**
 *
 * @description 上传页面资源ID
 * @doc https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/5PohCU7HAz/KN10n10fVZ/BYxihdm-ut0Xge
 * @param params
 */

export const getPageID = (storeData, path) => {
    const { qid, loId } = storeData;

    const pathAndPageIDMap = {
        'pages/triage/index': {
            key: 'qid',
            value: qid
        },
        'pages/chat/index': {
            key: 'loId',
            value: loId
        }
    };

    let resVal = {};
    Object.keys(pathAndPageIDMap).map(item => {
        if (path?.includes(item)) {
            resVal = {
                page_source_key: pathAndPageIDMap[item].key,
                page_source_id: pathAndPageIDMap[item].value
            };
        }
    });

    return resVal;
};
