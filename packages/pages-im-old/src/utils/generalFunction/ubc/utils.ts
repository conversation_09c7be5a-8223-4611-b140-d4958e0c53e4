import {getCurrentPage} from '@baidu/vita-utils-shared';

// 新增路径映射，用于处理特殊场景的页面路径覆盖问题，例如『医生分身』服务迁移兼容；@wanghaoyu08
export const NEED_COVER_PATH_MAP = {
    'vita/pages/docIm/index': 'wenzhen/pages/triageStream/index' // 用于处理『医生分身』服务迁移兼容；@wanghaoyu08
};

// 针对特殊场景对 pages 进行映射；
export const getUbcPageVal = () => {
    const page = getCurrentPage();

    return {
        ...page,
        route: NEED_COVER_PATH_MAP[page.route] || page.route
    };
};
