import {getCurrentPages} from '@tarojs/taro';
import {getCurrentPage, getAppInfo} from '@baidu/vita-utils-shared';

import {navigate} from '../../basicAbility/commonNavigate';
// eslint-disable-next-line no-duplicate-imports
import type {OpenType} from '../../basicAbility/commonNavigate';

import type {UserConfParams, swan} from './index.d';

const appInfo = getAppInfo();

/**
 * 百度小程序调起登录页
 */
export const loginApi: (userConf?: UserConfParams) => void = (userConf = {}) => {
    new Promise((resolve, reject) => {
        let canIUseLoginButton = false;
        canIUseLoginButton = swan.canIUse('button.open-type.login');
        if (canIUseLoginButton) {
            if (userConf.loginStatus) {
                resolve(null);
            } else {
                reject();
            }
        } else {
            swan.login()
                .then(d => resolve(d))
                .catch(e => reject(e));
        }
    })
        .then(() => {
            // 处理登录成功跳转地址
            const currentPage = getCurrentPage();
            const defHref = `/${currentPage?.path}`;
            // 处理登录成功跳转方式
            let defOpenType = '';
            if (currentPage.isOnlyPage) {
                defOpenType = 'relaunch';
            } else {
                defOpenType = 'redirect';
            }
            const url = userConf.href || defHref;
            navigate({
                url,
                openType: userConf.openType || (defOpenType as OpenType)
            });
        })
        .catch(() => {
            const pages = getCurrentPages();
            if (pages && pages.length - 1 > 0) {
                navigate({
                    url: '',
                    openType: 'navigateBack'
                });
            } else if (pages && pages.length === 1) {
                // eslint-disable-next-line prefer-destructuring
                const curPage = pages[0];
                const homePath = appInfo.home;
                const tabBarPathArr = appInfo.tabBar;
                try {
                    const {msg} = getCurrentPage().options;
                    const msgObj = msg ? JSON.parse(msg) : {};
                    const to = msgObj.to || '';
                    if (
                        (to && tabBarPathArr.some(item => to.indexOf(item) > -1)) ||
                        (curPage && tabBarPathArr.some(item => curPage.indexOf(item) > -1))
                    ) {
                        navigate({
                            url: homePath,
                            openType: 'relaunch'
                        });
                    }
                } catch (e) {
                    navigate({
                        url: homePath,
                        openType: 'relaunch'
                    });
                }
            }
        });
};

// 空实现
export const initPlugin = {};

// 空实现
export const bindWxWithPass = {};
