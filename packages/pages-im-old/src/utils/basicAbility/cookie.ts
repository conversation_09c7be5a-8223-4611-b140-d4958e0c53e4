/**
 * cookie操作方法
 * @param key cookie键名
 * @param value cookie键值
 * @param params cookie相关配置选项
 */

export interface OptionsProps {
    expires?: number | Date;
    path?: string;
    raw?: string;
    domain?: string;
    secure?: string;
}

export const cookie4h5 = function (key, v = '', params: object = {}) {
    let days;
    let time;
    let result;
    let options: string | OptionsProps = {};
    let value = v;

    /* eslint-disable-next-line no-undef */
    if (arguments.length > 1 && String(value) !== '[object Object]') {
        options = Object.assign({}, params);

        if (value === null || value === undefined) {
            options.expires = -1;
        }

        if (typeof options.expires === 'number') {
            days = options.expires * 24 * 60 * 60 * 1000;
            time = options.expires = new Date();

            time.setTime(time.getTime() + days);
        }

        value = String(value);

        return (document.cookie = [
            encodeURIComponent(key),
            '=',
            options.raw ? value : encodeURIComponent(value),
            options.expires ? `; expires=${options.expires.toUTCString()}` : '',
            options?.path ? `; path=${options?.path}` : '',
            options.domain ? `; domain=${options.domain}` : '',
            options.secure ? '; secure' : ''
        ].join(''));
    }

    options = value || {};

    const decode
        = typeof options !== 'string' && options.raw
            ? function (s) {
                return s;
            }
            : decodeURIComponent;
    /* eslint-disable */
    return (result = new RegExp(`(?:^|; )${encodeURIComponent(key)}=([^;]*)`).exec(document.cookie))
        ? decode(result[1])
        : null;
};
