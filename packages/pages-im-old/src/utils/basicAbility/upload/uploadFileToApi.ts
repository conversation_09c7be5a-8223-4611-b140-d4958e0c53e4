import {uploadPicToApiService} from '../../../models/services/common';
import {ubcCommonViewSend} from '../../../utils/generalFunction/ubc';

import type {IPicProps} from '../../../typings/upload';

/**
 * 接口上传图片
 * @param tempFiles
 * @returns
 */
export const uploadFileToApi = (tempFile: IPicProps, bucketConfName = '') => {
    return new Promise((resolve, reject) => {
        try {
            uploadPicToApiService(tempFile.filePath, bucketConfName)
                .then(res => {
                    resolve(res);
                })
                .catch(err => {
                    ubcCommonViewSend({
                        value: 'uploadToApiErr',
                        ext: {
                            info: JSON.stringify(err || '')
                        }
                    });
                    reject(err);
                });
        } catch (err) {
            ubcCommonViewSend({
                value: 'uploadToApiErr',
                ext: {
                    info: JSON.stringify(err || '')
                }
            });
            reject(err);
        }
    });
};
