import {base64toFile} from '@baidu/vita-utils-shared';
import {uploadToBos} from '@baidu/health-bos-uploader/lib/index.h5.esm';
import {chooseImage, chooseVideo, getImageInfo} from '@tarojs/taro';
import {ubcCommonViewSend} from '../../generalFunction/ubc';
import {uploadFileToApi} from '../../basicAbility/upload/uploadFileToApi';
import {showToast} from '../../customShowToast';
import {getFrameOfVideo} from '../getFrameOfVideo';
import {BUCKET_NAME} from '../../../constants/common';
import {DEFAULT_VIDEO_THUMB} from '../../../constants/msg';
import {getBosToken} from '../../../models/services/common';
import {getSystemInfo} from '../../taro';
import type {IPicConfProps, IPicProps} from '../../../typings/upload';
import {imageVerification} from './imageVerification';

const systemInfo = getSystemInfo();

/**
 * @Description 预览图片
 * @param {Object} Conf 用户配置
 * @param {number} Conf.count 图片数量
 * @param {number} Conf.quality 压缩质量，范围0～100，数值越小，质量越低，压缩率越高（仅对jpg有效）
 * @returns {Promise}
 */
export const previewPic = async (Conf: IPicConfProps) => {
    try {
        const sizeType: Array<'original' | 'compressed'> =
            systemInfo?.platform === 'ios' ? ['original', 'compressed'] : ['original'];
        const data = await chooseImage({
            count: Conf.count || 1,
            sizeType,
            sourceType: Conf?.btnType ? [Conf?.btnType] : ['album', 'camera', 'user', 'environment']
        });

        const {tempFiles} = data;

        // 如果图片是heic格式，则进行提示拦截
        const findHeic = tempFiles?.some(file => {
            const fileType = file?.type ? file.type.toLowerCase() : '';
            const _isFind = fileType.includes('heic') || fileType.includes('heif');

            if (_isFind) {
                showToast({
                    title: `暂不支持${file?.type || '此'}格式图片,请修改后再试`,
                    icon: 'none'
                });
            }

            return _isFind;
        });
        if (findHeic) {
            return false;
        }
        if (Conf?.imageMaxSize) {
            // eslint-disable-next-line no-console
            console.warn('上传图片大小限制功能暂未开发，目前仅支持微信小程序。');
        }
        let resultData: IPicProps[] = [];
        await Promise.all(
            tempFiles.map(async item => {
                // 获取图片宽高
                const infoData = await getImageInfo({src: item?.path});
                // merge数据
                const res = Object.assign({}, item, {
                    width: infoData.width,
                    height: infoData.height,
                    filePath: infoData?.path
                });
                resultData.push(res);
            })
        );

        // H5上传时由于无法限制上传个数，只能截取到单次上传最大数量，所以这里需要提示用户
        if (process.env.TARO_ENV === 'h5' && Conf?.H5Tips) {
            tempFiles?.length > (Conf?.count || 1) &&
                showToast({
                    title: Conf?.H5Tips,
                    icon: 'none'
                });

            Conf?.remainNum &&
                tempFiles?.length > Conf?.remainNum &&
                showToast({
                    title: '图片上传数量已达到上限',
                    icon: 'none'
                });
        }

        // 如果限制了上传图片的数量 剔除掉最后数据（H5的 chooseImg在Chrome无法限制数量）
        if ((Conf?.count || 1) < resultData.length) {
            resultData = resultData.slice(0, Conf?.count || 1);
        }
        return resultData;
    } catch (error) {
        console.error(error);

        return [];
    }
};

/**
 * 上传图片
 * @param tempFiles
 * @returns
 */
export const uploadFileToBos = async (
    tempFiles: IPicProps[],
    Conf: IPicConfProps,
    uploadFileType = 'pic'
): Promise<IPicProps[]> => {
    // eslint-disable-next-line no-async-promise-executor
    return new Promise(async (res, rej) => {
        try {
            await imageVerification(tempFiles);
            const [bosErr, bosData] = await getBosToken({
                bucketConfName: Conf.bucketConfName || 'muzhi-pic',
                fileNum: Conf.count || 1,
                ...(uploadFileType === 'video' ? {pointTime: 1} : {})
            });
            if (!bosErr && bosData?.status === 0) {
                const {
                    accessKeyID,
                    fileNames = [],
                    secretAccessKey,
                    sessionToken,
                    bucket
                } = bosData.data;
                const resultData: IPicProps[] = [];
                await Promise.all(
                    tempFiles.map(async (item, index) => {
                        const suffix =
                            typeof item?.type === 'string' ? item?.type.split('/')?.[1] : 'jpg';
                        const fileName = `${fileNames[index]}.${suffix}`;
                        try {
                            await uploadToBos({
                                // file 文件
                                filePath: item.path || item.filePath,
                                // bucket 名称
                                bucketName: bucket,
                                // 文件名
                                fileName,
                                // STS 临时秘钥
                                accessKeyID,
                                secretAccessKey,
                                sessionToken
                            });
                            resultData.push(Object.assign({}, item, {fileName}));
                        } catch (e) {
                            // 兜底接口上传
                            // eslint-disable-next-line @typescript-eslint/no-explicit-any
                            const resUpload: any = await uploadFileToApi(item, Conf.bucketConfName);
                            if (resUpload.data && resUpload.data.picID) {
                                const result = Object.assign(
                                    {},
                                    item,
                                    {...resUpload?.data},
                                    {fileName: resUpload?.data?.picID}
                                );
                                resultData.push(result);
                            }
                            ubcCommonViewSend({
                                value: 'uploadToBosErr',
                                ext: {
                                    info: JSON.stringify(e || '')
                                }
                            });
                        }
                    })
                );
                // 数组为空的情况，抛出异常，阻止调用 sendMsg
                resultData?.length ? res(resultData) : rej(resultData);
            } else {
                ubcCommonViewSend({
                    value: 'getBosTokenApiErr',
                    ext: {
                        info: JSON.stringify(bosErr || '')
                    }
                });
                rej(bosErr);
            }
        } catch (error) {
            console.error('上传失败, 请检查相关配置', error);
            rej(error);
        }
    });
};

/**
 * @Description 预览和上传图片 因为
 * @param {Object} Conf 用户配置
 * @param {number} Conf.count 图片数量
 * @param {number} Conf.bucketConfName bucket 名称
 * @param {number} Conf.quality 压缩质量，范围0～100，数值越小，质量越低，压缩率越高（仅对jpg有效）
 * @returns {Promise}
 */
export const preAndUploadPic = (Conf: IPicConfProps): Promise<IPicProps[]> => {
    // eslint-disable-next-line no-async-promise-executor
    return new Promise(async (res, rej) => {
        try {
            const preData = await previewPic(Conf);
            const picData = await uploadFileToBos(preData || [], Conf);
            res(picData || []);
        } catch (error) {
            rej(error);
        }
    });
};

/**
 * @Description 预览视频
 * @param {Object} Conf 用户配置
 * @returns {Promise}
 */
export const previewVideo = async (conf: IPicConfProps) => {
    try {
        let thumbForSend = DEFAULT_VIDEO_THUMB;
        const data = await chooseVideo({
            compressed: conf.compressed || true,
            maxDuration: conf.maxDuration || 1800,
            sourceType: ['album', 'camera']
        });
        if (!data.tempFilePath) {
            showToast({title: '上传失败，请重新上传', icon: 'none'});

            return [];
        }
        const thumb = await getFrameOfVideo({
            filePath: data?.tempFilePath,
            frame: 0
        });

        if (thumb?.dataUri) {
            const resp = await uploadFileToBos(
                [
                    {
                        filePath: thumb?.dataUri,
                        size: thumb?.size,
                        type: thumb?.type,
                        originalFileObj: thumb?.file
                    }
                ],
                {
                    count: 1,
                    bucketConfName: BUCKET_NAME[2]
                }
            );
            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
            thumbForSend = (resp?.length && resp[0] ? resp[0].fileName : '')!;
        }
        const type = data.tempFilePath.split(';')?.[0].split(':')?.[1];
        const file = base64toFile(data.tempFilePath, type);
        const resultData: IPicProps[] = [];
        const res = Object.assign(
            {},
            {...data},
            {
                filePath: data.tempFilePath,
                type,
                thumb: thumbForSend,
                originalFileObj: file
            }
        );

        resultData.push(res);

        return resultData;
    } catch (error) {
        console.error(error);

        return [];
    }
};
