import {hideLoading} from '@tarojs/taro';
import {
    AfterRequestReturnVal,
    RejectErrorVal,
    RequestConf as RequestParams,
    getCurrentPage,
    weirwoodReqFailedReport
} from '@baidu/vita-utils-shared';

import {showToast} from '../../../../utils/customShowToast';
// import globalData from '@globalDataStore/globalData';
import {navigate} from '../../../../utils/basicAbility/commonNavigate';
import {addObjectToUrlQuery} from '../../../index';
import {pageNetworkStatus} from '../../../../constants/common';

import {isFilterErrorUrl} from './callbackUrl';

export const beforeRequestCallback = (conf: RequestParams) => {
    if (conf.isFirstScreen) {
        setNetworkStatus(pageNetworkStatus.loading);
    }
};

export const afterRequestCallback = (params: AfterRequestReturnVal) => {
    const {conf, response} = params;
    // @ts-expect-error 临时防止报错
    console.log(conf, response);
    // if (conf.isFirstScreen) {
    //     const log = globalData.get('log') || {};
    //     globalData.set('log', {
    //         ...log,
    //         applid: response.applid,
    //         // applid为空则不更新referlid
    //         referlid: log.applid || log.referlid
    //     });
    // }
};

// TODO 暂时注释，保留相关函数入参的声明避免 eslint 报错；@wanghaoyu08
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const requestSucceedCallback = (params: AfterRequestReturnVal) => {
    const {conf, response} = params;
    // 商城接口未登录，跳转登录页，因商城未登录状态码为1，与问诊成功状态冲突，故在成功中判断
    if (response?.status === 1 && conf.url.includes('tradeflow/b2c/prescription')) {
        const curPage = getCurrentPage();
        const to = `/${curPage?.path}`;
        const path =
            process.env.TARO_ENV === 'weapp'
                ? '/wenzhen/pages/bdpass/index'
                : '/wenzhen/pages/common/error/index';
        const pageConfig = {
            PageStatus: 1,
            loginSource: 'se_020003'
        };
        const param = JSON.stringify({
            ...pageConfig,
            to,
            sourcePage: encodeURIComponent(curPage?.path)
        });
        const url = addObjectToUrlQuery(path, {msg: encodeURIComponent(param)});

        // eslint-disable-next-line no-console
        console.warn('unLoginCallback 回调', curPage, path.split('/wenzhen/')[1]);
        if (
            process.env.TARO_ENV === 'weapp' &&
            curPage?.route.includes(path.split('/wenzhen/')[1])
        ) {
            // eslint-disable-next-line no-console
            console.warn(
                'unLoginCallback 回调：当前页面已跳转到登录页，后续请求队列中的跳转暂不执行',
                curPage
            );

            return;
        }

        navigate({
            url,
            openType: 'redirect'
        });
    }
    setNetworkStatus(pageNetworkStatus.success);
    // const { conf, response, fullResponse } = params;

    // if (process.env.TARO_ENV !== 'h5') {
    //     weirwoodReqFailedReport({
    //         url: conf?.url,
    //         params: conf?.data,
    //         // @ts-expect-error wanghaoyu08
    //         method: conf.method,
    //         // @ts-expect-error wanghaoyu08 fullResponse 为全部 response 值
    //         responseHeaders: fullResponse.header,
    //         response: response || {},
    //         // @ts-expect-error wanghaoyu08 fullResponse 为全部 response 值
    //         status: fullResponse.statusCode
    //     });
    // }
};

export const requestFailedCallback = (params: RejectErrorVal) => {
    const {toast, conf, response, status, fullResponse} = params;
    const {errorToast, isFirstScreen} = conf;

    const callUrl = getCurrentPage();
    hideLoading();

    if (status === 40001) {
        // 权限错误跳转
        navigate({
            url: '/pages/common/validateLogin/index',
            openType: 'redirect',
            params: {
                // @ts-expect-error 错误类型临时处理
                soId: conf?.soId || '',
                // @ts-expect-error 错误类型临时处理
                loId: conf?.loId || '',
                // @ts-expect-error 错误类型临时处理
                qid: conf?.qid || '',
                callUrl: encodeURIComponent(callUrl?.path) || ''
            }
        });
    } else if (status === 60001) {
        // 挂号权限校验
        navigate({
            url: '/pages/common/validateLogin/index',
            openType: 'redirect',
            params: {
                callUrl: encodeURIComponent(callUrl?.path) || '',
                type: 'guahao'
            }
        });
    } else {
        // 通用错误toast提示; errorToast默认是true；flase 则不进行error提示
        const hideToastStatus = [508003];
        // TODO 异常状态码白名单 @banxiaoke
        if (errorToast && !hideToastStatus.includes(+(status || 0))) {
            toast &&
                showToast({
                    title: toast,
                    icon: 'none'
                });
        }
        // 首屏接口错误时, 将网络状态标示置为error
        if (isFirstScreen) {
            setNetworkStatus(pageNetworkStatus.error);
        }
    }

    if (process.env.TARO_ENV !== 'h5') {
        weirwoodReqFailedReport({
            url: conf?.url,
            params: conf?.data,
            // @ts-expect-error 错误类型临时处理
            method: conf.method,
            // @ts-expect-error wanghaoyu08 fullResponse 为全部 response 值
            responseHeaders: fullResponse.header,
            response: response || {},
            // @ts-expect-error wanghaoyu08 fullResponse 为全部 response 值
            status: fullResponse.statusCode
        });
    }
};

export const unLoginCallback = e => {
    try {
        const {response, conf} = e;
        const curPage = getCurrentPage();
        const to = `/${curPage?.path}`;

        // 屏蔽错误页，以免两次鉴权请求太快，跳转2次，传递给pass登录错误的callbackUrl
        if (isFilterErrorUrl(curPage?.path)) {
            return;
        }

        const path =
            process.env.TARO_ENV === 'weapp'
                ? '/wenzhen/pages/bdpass/index'
                : '/wenzhen/pages/common/error/index';
        const pageConfig = {
            PageStatus: 1,
            loginSource: 'se_020003'
        };
        const callUrl = getCurrentPage();

        // cui 和 blackTea 返回错误码冲突，cui 40001 权限错误，blackTea 40001
        if (response?.status === 40001 && conf.url.includes('/wzcui/uiservice')) {
            // 权限错误跳转
            navigate({
                url: '/wenzhen/pages/common/validateLogin/index',
                openType: 'redirect',
                params: {
                    soId: conf?.soId || '',
                    loId: conf?.loId || '',
                    qid: conf?.qid || '',
                    callUrl: encodeURIComponent(callUrl?.path) || ''
                }
            });

            return;
            // path = '/pages/wz-sub/error/index';
            // pageConfig = {
            //     PageStatus: 2,
            //     loginSource: 'se_080000',
            //     loginName: '***',
            //     loginAvatar: 'https://med-fe.cdn.bcebos.com/wenzhen-mini-app/default-head.jpg',
            //     loginTips: '（提交问题账号）',
            //     loginMsg: ['为保护用户隐私', '您需要登录提交问题时的百度账号方可浏览', '请更换账号重试'],
            //     loginBtn: '使用提交问题账号登录'
            // };
        } else if (response?.status === 30002 && !conf.url.includes('user/bindwx')) {
            const params = curPage?.options;
            if (params.questionId || params.question_id) {
                params.qid = params.questionId || params.question_id;
            }
            // 订单异常去订单详情页
            navigate({
                url: '/pages/wz/order/index',
                openType: 'redirect',
                params
            });

            return;
        } else if (response?.status === 60001 && conf.url.includes('/guahao/api')) {
            // 订单异常去validateLogin
            return navigate({
                url: '/wenzhen/pages/common/validateLogin/index',
                openType: 'redirect',
                params: {
                    callUrl: encodeURIComponent(callUrl?.path) || '',
                    type: 'guahao'
                }
            });
        }

        // 商城-处方详情页面status等于2表示当前登录用户与当前处方不符，则跳转到错误兜底页面
        if (response?.status === 2 && conf.url.includes('tradeflow/b2c/prescription')) {
            return navigate({
                url: '/wenzhen/pages/common/validateLogin/index',
                openType: 'redirect',
                params: {
                    callUrl: encodeURIComponent(callUrl?.path) || '',
                    type: 'chufang',
                    realUserPhone: response?.data?._page?.realUserPhone || ''
                }
            });
        }

        const params = JSON.stringify({
            ...pageConfig,
            to,
            sourcePage: encodeURIComponent(curPage?.path)
        });
        const url = addObjectToUrlQuery(path, {msg: encodeURIComponent(params)});

        // eslint-disable-next-line no-console
        console.warn('unLoginCallback 回调', curPage, path.split('/wenzhen/')[1]);
        if (
            process.env.TARO_ENV === 'weapp' &&
            curPage?.route.includes(path.split('/wenzhen/')[1])
        ) {
            // eslint-disable-next-line no-console
            console.warn(
                'unLoginCallback 回调：当前页面已跳转到登录页，后续请求队列中的跳转暂不执行',
                curPage
            );

            return;
        }

        navigate({
            url,
            openType: 'redirect'
        });
    } catch (err) {
        console.error('unLoginCallback 出错：', e);
    }
};

function setNetworkStatus(_status = -1) {
    // try {
    //     const network = globalData.get('network');
    //     if (network) {
    //         network.status = status;
    //         globalData.set('network', network);
    //     }
    // } catch (error) {
    //     console.error(error);
    //     throw error;
    // }
}
