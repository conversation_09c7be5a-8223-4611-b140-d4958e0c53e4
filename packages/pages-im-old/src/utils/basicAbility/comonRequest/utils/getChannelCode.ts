import {getCurrentUrlQuery} from '@baidu/vita-utils-shared';

import {inPc} from '../../../env';
import {isAliApp} from './getScene';

/**
 *
 * @description 获取 channel_code 参数
 * @returns string
 */
export const getChannelCode = (): string => {
    try {
        let res = '';
        if (process.env.TARO_ENV === 'h5') {
            const isPc = inPc();
            res = isPc ? 'pc-miniapp' : 'web-miniapp';
        } else if (process.env.TARO_ENV === 'swan') {
            res = 'bd-miniapp';
        } else if (process.env.TARO_ENV === 'weapp') {
            res = 'weixin-miniapp';
        }
        // 学习强国场景
        isAliApp() && (res = 'web_xuexiqiangguo');

        const pageQuery = getCurrentUrlQuery();
        if (pageQuery?.channel_code) {
            res = pageQuery?.channel_code;
        }

        return res;
    } catch (err) {
        console.error('getChannelCode 出错：', err);

        return '';
    }
};
