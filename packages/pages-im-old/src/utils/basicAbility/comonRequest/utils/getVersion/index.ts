import {ONLINE_HOST} from '../../../../../constants/common';

export const getVitaVersion = () => {
    const isOnlineProduction = ONLINE_HOST.includes(process.env.TARO_APP_API_HOST || '');
    let v = isOnlineProduction ? '*******' : '*******';
    if (process.env.TARO_APP_API_HOST === 'https://jiankang-fenji.baidu.com') {
        //TODO： 临时处理本次上线 fenji LR 版本，后续高优修复；@wanghaoyu08
        v = '*******';
    }
    return `web_${v}`;
};
