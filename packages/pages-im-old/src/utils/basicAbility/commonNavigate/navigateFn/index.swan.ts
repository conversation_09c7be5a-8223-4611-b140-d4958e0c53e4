import { navigateTo, redirectTo, switchTab, reLaunch, navigateBack } from '@tarojs/taro';

import { getSystemInfo } from '../../../taro/index';
import { WX_APP_KEY_MAP } from '../../../../constants/common';
import { ubcCommonClkSend } from '../../../generalFunction/ubc';

import { OpenType } from '../main';
import { NavigateFnParams } from './index.d';

const easybrowseConfig = {
    // 手百
    baiduboxapp(url) {
        return {
            module: 'easybrowse',
            action: 'open',
            parameters: {
                url,
                newbrowser: 1
            }
        };
    },
    // 地图
    bdmap(url) {
        return {
            authority: 'map',
            path: '/cost_share',
            parameters: {
                popRoot: 'no',
                url,
                hideshare: 'yes'
            }
        };
    },
    // 百度极速版
    bdlite(url) {
        return {
            module: 'easybrowse',
            action: 'open',
            parameters: {
                url,
                newbrowser: 1
            }
        };
    }
};

// 找不到匹配config时，使用默认配置
const defaultAction = url => {
    return {
        module: 'easybrowse',
        action: 'open',
        parameters: {
            url,
            simple: '1'
        }
    };
};

export const navigateFn = (fnArgs: NavigateFnParams) => {

    /**
     *
     * @description 打开手百轻浏览框架跳转
     * @param arg
     */
    const easybrowseNavigate = (arg: NavigateFnParams) => {
        const sys = getSystemInfo();
        const { host = 'baiduboxapp' } = sys;
        const config = easybrowseConfig[host]?.(arg?.url) || defaultAction(arg?.url);
        swan.openBdboxWebview?.(config);
        ubcCommonClkSend({
            value: 'easybrowseOpen',
            ext: {
                value_id: host
            }
        });
    };

    /**
     *
     * @description 百度跳转三方小程序
     * @param arg
     * @param {string} arg.url 跳转链接 小程序拼接规则：baiduboxapp://swan/${appKey}/${path}?${query}
     * @example baiduboxapp://swan/********************************/pages/view/view?query=1&_baiduboxapp=%7B%22ext%22%3A%7B%7D%7D&callback=_bdbox_js_275&upgrade=0
     */
    const navigateotherMiniApp = (arg: NavigateFnParams) => {
        const matchs = (arg?.url && arg?.url.match(/baiduboxapp:\/\/swan\/(\w+)\/(.+)/)) || [];
        let appKeyData = '';
        let pathData = '';
        // 判断url中是否带有baiduboxapp://swan/${appKey}/
        if (arg?.url && arg?.url.includes('baiduboxapp:')) {
            [, appKeyData, pathData] = matchs;
        } else {
            // url只有pages路径
            appKeyData = (arg?.miniAppName && WX_APP_KEY_MAP[arg?.miniAppName]) || '';
            pathData = arg?.url || '';
        }

        swan.navigateToSmartProgram({
            appKey: appKeyData,
            path: pathData,
            fail: (error: Error) => {
                console.error('navigateToSmartProgram 出错：', error);
            }
        });
    };

    const fnMap: { [k in OpenType]?: (arg?: unknown) => unknown } = {
        switchTab,
        navigateBack,
        navigate: navigateTo,
        redirect: redirectTo,
        relaunch: reLaunch,
        easybrowse: easybrowseNavigate,
        otherMiniApp: navigateotherMiniApp
    };

    fnMap[fnArgs?.openType]?.(fnArgs);
};
