import type { EventChannel } from '@tarojs/taro';
import { OpenType, NavigateParams } from '../main';

export interface NavigateFnParams {
    openType: OpenType;
    url?: string;
    delta?: number;
    miniAppName?: string;
    needUserCheck?: boolean;
    extraData?: NavigateParams['extraData'];
    envVersion?: 'develop' | 'trial' | 'release';
    needTakeLoginStateOfWechat?: NavigateParams['needTakeLoginStateOfWechat'];
}

export interface NavigateToOption {
    url: string;
    complete?: (res: TaroGeneral.CallbackResult) => void;
    events?: TaroGeneral.IAnyObject;
    fail?: (res: TaroGeneral.CallbackResult) => void;
    success?: (res: TaroGeneral.CallbackResult & { eventChannel: EventChannel }) => void;
}
