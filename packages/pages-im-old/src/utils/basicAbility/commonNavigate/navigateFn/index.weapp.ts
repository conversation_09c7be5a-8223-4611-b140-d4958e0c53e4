import {
    reLaunch,
    switchTab,
    navigateTo,
    redirectTo,
    navigateBack,
    requirePlugin,
    getAccountInfoSync
} from '@tarojs/taro';

import { WECHAT_TABBAR, WZ_ALL_PATH } from '../../../../path/wenzhen';
import { WECHAT_JIAYI_TABBAR, JIAYI_ALL_PATH } from '../../../../path/jiayi';

import { LOCAL_APPID_MAP, WX_APPID_MAP } from '../../../../constants/common';

import { NAV_MINI_APP_VERSION } from '../../../../models/apis/host';
import { getWechatPassUserInfo } from '../../../../utils/generalFunction/login';

import { OpenType } from '../main';
import type { NavigateFnParams, NavigateToOption } from './index.d';

/**
 *
 * @description 判断落地页是否为 tab 页，如果是按照协议返回结果数据
 * @param path
 * @returns
 */
function mergeParams(path) {
    try {
        // 判断是否为 tabbar 页面， tab 页面需要将 params 参数通过 extraData 透传；
        let isTabPage = false;
        const wenzhenWeappTabs = WECHAT_TABBAR.list.map(item => item.pagePath);
        const jiayiWeappTabs = WECHAT_JIAYI_TABBAR.list.map(item => item.pagePath);

        [...wenzhenWeappTabs, ...jiayiWeappTabs].some(i => {
            if (path?.includes(i) || path?.includes(`/${i as string}`)) {
                isTabPage = true;

                return true;
            }

            return false;
        });

        let rewriteParams = {};
        if (isTabPage) {
            rewriteParams = {};

            return {
                path,
                params: rewriteParams,
                isTabPage: false
            };
        }

        return {
            path,
            params: {},
            isTabPage: false
        };
    } catch (err) {
        console.error('navigateFn mergeParams 执行出错：', err);

        return {
            path,
            params: {},
            isTabPage: false
        };
    }
}

export const navigateFn = (fnArgs: NavigateFnParams) => {

    /**
     *
     * @description 微信跳转三方小程序
     * @param {string} arg.url 跳转链接 weixin://dl/${appKey}/${path}?${query}
     * @example weixin://dl/4fecoAqgCIUtzIyA4FAPgoyrc4oUc25c/pages/view/view?query=1&_baiduboxapp=%7B%22ext%22%3A%7B%7D%7D&callback=_bdbox_js_275&upgrade=0
     */
    const otherMiniApp = async (arg: NavigateFnParams) => {
        try {
            const { extraData, miniAppName, needTakeLoginStateOfWechat = true, url = '' } = arg;
            let path = arg?.url;
            let appId = LOCAL_APPID_MAP[miniAppName || ''];

            const matchs = url.match(/weixin:\/\/dl\/(\w+)\/(.+)/) || [];
            if (matchs && matchs.length > 0) {
                appId = matchs[1] || '';
                path = matchs[2] || '';
            }

            if ((miniAppName && !LOCAL_APPID_MAP[miniAppName]) || !appId) {
                console.error('otherMiniApp 获取小程序 appid 出错');

                return;
            }

            // 处理落地页为 tab 页场景
            const { params: rewriteParams } = mergeParams(arg?.url);

            if (needTakeLoginStateOfWechat) {
                // 跳转下游需要用户 check 的小程序
                const needUserCheckMpList = [
                    LOCAL_APPID_MAP.wx_jiayi,
                    LOCAL_APPID_MAP.healthTipsBag,
                    LOCAL_APPID_MAP.wx_wenzhen,
                    LOCAL_APPID_MAP.wx_aiAssistant
                ];

                const plugin = requirePlugin('baidu-health-login-transfer');
                const paramsToMp = await plugin.genNavigateToMpWithLoginStatusParams({
                    path,
                    noRelaunchIfPathUnchanged: false,
                    appId,
                    needUserCheck: needUserCheckMpList?.includes(appId),
                    envVersion: arg.envVersion || NAV_MINI_APP_VERSION,
                    getBdussFunction: () => {
                        const userInfo = getWechatPassUserInfo();

                        return { bduss: userInfo?.bduss || '' };
                    },
                    failCallback: res => {
                        console.error('otherMiniApp 跳转失败', res);
                    }
                });

                const mergedParams = {
                    ...paramsToMp,
                    extraData: {
                        ...rewriteParams,
                        ...extraData,
                        // WARNING：以下三个参数用于透传登录态，切勿改动；
                        token: paramsToMp.extraData?.token || '',
                        randomSeed: paramsToMp.extraData?.randomSeed || '',
                        needUserCheck: paramsToMp.extraData?.needUserCheck || false
                    }
                };

                wx.navigateToMiniProgram({
                    ...mergedParams
                });

                return;
            }

            wx.navigateToMiniProgram({
                path,
                noRelaunchIfPathUnchanged: false,
                appId,
                envVersion: arg.envVersion || NAV_MINI_APP_VERSION,
                extraData: {
                    ...rewriteParams,
                    ...extraData
                }
            });
        } catch (error) {
            console.error(error);
        }
    };

    const navigate = (option: NavigateToOption) => {
        const isJiayi = getAccountInfoSync()?.miniProgram?.appId === WX_APPID_MAP.jiayiAppId;

        if (isJiayi) {
            // 1. 判断家医小程序地址存在，直接前往家医微信小程序
            // 2. 判断家医小程序端地址不存在，存在wenzhen小程序地址时，直接前往wenzhen打开百度健康微信小程序的原生页
            let redirectUrl = option?.url;
            const searchIndex = redirectUrl.indexOf('?');
            redirectUrl = searchIndex !== -1 ? redirectUrl.substring(0, searchIndex) : redirectUrl;
            // 格式化地址，去掉所有/开头的和wenzhen/开头的
            redirectUrl = redirectUrl.replace(/^\/?(wenzhen\/)?/, '');
            // 1. 判断家医小程序地址存在，直接前往家医微信小程序
            if (JIAYI_ALL_PATH.includes(redirectUrl)) {
                return navigateTo(option);
            } else if (
                // 2. 判断家医小程序端地址不存在，存在wenzhen小程序或就医|服务包分包地址时，直接前往wenzhen打开百度健康微信小程序的原生页
                WZ_ALL_PATH.includes(redirectUrl)
                || redirectUrl.startsWith('decision/')
                || redirectUrl.startsWith('vas/')
            ) {
                return otherMiniApp({
                    extraData: {},
                    miniAppName: 'wx_wenzhen',
                    openType: 'otherMiniApp',
                    url: option?.url
                });
            }

            return navigateTo(option);
        }

        return navigateTo(option);
    };

    const fnMap: { [k in OpenType]?: (arg?: unknown) => unknown } = {
        switchTab,
        navigateBack,
        navigate,
        redirect: redirectTo,
        relaunch: reLaunch,
        otherMiniApp
    };

    fnMap[fnArgs?.openType]?.(fnArgs);
};
