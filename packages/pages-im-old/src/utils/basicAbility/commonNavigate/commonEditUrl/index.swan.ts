import {H5_HOST} from '../../../../models/apis/host';
import {OpenType} from '../main';

// 需要轻浏览框架打开 list
const needEasybrowseList = [
    'pages/jiayi/purchase/index',
    'pages/middlePage/index',
    'wwwhos/h5#/', // 增值服务包手百场景都走轻框
    'mfollow/materials', // 患教资料H5
    'map.baidu.com', // 百度地图
    'health.baidu.com',
    'healthbusiness/pages/diseaseZone'
];

/**
 *
 * @description 处理需要使用手百轻框打开的页面
 * @param url url 地址
 * @param openType open 类型
 * @returns
 */
const dealneedEasybrowseList = (
    url: string,
    openType: OpenType
): {
    url: string;
    openType: OpenType;
    hasRewrited: boolean;
} => {
    try {
        let resUrl = url;
        let resOpenType = openType;

        needEasybrowseList.some(i => {
            if (url.includes(i)) {
                resUrl =
                    url.startsWith('http://') || url.startsWith('https://')
                        ? url
                        : `${H5_HOST}${(url.startsWith('/') ? url : `/${url}`) as string}`;

                resOpenType = 'easybrowse';

                return true;
            }
        });

        return {
            hasRewrited: true,
            url: resUrl,
            openType: resOpenType
        };
    } catch (err) {
        console.error('dealneedEasybrowseList 出错：', err);

        return {
            hasRewrited: false,
            url,
            openType
        };
    }
};

const setNewUrl = (url, openType) => {
    const _openType = openType;
    const _url = url;
    let matches: string[] = [];

    const reg = new RegExp(
        '\\/?(healthbusiness|sporem|uha/ask|mfollow/materials|mall|tuanjian|' +
            'vas|decision|aihub|wenzhen|guahao)\\/pages[\\w-/]+'
    );

    // 提取URL中的路径部分 针对可能返回全路径的已重构页面
    const extractPath = (url: string) => {
        // 如果包含域名，则截取域名后的部分
        if (url.includes('://')) {
            return `/${url.split('://')[1].split('/').slice(1).join('/')}`;
        }
        // 如果以/开头，直接返回
        if (url.startsWith('/')) {
            return url;
        }
        // 其他情况添加/前缀
        return `/${url}`;
    };

    if (reg.test(_url)) {
        return {
            _url: extractPath(_url),
            _openType
        };
    }

    // 小程序跳转其他小程序
    if (/^baiduboxapp.*/.test(url)) {
        return {_url: url, _openType: 'otherMiniApp'};
    }

    if ((matches = _url.match(/\/?(pages[\w-/]+.*)/) as string[])) {
        // 是否直接返回传入的openType
        const filterUrlArr = [
            'pages/brand/home/<USER>', // 百度健康首页
            'pages/chats/index' // 百度智能小程序AI健康助手
        ];

        const filterUrlData = filterUrlArr.find(urlStr => {
            return _url.indexOf(urlStr) > -1;
        });
        const openTypeVal = filterUrlData ? _openType : 'navigate';

        return {
            _url: `/${matches[1] || matches[1] || (_url as string)}`,
            _openType: openTypeVal
        };
    }

    if (_url?.startsWith('http')) {
        return {_url, _openType: 'easybrowse'};
    }

    return {_url, _openType};
};

export const commonRedirectUrlFn = ({
    url,
    openType
}): Promise<{url: string; openType: OpenType}> => {
    return new Promise((resolve, reject) => {
        try {
            let _url = url;
            let _openType = openType;
            // 如果是非轻浏览框架页面处理 url
            if (openType !== 'easybrowse') {
                const r = setNewUrl(url, openType);
                _url = r._url as string;
                _openType = r._openType as string;
            }

            // 处理需要轻框打开的地址
            const easybrowseRes = dealneedEasybrowseList(_url, _openType);
            if (easybrowseRes?.hasRewrited) {
                _url = easybrowseRes?.url;
                _openType = easybrowseRes?.openType;
            }

            try {
                resolve({
                    url: _url,
                    openType: _openType
                });
            } catch (err) {
                reject(err);
            }
        } catch (err) {
            console.error('commonRedirectUrlFn 出错：', err);
            reject(err);
        }
    });
};
