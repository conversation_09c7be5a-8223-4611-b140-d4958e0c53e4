import { getCurrentInstance } from '@tarojs/taro';

import { ubcCommonViewSend } from '../../../utils/generalFunction/ubc';

import {
    UrlParams,
    NavigateParams,
    AddPublicParamsFn,
    CommonRedirectUrlFn,
    CommonNavigationInitArgs,
    CommonNavigationInstance
} from './typing.d';
import { navigateFn } from './navigateFn';

export * from './typing.d';

export class CommonNavigation implements CommonNavigationInstance {
    constructor(args: CommonNavigationInitArgs) {
        args?.addPublicParamsFn && (this.addPublicParamsFn = args?.addPublicParamsFn);
        args?.commonRedirectUrlFn && (this.commonRedirectUrlFn = args?.commonRedirectUrlFn);
    }

    // 自定义增加通用参数函数
    private addPublicParamsFn: AddPublicParamsFn | null = null;

    // 通用重镜像 url 函数
    private commonRedirectUrlFn: CommonRedirectUrlFn | null = null;

    private getCurPageUrlParams = (): { [k in string]: string } => {
        try {
            const data = getCurrentInstance?.();
            if (data?.router?.params) {
                const o = data?.router?.params;
                delete o.$taroTimestamp;

                return o as { [k in string]: string };
            }

            return {};
        } catch (err) {
            console.error('getCurPageUrlParams 出错：', err);

            return {};
        }
    };

    /**
     *
     * @description 将 params 对象拼接到 url 后
     * @param param0 * url 地址
     * @param param1 {UrlParams} * params 需要拼接的参数
     * @returns {string} 拼接后的 url 地址
     */
    public spliceUrlWithParams = ({ urlArg, params }: { urlArg: string; params: UrlParams }): string => {
        try {
            let resUrl = urlArg;

            if (params && Object?.keys(params)?.length && urlArg) {
                resUrl = Object?.keys(params)?.reduce((pre, cur, index) => {
                    if (urlArg?.includes('?')) {
                        return urlArg?.endsWith('&') ? `${pre}${cur}=${params[cur]}` : `${pre}&${cur}=${params[cur]}`;
                    }

                    return index > 0 ? `${pre}&${cur}=${params[cur]}` : `${pre}?${cur}=${params[cur]}`;
                }, urlArg);
            }

            return resUrl;
        } catch (err) {
            console.error('urlDecorateFn 出错：', err);

            return urlArg;
        }
    };

    /**
     *
     * @description url 解析 url 和拼接的参数对象
     * @param param0
     * @returns
     */
    public getUrlAndParamsFromFull = ({
        initialUrl
    }: {
        initialUrl: string;
    }): { url: string; params: UrlParams | null } => {
        try {
            let resUrl = '';
            const paramsObj = {};
            if (initialUrl) {
                // 解决 url 上有多个问号的场景
                const paramsStartIndex = initialUrl.indexOf('?');
                // 链接上可能没有拼接参数
                if (paramsStartIndex < 0) {
                    return {
                        url: initialUrl,
                        params: {}
                    };
                }
                const s = initialUrl.substring(0, paramsStartIndex);
                const p = initialUrl.substring(paramsStartIndex + 1);
                // const [s, p] = initialUrl.split('?');
                resUrl = s;
                const params = p?.split('&');
                params?.length
                    && params.forEach(i => {
                        // eslint-disable-next-line prefer-destructuring
                        paramsObj[i.split('=')[0]] = i.split('=')[1];
                    });

                return {
                    url: resUrl,
                    params: paramsObj
                };
            }

            return { url: '', params: null };
        } catch (err) {
            console.error('getUrlAndParamsFromFull 出错：', err);

            return { url: '', params: null };
        }
    };

    /**
     *
     * @description 页面跳转方法
     * @param arg
     * @returns
     */
    public navigate = async (arg: NavigateParams): Promise<null> => {
        return new Promise(async (resolve, reject) => {
            try {
                const {
                    url = '',
                    delta,
                    params,
                    extraData = {},
                    miniAppName,
                    openType = 'navigate',
                    validateUrl,
                    redireactUrlFn,
                    commonCallbackBeforeNavigation
                } = arg;

                const { url: pureUrl, params: pureParams } = this.getUrlAndParamsFromFull({ initialUrl: url });
                const currentPageUrlParams = this.getCurPageUrlParams();

                let finalParams: UrlParams | null = {
                    ...pureParams,
                    ...params
                };
                let finalExtraData = {
                    ...extraData
                };

                if (this.addPublicParamsFn) {
                    const r = this.addPublicParamsFn({
                        url: pureUrl,
                        initialParams: currentPageUrlParams || {},
                        curParams: finalParams || {}
                    });
                    finalParams = r.params;
                    finalExtraData = {
                        ...finalExtraData,
                        ...r.extraDataParams
                    };
                }
                const finalUrl = this.spliceUrlWithParams({ urlArg: pureUrl, params: finalParams || {} });

                // 自定义校验
                if (validateUrl && !validateUrl(finalUrl)) {
                    console.error('跳转被拦截，validateUrl 校验未通过');

                    return;
                }

                // 通用跳转前回调
                commonCallbackBeforeNavigation
                    && commonCallbackBeforeNavigation({
                        openType,
                        url: finalUrl,
                        params: finalParams || {}
                    });

                let redirectUrl;
                let redirectOpenType;
                if (this.commonRedirectUrlFn) {
                    const r = await this.commonRedirectUrlFn({ url: finalUrl, openType });
                    redirectUrl = r?.url;
                    redirectOpenType = r?.openType;
                }

                if (redireactUrlFn) {
                    const r = await redireactUrlFn({
                        openType: redirectOpenType || openType,
                        url: redirectUrl || finalUrl
                    });
                    redirectUrl = r?.url;
                    redirectOpenType = r?.openType;
                }

                navigateFn({
                    miniAppName,
                    extraData: finalExtraData,
                    url: redirectUrl || finalUrl,
                    openType: redirectOpenType || openType,
                    ...(delta ? { delta: Number(delta) } : {})
                });

                resolve(null);
            } catch (err) {
                ubcCommonViewSend({
                    value: 'navigateErr',
                    ext: {
                        info: JSON.stringify(err)
                    }
                });

                console.error('navigate 出错：', err);
                reject(err);
            }
        });
    };
}
