export interface CommonNavigationInstance {
    navigate: (arg: NavigateParams) => void;
}

export interface CommonNavigationInitArgs {
    addPublicParamsFn?: AddPublicParamsFn;
    commonRedirectUrlFn?: CommonRedirectUrlFn;
}

export interface NavigateParams {
    url?: string;
    openType: OpenType;
    delta?: number;
    params?: UrlParams;
    // 用于跳转不同小程序，标识下游小程序；
    miniAppName?: string;
    // 微信小程序跳转时，是否需要携带登录态；（默认携带）
    needTakeLoginStateOfWechat?: boolean;
    // 用于跳转微信小程序时，携带透传参数；
    extraData?: {
        [k in string]: string | number;
    };
    // 校验 url 合法性回调；
    validateUrl?: (url: string) => boolean;
    // 链接通用处理前切面，用于修改 url 链接，修改后的链接扔会按照通用方法处理；
    commonRedirectUrlFn?: CommonRedirectUrlFn;
    // 页面跳转前最终切面，用于修改通用处理后的 url 链接；
    redireactUrlFn?: (arg: { url: string; openType: OpenType }) => Promise<{
        url: string;
        openType: OpenType;
    }>;
    // 页面跳转前回调，返回值是 Promise;
    commonCallbackBeforeNavigation?: (args: { url: string; params: UrlParams; openType: OpenType }) => Promise<unknown>;
}

export type AddPublicParamsFn = (arg: {
    url: string;
    initialParams: UrlParams; // 页面初始化的 params
    curParams: UrlParams;
}) => {
    params: UrlParams | null;
    extraDataParams: {
        [k in string]: string | number;
    };
};

export type CommonRedirectUrlFn = (arg: { url: string; openType: OpenType }) => Promise<{
    url: string;
    openType: OpenType;
}>;

export type OpenType =
    | 'navigate'
    | 'redirect'
    | 'switchTab'
    | 'relaunch'
    | 'navigateBack'
    | 'easybrowse'
    | 'otherMiniApp';

export type UrlParams = {
    [k in string | number]: string;
};
