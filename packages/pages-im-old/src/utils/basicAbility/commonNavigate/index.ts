import { CommonNavigation } from './main';

import { commonRedirectUrlFn } from './commonEditUrl';
import { addPublicParamsFn } from './addPublicParamsFn';


type UrlParams = {
    [k in string | number]: string;
};

export type OpenType =
    | 'navigate'
    | 'redirect'
    | 'switchTab'
    | 'relaunch'
    | 'navigateBack'
    | 'easybrowse'
    | 'otherMiniApp';



const navigation = new CommonNavigation({
    // 通用 url 修改方法
    commonRedirectUrlFn,
    // 公共参数
    addPublicParamsFn
});

export const navigate = navigation?.navigate;
export {
    UrlParams,
    OpenType
};
