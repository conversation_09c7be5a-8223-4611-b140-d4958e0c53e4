import {getFileInfo} from '@tarojs/taro';

/**
 *
 * @description 获取视频对应帧图片
 * @param param0 * filePath 文件路径
 * @param param0 * frame 视频帧数，默认 0 即第一帧
 */
export const getFrameOfVideo = ({filePath}: { filePath: string; frame?: number }) => {
    getFileInfo({
        filePath,
        success: res => {
            // 排查问题需要，保留
            console.info('选择的视频', res);
        },
        fail: err => {
            console.error(err);
        }
    });
};
