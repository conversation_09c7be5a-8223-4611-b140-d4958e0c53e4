import {getVideoSize, dataURItoBlob, base64toFile} from '@baidu/vita-utils-shared';

export const getFrameOfVideo = ({
    filePath
}: {
    filePath: string;
}): Promise<{
    type: string;
    size: number;
    blobPic: string;
    dataUri: string;
    transType: string;
    file: File;
}> => {
    return new Promise((resolve, rej) => {
        try {
            if (!filePath || !(process.env.TARO_ENV === 'h5')) {
                process.env.TARO_ENV === 'h5'
                    ? console.error('getFirstFrameOfVideo 出错：url 为必传参数')
                    : console.error(
                        'getFirstFrameOfVideo 出错：方法使用错误，该方法仅支持 h5 使用'
                    );
                rej();

                return;
            }

            const video = document.createElement('video');
            video.src = filePath;
            video.muted = true;
            video.autoplay = true;
            video.setAttribute('style', 'width:0;height:0;position:fixed;right:-100%;');

            document.body.appendChild(video);

            video.onloadeddata = () => {
                const {width, height} = getVideoSize(414, video.videoWidth, video.videoHeight);
                const canvas = document.createElement('canvas');

                canvas.width = width;
                canvas.height = height;

                canvas.getContext('2d')?.drawImage(video, 0, 0, width, height);

                // 视频转换为 base 64图片
                const res = canvas.toDataURL('image/png');
                const blobRes = dataURItoBlob(res);
                document.body.removeChild(video);

                // base 64图片转 blob 预览
                const blobPic = window.URL.createObjectURL(blobRes);
                resolve({
                    dataUri: res,
                    blobPic,
                    transType: 'base64',
                    size: blobRes?.size,
                    type: blobRes?.type || '',
                    file: base64toFile(res, 'blobRes?.type')
                });
            };

            video.onabort = () => {
                video && document.body.removeChild(video);
                rej();
            };
        } catch (err) {
            console.error('getFirstFrameOfVideo 出错：', err);
            rej(err);
        }
    });
};
