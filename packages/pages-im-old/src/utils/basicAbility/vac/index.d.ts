/**
 *
 * @description 人机防作弊相关
 * @link：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/bYw-fpFT3G/-2LebgzicE/5RKoCjCo-gza-E
 */

export interface HumanVerificationKey {
    ds: string;
    tk: string;
}

export interface HumanVerificationParams {
    nodeId: HTMLElement | null;
    type: 'puzzle' | 'click' | 'spin'; // 目前支持 puzzle(滑块)、click(点选)、spin(转图)
    hasClose?: boolean; // 是否有关闭按钮
    closeCallback?: () => void; // 点击关闭按钮的回调，在执行关闭弹窗方法前执行，通过return true可以中断默认的关闭方法
    errorCallback?: () => void;
    verifyFailCallback?: () => void;
    initApiSuccessCallback?: () => void;
    verifySuccessCallback?: (d: HumanVerificationKey) => void;
}

export interface VacInstance {
    init: () => void;
    hide: () => void;
    show: () => void;
    close: () => void;
    refresh: () => void;
    getDataAsync: () => Promise<HumanVerificationKey>;
    updateRender: () => void; // 相关枚举暂未补充，详见相关文档
}
