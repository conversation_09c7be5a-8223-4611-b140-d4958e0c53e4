import { authorize, getSetting, openSetting } from '@tarojs/taro';

import { showToast } from '../../customShowToast';

/**
 * 获取系统权限
 * @param {*} scopeStr // 'scope.record' // 权限列表 https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/authorize.html
 **/

export function checkPermissions(scopeStr) {
    return new Promise((resolve, reject) => {
        getSetting({
            success: async res => {
                const authSettingObj = res.authSetting;
                if (authSettingObj[scopeStr] === true) {
                    // 当前已经有权限了
                    return resolve(true);
                } else if (authSettingObj[scopeStr] === false) {
                    // 之前问过权限，但是拒绝了 打开setting
                    try {
                        await openSetting(scopeStr);

                        return resolve(true);
                    } catch {
                        return reject(false);
                    }
                } else {
                    // 还没问过权限 去请求授权
                    authorize({
                        scope: scopeStr,
                        success: () => {
                            return resolve(true);
                        },
                        fail: async () => {
                            // 拒绝授权了
                            // 打开setting
                            try {
                                await openSetting(scopeStr);

                                return resolve(true);
                            } catch {
                                return reject(false);
                            }
                        }
                    });
                }
            }
        });
    });
}

/**
 * 获取系统权限方法
 * 适用于业务侧的授权
 */

export function checkAuth(scopeStr: string, successCal?: () => void, failCal?: () => void) {
    return new Promise(resolve => {
        getSetting({
            success: async res => {
                const authSettingObj = res.authSetting;
                if (!!authSettingObj[scopeStr] === true) {
                    // 当前已经有权限了
                    successCal && successCal();

                    return resolve(true);
                } else if (!!authSettingObj[scopeStr] === false) {
                    authorize({
                        scope: scopeStr,
                        success: () => {
                            // 授权成功
                            return resolve(true);
                        },
                        fail: async () => {
                            // 拒绝授权了
                            // 打开setting
                            if (process.env.TARO_ENV === 'swan') {
                                try {
                                    await openSetting();
                                    failCal && failCal();
                                } catch {
                                    failCal && failCal();
                                } finally {
                                    // eslint-disable-next-line
                                    return resolve(false);
                                }
                            } else {
                                showToast({
                                    title: '请在设置中开启权限或重启小程序以获取授权',
                                    icon: 'none'
                                });

                                return resolve(false);
                            }
                        }
                    });
                }

                return resolve(false);
            }
        });
    });
}
