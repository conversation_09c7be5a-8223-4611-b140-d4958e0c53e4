/**
 * @description: 判断title
 * @return {Boolean}
 */
import {getSfRef, isWxMiniProgramWebView, versionCompare} from '@baidu/vita-utils-shared';

import {getSystemInfo} from './taro/get_system_info';

export const getTitle = () => {
    let title = '';
    if (process.env.TARO_ENV === 'h5') {
        if (getSfRef() === 'tg_weixin_cqjk' && isWxMiniProgramWebView()) {
            title = '赤雀健康';
        } else {
            title = '百度健康';
        }
    } else if (process.env.TARO_ENV === 'swan') {
        title = '百度健康';
    }

    return title;
};

export const validateIsAndrodSwan = () => {
    const systemInfo = getSystemInfo();
    let android = false;
    if (
        (systemInfo?.system && systemInfo?.system?.includes('Android')) ||
        systemInfo?.system?.includes('android')
    ) {
        android = true;
    }
    return process.env.TARO_ENV === 'swan' && android;
};

export const fitLowSwanSystem = (fitVersion: string) => {
    const systemInfo = getSystemInfo();
    const {version = '*********'} = systemInfo;
    if (versionCompare(version, fitVersion) > -1 && validateIsAndrodSwan()) {
        return true;
    }
    return false;
};

export const preloadSwanPackage = (
    {
        appKey = 'VlKQRMSyT32ln2AG84dmTjW6qldpGsNk',
        pageUrl = '/wenzhen/pages/triage/index'
    }
) => {
    if (process.env.TARO_ENV === 'swan') {
        if (typeof swan.preloadPackage === 'function') {
            try {
                swan.preloadPackage({
                    appKey,
                    pageUrl,
                    success: () => {
                        console.info(`load小程序页面${pageUrl}成功`);
                    },
                    fail: () => {
                        console.error(`load小程序页面${pageUrl}失败`);
                    }
                });
            } catch (e) {
                console.error(e);
            }
        }
    }
};
