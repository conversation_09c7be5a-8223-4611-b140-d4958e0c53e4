import { WX_APP_SIDS } from '../../../constants/common';

export const getCommonSysInfo = () => {
    if (wx.getExptInfoSync) {
        // 获取实验分组中的参数
        const wxSidMap = wx.getExptInfoSync(WX_APP_SIDS);
        const wxSids = Object.values(wxSidMap);
        // 根据现有规则拼接sid
        const wxSidStr = wxSids.join('_');
        const weappSid = { sid: wxSidStr || '' };

        return weappSid;
    }

    return '';
};
