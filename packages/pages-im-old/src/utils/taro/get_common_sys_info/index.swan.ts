/* eslint-disable no-undef */
import globalData from '../../../globalDataStore/globalData';
import {ubcCommonViewSend} from '../../generalFunction/ubc';

export const getCommonSysInfo = () =>
    new Promise(resolve => {
        let {baidu_id = '', cuid = '', sid = ''} = globalData.get('deviceIds') || {};
        if (baidu_id) {
            resolve({
                baidu_id,
                cuid,
                sid
            });
        } else {
            swan.getCommonSysInfo({
                success(res) {
                    baidu_id = res.baidu_id || '';
                    sid = res.sid || '';
                    cuid = res.cuid || '';
                    globalData.set('deviceIds', {
                        baidu_id,
                        sid,
                        cuid
                    });
                    // 判断cuid格式有问题，进行上报，判断链路问题
                    if (!res.cuid || /[_-]/.test(res.cuid)) {
                        // 上报
                        ubcCommonViewSend({
                            value: 'cuid_format_error',
                            ext: {
                                cuid: res.cuid || ''
                            }
                        });
                    }
                    resolve({
                        baiduid: baidu_id,
                        sid,
                        cuid
                    });
                },
                fail() {
                    resolve({
                        baiduid: '',
                        sid: ''
                    });
                }
            });
        }
    });
