import Taro from '@tarojs/taro';
import store from '../../../globalDataStore/index';
import { SystemInfoProps } from './index.d';

export const getSystemInfo = (): SystemInfoProps => {
    const hdheader = store.get('hdheader') || false;
    const curBarHeight = hdheader ? 0 : 44;

    const storeSystemInfo = store.get('systemInfo');
    if (storeSystemInfo && curBarHeight === storeSystemInfo.barHeight) {
        return storeSystemInfo;
    }

    const sysInfo = Taro.getSystemInfoSync();

    const info = {
        ...sysInfo,
        navigationBarHeight: 44,
        bottomSafeAreaHeight: 0, // h5获取不到此数据
        barHeight: curBarHeight,
        statusBarHeight: 0 // h5获取不到此数据
    };
    store.set('systemInfo', info);

    return info;
};
