import Taro from '@tarojs/taro';
import globalData from '../../../globalDataStore/globalData';
import { SystemInfoProps } from './index.d';

export const getSystemInfo = (): SystemInfoProps => {
    if (globalData.get('systemInfo')) {
        return globalData.get('systemInfo');
    }

    const sysInfo = Taro.getSystemInfoSync();
    const menuRect = Taro.getMenuButtonBoundingClientRect();
    const { safeArea = {}, screenHeight, statusBarHeight = 0 } = sysInfo;
    const navigationBarHeight = (menuRect.top - statusBarHeight) * 2 + menuRect.height;
    const bottomSafeAreaHeight = screenHeight - safeArea.bottom;

    const info = {
        ...sysInfo,
        navigationBarHeight,
        bottomSafeAreaHeight,
        barHeight: navigationBarHeight + statusBarHeight
    };
    globalData.set('systemInfo', info);

    return info;
};
