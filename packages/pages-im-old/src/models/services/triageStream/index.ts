import {API_HOST} from '../../../models/apis/host';
import {
    getTriageStreamCore,
    getUseraction,
    getOtherData,
    getUserBizAction,
    getSessionMsgList
} from '../../apis/vtui';

import httpRequest from '../../../utils/basicAbility/comonRequest/cui';
import type {SessionId} from '../../../store/triageStreamAtom/index.type.ts';

import type {
    GetTriageStreamCoreRespType,
    GetTriageStreamCoreParamsType,
    GetUseractionParamsType,
    CommonBizActionType,
    WzBizActionType,
    GetUseractionRespType,
    GetUserBizActionRespType,
    GetOtherDataRespType,
    GetUserBizActionParamsType,
    GetSessionMsgListParamsType,
    GetSessionMsgListRespType
} from './index.d';
/**
 *  获取诊前 Stream 页面核心数据V2
 *
 * @param {GetTriageStreamCoreParamsType} params 请求参数
 * @returns {GetTriageStreamCoreRespType} 返回请求的结果
 */
export const getTriageStreamCoreReq = (params: GetTriageStreamCoreParamsType) => {
    return httpRequest<GetTriageStreamCoreRespType>({
        url: `${API_HOST}${getTriageStreamCore}`,
        method: 'POST',
        data: params,
        isFirstScreen: false,
        needTransparentWhiteListParams: true
    });
};

/**
 *  获取用户行为操作后的卡片数据
 *
 * @param {GetUseractionParamsType} params 请求参数
 * @returns {GetUseractionRespType} 返回请求的结果
 */
export const getUseractionReq = <T extends CommonBizActionType>(
    params: GetUseractionParamsType<T>
) => {
    return httpRequest<GetUseractionRespType<T>>({
        url: `${API_HOST}${getUseraction}`,
        method: 'POST',
        data: params,
        isFirstScreen: false,
        needTransparentWhiteListParams: true
    });
};

/**
 *  获取用户行为操作后的卡片数据
 *
 * @param {GetUseractionParamsType} params 请求参数
 * @returns {GetUseractionRespType} 返回请求的结果
 */
export const getUserBizActionReq = <T extends WzBizActionType>(
    params: GetUserBizActionParamsType<T>
) => {
    return httpRequest<GetUserBizActionRespType<T>>({
        url: `${API_HOST}${getUserBizAction}`,
        method: 'POST',
        data: params,
        isFirstScreen: false,
        needTransparentWhiteListParams: true
    });
};

export const getOtherDataReq = (params: {sessionId: SessionId}) => {
    return httpRequest<GetOtherDataRespType>({
        url: `${API_HOST}${getOtherData}`,
        method: 'POST',
        data: params,
        isFirstScreen: false,
        needTransparentWhiteListParams: true
    });
};

/**
 * 获取会话消息列表
 *
 * @param {GetSessionMsgListParamsType} params 请求参数
 * @returns {GetSessionMsgListRespType} 返回
 */
export const getSessionMsgListReq = (params: GetSessionMsgListParamsType) => {
    return httpRequest<GetSessionMsgListRespType>({
        url: `${API_HOST}${getSessionMsgList}`,
        method: 'POST',
        data: params,
        isFirstScreen: false,
        needTransparentWhiteListParams: true
    });
};
