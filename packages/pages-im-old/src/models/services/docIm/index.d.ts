import type {MsgId, InteractionInfo, Qid, InteractionType} from '../../../typings';

import type {
    MsgItemType,
    SessionId,
    CapsulesToolsType,
    UserDataType,
    InputDataType,
    WxLoginData,
    StatusDataType,
    GetMsgListTransParams
} from '../../../store/docImAtom/index.type';

export interface GetTriageStreamCoreParamsType {
    riskInfo?: unknown;
    sessionKey?: string;
    sessionId?: string;
}

export interface AgreementDataType {
    list: {
        type: 'highLightText' | 'text';
        value: string;
        interaction: string;
        interactionInfo: InteractionInfo;
    }[][];
}

export interface GetTriageStreamCoreRespType {
    toolData: {
        capsules: {
            list: CapsulesToolsType[];
            md5: string;
        };
    };
    msgIds?: MsgId[];
    msgData?: {
        [k in MsgId]: MsgItemType<unknown>;
    };
    session: {
        sessionId: SessionId;
        isPullHistory?: boolean;
        hasMorePreMsg?: boolean;
        stopConversation?: boolean;
    };
    sessionData?: {
        control?: WxLoginData;
    };
    userData: {
        avatar: string;
        isLogin: boolean;
        name: string;
    };
    inputData: InputDataType;
    imActionData?: {
        actionData: {
            actionType: 'antipass';
            interaction: 'popup';
            interactionInfo: InteractionInfo;
        };
    };
    // 需要给后端带的数据，用于透传到后端
    transData?: GetMsgListTransParams;
    titleData?: {
        title: string;
    };
    bottomTips?: {
        agreement?: AgreementDataType;
    };
}

interface PatientInfo {
    contactId?: string;
    gender?: string;
    age?: string;
    telphone?: string;
}

interface SwitchCouponInfo {
    msgId: MsgId;
    couponId: number;
}

export interface EditedZhusuInfo {
    msgId: MsgId;
    patientInfo: PatientInfo;
    zhusu?: string;
    telPhone?: string;
    couponId?: string;
    skuId?: string;
}

interface StopConvInfoType {
    msgId: MsgId;
    eventId: string;
    reason: string;
}

interface ChatData {
    expertId?: number;
    sessionId: SessionId;
    qid?: Qid;
    content?: string;
    chatType?: string;
    provCode?: string;
    cityCode?: string;
}

export interface GetUseractionParamsType<T extends BizActionType> {
    chatData: ChatData;
    bizActionType: T;
    bizActionData: RequiredBizActionData[T];
}

export type BizActionType =
    | 'stopConv'
    | 'attitude'
    | 'capsuleClick'
    | 'userLogin'
    | 'editZhusu'
    | 'switchCoupon';

export interface RequiredBizActionData {
    stopConv: {stopConvInfo: StopConvInfoType};
    attitude: {
        voteMsgInfo: {
            msgId: MsgId;
            attitude: string;
            reason: string;
            content: string;
        };
    };
    capsuleClick: {
        clickCapsuleInfo: {
            msgKey: MsgId;
            content: string;
        };
    };
    userLogin: {
        userLoginInfo?: userInfoItem;
        userLoginInfoList?: {msgList: userInfoItem[]};
    };
    editZhusu: {editZhusuInfo: EditedZhusuInfo};
    switchCoupon: {switchCouponInfo: SwitchCouponInfo};
}

type commonData = {
    chatData: ChatData;
    userData: UserDataType;
    toolData?: {
        capsules: {
            list: CapsulesToolsType[];
            md5: string;
        };
    };
    statusData: StatusDataType;
};

type MessageAndData = commonData & {
    message: MsgItemType<unknown>[];
    toast?: string;
};

export type GetUseractionRespType<T extends BizActionType> = T extends 'stopConv' | 'attitude'
    ? commonData // 只包含 chatData 和 userData
    : MessageAndData;

export type WzBizActionType =
    | 'stopConv' // 打断
    | 'capsuleClick' // 点击胶囊
    | 'userLogin' // 登录
    | 'editZhusu' // 编辑主诉
    | 'switchCoupon'; // 切换优惠券

export type GetUserBizActionRespType<T extends WzBizActionType> = T extends 'stopConv'
    ? commonData
    : MessageAndData;

// 获取跳转微信 登录会话入参
export interface GetWxLoginSessionParams {
    url?: string;
    params?: {
        msgId?: string;
        docId?: string;
        sessionId?: string;
        expertId?: string;
    };
}

// 获取跳转微信 登录会话返回值
export interface GetWxLoginSessionRes {
    sessionData?: {
        control?: {
            gotoWxLogin: number;
            gotoWxLoginAction: {
                interaction: InteractionType;
                interactionInfo: InteractionInfo;
            };
        };
    };
}

// 诊前历史消息入参
export interface GetAiMsgHistoryParams {
    sessionId?: string;
    expertId?: number;
    docId?: string;
    size?: number;
    viewType?: string;
    transData?: GetMsgListTransParams;
    currentMsgId?: MsgId;
}

// 诊前历史消息返回值
export interface GetAiMsgHistoryResp {
    msgIds?: MsgId[];
    msgData?: {
        [k in MsgId]: MsgItemType<unknown>;
    };
    hasMore?: boolean;
    transData?: GetMsgListTransParams;
}

export interface userInfoItem {
    msgId: MsgId;
    patientInfo?: PatientInfo;
}

export interface GetUserBizActionParamsType<T extends WzBizActionType> {
    chatData: ChatData;
    bizActionType: T;
    bizActionData: RequiredBizActionData[T];
}
