import type {MsgItemType, SessionId} from '../../../../store/triageStreamAtom/index.type.ts';
import type {InteractionType, InteractionInfo} from '../../../../typings/index.d';

// 问卷类型
export const enum TriggerType {
    // 1 弹窗问卷
    TYPE_1 = 1,
    // 2 IM消息问卷
    TYPE_2 = 2
}

// 问卷渲染类型
export const enum NpsRenderType {
    // 1 普通类型 10分制
    TYPE_1 = 1,
    // 2 表情类型，10分制
    TYPE_2 = 2
}

// 获取 nps info的入参
export interface NpsInfoParams {
    sessionId?: SessionId;
    triggerType?: TriggerType;
    appraiseIds?: string;
}

// nps info的返回值
export interface NpsInfoResponse {
    // 是否有nps： 0不存在 1存在
    hasNps: 0 | 1;
    // 问卷id，hasNps=1才有
    appraiseId?: string;
    // 问卷类型
    triggerType?: TriggerType;
    // 弹窗问卷
    npsModel?: NpsModel;
    // IM消息问卷
    npsMsg?: NpsMsg;
}

// 弹窗问卷
export interface NpsModel {
    title?: string;
    titleDesc?: string;
    npsRenderType?: NpsRenderType;
    userResponse: UserResponseProps[];
    scaleConfiguration: ScaleConfiguration[];
    surveyInfo: SurveyInfo;
    btn: Btn;
}

// 按钮信息
export interface Btn {
    text: string;
    interaction?: InteractionType;
    interactionInfo?: InteractionInfo;
}

// 评分配置
export interface ScaleConfiguration {
    scoreShow: ScoreShow[];
    configRation: ConfigRation;
}

export interface ConfigRation {
    promptMessage?: string;
    tags?: string[];
    isTagRequired?: number;
    customInput?: CustomInput;
}

// 输入框
export interface CustomInput {
    isRequired?: 0 | 1;
    placeholder?: string;
    maxLen?: number;
}

// 评分项value
export type ScoreItemValue = string;

// 评分项
export interface ScoreShow {
    iconUrl?: string;
    selectedIconUrl?: string;
    text?: string;
    value?: ScoreItemValue;
}

// 用户已提交的问卷信息
export interface SurveyInfo {
    rating?: string;
    tags?: string[];
    customInput?: string;
}

// 左右边项
export interface UserResponseProps {
    icon?: string;
    label?: string;
}

export interface NpsMsg {
    msgId?: string[];
    msgData?: Record<string, MsgItemType<unknown>>;
}
