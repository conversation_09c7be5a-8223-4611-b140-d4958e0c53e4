import type {SessionId} from '../../../../store/triageStreamAtom/index.type.ts';
import type {InteractionType, InteractionInfo} from '../../../../typings/im.d.ts';
import type {ScoreItemValue} from '../getNpsInfo/index.d';

// 提交nps的入参
export interface SubmitNpsParams {
    // 选中的分数
    score?: ScoreItemValue;
    // 1 弹窗；2 IM消息
    triggerType?: 1 | 2;
    // 用户选择的标签
    tags?: string[];
    // 用户输入内容
    input?: string;
    // 问卷id
    appraiseId: string;
    // 消息id
    msgId?: string;
    sessionId?: SessionId;
}

// 提交nps的返回值
export interface SubmitNpsResponse {
    // 是否已过期
    hasExpired?: number;
    // 已过期文案
    expiredDesc?: string;
    // 确认弹窗文案反馈
    popupDesc?: string;
    // 百度长问卷
    baiduQuestionnairBtnInfo?: BaiduQuestionnairBtnInfo;
    // 用户提交成功后卡片渲染字段
    cardSuccessInfo?: CardSuccessInfo;
}

// 百度长问卷按钮信息
export interface BaiduQuestionnairBtnInfo {
    value?: string;
    interaction?: InteractionType;
    interactionInfo?: InteractionInfo;
}

// 用户提交成功后卡片渲染字段
export interface CardSuccessInfo {
    iconUrl?: string;
    text?: string;
}
