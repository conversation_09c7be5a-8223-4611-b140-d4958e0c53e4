/**
 * @file 获取nps信息
 * <AUTHOR>
 */
import httpRequest from '../../../../utils/basicAbility/comonRequest/cui';
import {API_HOST} from '../../../../models/apis/host';

import type {SubmitNpsParams, SubmitNpsResponse} from './index.d';

/**
 * 获取对话列表
 * @param params - 请求参数
 * @returns Promise<TalkListResponse>
 */
export const submitNpsInfo = (params: SubmitNpsParams) => {
    return httpRequest<SubmitNpsResponse>({
        url: `${API_HOST}/vtui/nps/submit`,
        method: 'POST',
        data: params,
        isNeedLogin: false,
        isFirstScreen: false,
        needTransparentWhiteListParams: true
    });
};
