import {uploadFile} from '@tarojs/taro';

import {API_HOST} from '../../apis/host';
import {getBucketToken, uploadImageApi} from '../../apis/vtui';

import httpRequest from '../../../utils/basicAbility/comonRequest/common';

interface IReqConfProps {
    url: string;
    method?: 'GET' | 'POST';
    // 超时时间
    timeout?: number;
    // 是否需要登录
    isNeedLogin?: boolean;
    // 是否为首屏数据, 主要是用于首屏打点
    isFirstScreen?: boolean;
    // 是否是预下载请求
    isPrefetch?: boolean;
    // 是否页面内置未登录模块
    isInnerLogin?: boolean;
    header?: {[key: string]: string};
    data?: {[key: string]: string | number | object | boolean | unknown};
    cb?: () => void;
}

export interface IBucketDataProps {
    accessKeyID?: string;
    secretAccessKey?: string;
    sessionToken?: string;
    bucket?: string;
    endPoint?: string;
    fileNames?: string[];
    snapshotURLs?: string[];
    mapFileUrls?: Record<string, Record<'icon' | 'origin' | 'small', string>>;
}

export const getUseprotocolDetail = async (conf: IReqConfProps) => {
    return await httpRequest<{verified: number}>(conf);
};

/**
 *
 * @description 获取上传图片token
 * @param params
 * @returns
 */
export const getBosToken = async (params: {bucketConfName?: string; fileNum?: number}) => {
    return await httpRequest<IBucketDataProps>({
        url: `${API_HOST}${getBucketToken}`,
        method: 'POST',
        data: params,
        isNeedLogin: false,
        isFirstScreen: false
    });
};

export const uploadPicToApiService = async (filePath: string, bucketConfName = 'default') => {
    return new Promise((resolve, reject) => {
        uploadFile({
            url: `${API_HOST}${uploadImageApi}?bucketConfName=${bucketConfName}`,
            filePath,
            name: 'file',
            success: res => {
                resolve(res?.data);
            },
            fail: err => {
                reject(err);
            }
        });
    });
};
