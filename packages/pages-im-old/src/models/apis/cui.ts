/**
 *
 * <AUTHOR>
 * @link https://iapi.baidu-int.com/web/project/356132/apis/api-4590249
 * @description 获取诊前 Stream 页面核心数据V2
 */
export const getTriageStreamCore = '/wzcui/uiservice/zhenqian/frame/getCoreDataV2';

/**
 *
 * <AUTHOR>
 * @link https://iapi.baidu-int.com/web/project/356132/apis/api-4590249
 * @description 诊前 Stream 页面创建会话接口，SSE 流式传输
 */
export const createTriageStreamConversation = '/wzcui/uiservice/zhenqian/zhusu/conversation';

/**
 *
 * <AUTHOR>
 * @link https://iapi.baidu-int.com/web/project/356132/apis/api-4590249
 * @description 用户行为
 */

export const getUseraction = '/wzcui/uiservice/zhenqian/zhusu/useraction';

/**
 * <AUTHOR>
 * @link https://iapi.baidu-int.com/web/project/356132/apis/api-4652322
 * @description 获取历史消息记录
 */
export const getAiMsgHistory = '/wzcui/uiservice/zhenqian/zhusu/aimsghistory';

/**
 *
 * <AUTHOR>
 * @link https://iapi.baidu-int.com/web/project/356132/apis/api-4590249
 * @description 用户业务行为
 */
export const getUserBizAction = '/vtui/useraction/bizaction';
