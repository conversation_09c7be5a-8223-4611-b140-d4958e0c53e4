// 诊前tab接口
export const filterTabs = '/wz/api/uiservice/zhenqian/filter_tabs';
// 义诊医生列表接口
export const yizhenDoctor = '/wz/api/uiservice/zhenqian/expert_list_yizhen';

// 优惠券列表接口
export const couponList = '/wz/operate/coupondiscount';
// 获取打赏信息接口
export const getRewardInfo = '/wzcui/uiservice/common/reward/getForm';

// 领取优惠卷
export const getCouponsByCode = '/wz/operate/sentcoupon';
// 定向服务列表算券
export const calcCoupons = '/wzcui/uiservice/zhenqian/zhusu/calcCoupons';
// 获取评价信息
export const getComment = '/wzcui/uiservice/zhenzhong/changjinghua/getComment';
// 获取医生拉黑患者信息
export const getPatientBlock = '/wzcui/uiservice/common/user/blacklist';

// 根据触发点请求发券码
export const getPlanedCouponCode = '/wz/api/uiservice/zhenqian/plantrigger';
// 就诊人信息详情接口
export const patientInfoDetail = '/rxui/medicalrecord/treatinfo';

// 消息退场接口
export const homeMsgUpdate = '/harbor/msg/effectStatus/update';

// 计算优惠券结果
export const calcFormCoupons = '/wzcui/uiservice/zhenqian/order/calcCoupons';

/** 待支付页面相关接口 */
// 待支付消息列表
export const chatMsgList = '/med/page/chat';
export const commitPrePay = '/abot/api/v1/tpl/commit';
export const refreshPrePay = '/med/api/refresh';
