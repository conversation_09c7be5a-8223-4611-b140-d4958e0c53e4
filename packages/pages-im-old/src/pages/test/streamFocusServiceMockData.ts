export const streamFocusServiceData = {
    data: {
        "cardStyle": {
            "renderType": 1
        },
        "content": {
            "skuData": {
                "expertId": 1630815,
                "topDes": {
                    "title": "向张宇医生咨询",
                    "desc": [
                        {
                            "type": "text",
                            "value": "认证医生",
                            "interactionInfo": {
                                "content": {
                                    "sloganUrl": "",
                                    "serviceIcon": "",
                                    "list": null,
                                    "tips": ""
                                }
                            }
                        },
                        {
                            "type": "text",
                            "value": "未接诊可退",
                            "interactionInfo": {
                                "content": {
                                    "sloganUrl": "",
                                    "serviceIcon": "",
                                    "list": null,
                                    "tips": ""
                                }
                            }
                        },
                        {
                            "type": "text",
                            "value": "不满意可申诉",
                            "interactionInfo": {
                                "content": {
                                    "sloganUrl": "",
                                    "serviceIcon": "",
                                    "list": null,
                                    "tips": ""
                                }
                            }
                        },
                        {
                            "type": "icon",
                            "value": "https://med-fe.cdn.bcebos.com/wz/card/service-title.png",
                            "interaction": "popup",
                            "interactionInfo": {
                                "content": {
                                    "sloganUrl": "https://med-fe.cdn.bcebos.com/wz/card/logo.png",
                                    "serviceIcon": "https://med-fe.cdn.bcebos.com/wz/card/service-logo.png",
                                    "list": [
                                        {
                                            "icon": "https://med-fe.cdn.bcebos.com/wz/card/icon-doctor.png",
                                            "title": "认证医生",
                                            "desc": "百度健康认证医生，真实有保障"
                                        },
                                        {
                                            "icon": "https://med-fe.cdn.bcebos.com/wz/card/icon-cure.png",
                                            "title": "未接诊可退",
                                            "desc": "医生接诊前，可随时发起退款"
                                        },
                                        {
                                            "icon": "https://med-fe.cdn.bcebos.com/wz/card/icon-refund.png",
                                            "title": "不满意可申诉",
                                            "desc": "医生服务不满意，可随时向平台申诉退款"
                                        }
                                    ],
                                    "tips": "问题建议通过百度APP \\u003e 百度健康 \\u003e 投诉反馈告诉我们"
                                }
                            }
                        }
                    ]
                },
                "skuList": [
                    {
                        "isSelected": "",
                        "skuId": "200001",
                        "skuType": 0,
                        "icon": "https://med-fe.cdn.bcebos.com/wz-mini/skuHook/skuIconTuWen.png",
                        "title": "图文咨询",
                        "formType": 1,
                        "subtitle": "24小时内不限次沟通 随时提问",
                        "label": "",
                        "description": [
                            "图文咨询为医生团队服务，将由医生团队成员共同为您提供高效服务"
                        ],
                        "couponInfo": {},
                        "skuDetail": {
                            "rights": [
                                {
                                    "type": "text",
                                    "value": "适用于想要详细咨询病情的患者"
                                },
                                {
                                    "type": "text",
                                    "value": "建议充分准备就诊资料，沟通更高效"
                                }
                            ],
                            "rules": [
                                {
                                    "type": "text",
                                    "value": "问诊规则：支付后需等待医生接诊，接诊后交流通道开启并开始计时；医生和患者每相互回复一次记为一轮，时长或轮次到达上限时，服务结束"
                                },
                                {
                                    "type": "text",
                                    "value": "退款规则：医生首次回复前，可随时取消，自动退款；支付后24小时内医生未接诊，系统自动退款"
                                },
                                {
                                    "type": "text",
                                    "value": "温馨提示：支付后会及时通知医生，如遇医生临床工作较忙，留言后请耐心等待回复"
                                }
                            ],
                            "platformFeeDesc": null
                        },
                        "priceInfo": {
                            "salePrice": 7500,
                            "promotionPrice": 7500,
                            "totalReductionPrice": 0,
                            "totalPrice": 7500,
                            "platformFee": 0
                        },
                        "btnInfo": {
                            "type": "text",
                            "value": "去咨询",
                            "interaction": "payment",
                            "interactionInfo": {
                                "url": "/wz/order/makeorder?coupon_select=-1&ctrlPreferPreTriage=1&entrance=triage_expert_card&expert_id=1630815&isDirected=1&qid=1440425627250691&sku_id=1865740336498688"
                            }
                        },
                        "skuRightsDes": null
                    },
                    {
                        "isSelected": "",
                        "skuId": "200010",
                        "skuType": 0,
                        "icon": "https://med-fe.cdn.bcebos.com/wz-mini/skuHook/skuIconDianHua.png",
                        "title": "电话咨询",
                        "formType": 2,
                        "subtitle": "10分钟通话 送图文 更高效",
                        "label": "推荐",
                        "description": [
                            "支付后等待医生回电，接诊后##赠送3次图文回复##"
                        ],
                        "couponInfo": {},
                        "skuDetail": {
                            "rights": [
                                {
                                    "type": "text",
                                    "value": "可与医生1对1通话10分钟"
                                },
                                {
                                    "type": "text",
                                    "value": "适用于想要短时间内高效咨询的患者"
                                },
                                {
                                    "type": "text",
                                    "value": "医生接诊后赠送3次图文回复机会，7天内有效"
                                }
                            ],
                            "rules": [
                                {
                                    "type": "text",
                                    "value": "问诊规则：支付后需等待医生确认通话时间，将以短信方式通知您"
                                },
                                {
                                    "type": "text",
                                    "value": "退款规则：医生通话前，可随时取消，自动退款；支付后48小时内医生未通话，系统自动退款"
                                },
                                {
                                    "type": "text",
                                    "value": "温馨提示：支付后会及时通知医生，如遇医生临床工作较忙，请耐心等待"
                                }
                            ],
                            "platformFeeDesc": null
                        },
                        "priceInfo": {
                            "salePrice": 25000,
                            "promotionPrice": 25000,
                            "totalReductionPrice": 0,
                            "totalPrice": 25000,
                            "platformFee": 0
                        },
                        "btnInfo": {
                            "type": "text",
                            "value": "去咨询",
                            "interaction": "request",
                            "interactionInfo": {
                                "url": "/wzcui/uiservice/zhenqian/zhusu/checkDirectPhone?coupon_select=-1&ctrlPreferPreTriage=1&entrance=triage_expert_card&expert_id=1630815&isDirected=1&qid=1440425627250691&sku_id=1870688138823680"
                            }
                        },
                        "skuRightsDes": null
                    },
                    {
                        "isSelected": "",
                        "skuId": "200059",
                        "skuType": 0,
                        "icon": "https://med-fe.cdn.bcebos.com/wz-mini/ImVideoCall/videoSkuLogo.png",
                        "title": "视频咨询",
                        "formType": 3,
                        "subtitle": "30分钟通话 面对面视频交流",
                        "label": "",
                        "couponInfo": {},
                        "skuDetail": {
                            "rights": [
                                {
                                    "type": "text",
                                    "value": "可与医生视频通话30分钟"
                                },
                                {
                                    "type": "text",
                                    "value": "适合需要医生观察病情或患处的情况"
                                },
                                {
                                    "type": "text",
                                    "value": "医患交流通道最长为48小时"
                                }
                            ],
                            "rules": [
                                {
                                    "type": "text",
                                    "value": "问诊规则：医生接诊后，交流通道开启；视频通话由医生侧发起，患者接听，患者可以通过文字沟通与医生约定或修改视频时间"
                                },
                                {
                                    "type": "text",
                                    "value": "退款规则：医生接诊前，可随时取消订单，系统自动退款；医生接诊后，48小时内未接通视频，系统自动退款"
                                },
                                {
                                    "type": "text",
                                    "value": "温馨提示：支付后会及时通知医生，如遇医生临床工作较忙，请耐心等待；建议患者提前补充资料，咨询更高效"
                                }
                            ],
                            "platformFeeDesc": null
                        },
                        "priceInfo": {
                            "salePrice": 37500,
                            "promotionPrice": 37500,
                            "totalReductionPrice": 0,
                            "totalPrice": 37500,
                            "platformFee": 0
                        },
                        "btnInfo": {
                            "type": "text",
                            "value": "去咨询",
                            "interaction": "payment",
                            "interactionInfo": {
                                "url": "/wz/order/makeorder?coupon_select=-1&ctrlPreferPreTriage=1&entrance=triage_expert_card&expert_id=1630815&isDirected=1&qid=1440425627250691&sku_id=1897626173704192"
                            }
                        },
                        "skuRightsDes": null
                    },
                    {
                        "isSelected": "",
                        "skuId": "200023",
                        "skuType": 0,
                        "icon": "https://med-fe.cdn.bcebos.com/wz-mini/skuHook/skuIconZhuanJia.png",
                        "title": "专家月卡",
                        "formType": 1,
                        "subtitle": "30天不限次沟通 适合慢病复诊",
                        "label": "",
                        "description": [
                            "专家月卡为医生团队服务，将由医生团队成员共同为您提供长期服务"
                        ],
                        "couponInfo": {},
                        "skuDetail": {
                            "rights": [
                                {
                                    "type": "text",
                                    "value": "30天内不限次图文沟通"
                                },
                                {
                                    "type": "text",
                                    "value": "适用于慢病复诊和想要长期联系医生的患者"
                                }
                            ],
                            "rules": [
                                {
                                    "type": "text",
                                    "value": "问诊规则：支付后需等待医生接诊，接诊后交流通道开启并开始计时；医生和患者每相互回复一次记为一轮，时长到达上限时，服务结束"
                                },
                                {
                                    "type": "text",
                                    "value": "退款规则：医生首次回复前，可随时取消，自动退款；支付后24小时内医生未接诊，系统自动退款"
                                },
                                {
                                    "type": "text",
                                    "value": "温馨提示：支付后会及时通知医生，如遇医生临床工作较忙，留言后请耐心等待回复"
                                }
                            ],
                            "platformFeeDesc": null
                        },
                        "priceInfo": {
                            "salePrice": 62500,
                            "promotionPrice": 62500,
                            "totalReductionPrice": 0,
                            "totalPrice": 62500,
                            "platformFee": 0
                        },
                        "btnInfo": {
                            "type": "text",
                            "value": "去咨询",
                            "interaction": "payment",
                            "interactionInfo": {
                                "url": "/wz/order/makeorder?coupon_select=-1&ctrlPreferPreTriage=1&entrance=triage_expert_card&expert_id=1630815&isDirected=1&qid=1440425627250691&sku_id=1877834964404224"
                            }
                        },
                        "skuRightsDes": null
                    }
                ]
            },
            "collectedInfo": {
                "curPatient": {
                    "contactId": "869181742367803345",
                    "name": "李静",
                    "age": "34",
                    "gender": "女",
                    "isCertified": 0
                },
                "patientList": [
                    {
                        "contactId": "869181742367803345",
                        "name": "李静",
                        "age": "34",
                        "gender": "女",
                        "isCertified": 1
                    },
                    {
                        "contactId": "869181743076495662",
                        "name": "",
                        "age": "20",
                        "gender": "男",
                        "isCertified": 0
                    },
                    {
                        "contactId": "869181742800252442",
                        "name": "",
                        "age": "20",
                        "gender": "女",
                        "isCertified": 0
                    }
                ],
                "clinicalDesc": "医生您好，我想咨询中医神经内科的一些问题",
                "telPhone": "*******5420",
                "servicePhone": "9509-2120"
            },
            "requireDesc": {
                "titleCon": [
                    {
                        "type": "icon",
                        "value": "https://med-fe.cdn.bcebos.com/wz-mini/icon-requirement.png"
                    },
                    {
                        "type": "text",
                        "value": "接诊要求"
                    }
                ],
                "descList": [
                    {
                        "title": "基本要求",
                        "desc": [
                            "不接诊怀孕患者"
                        ]
                    }
                ]
            },
            "coupon": {
                "couponInfo": null,
                "couponTips": [
                    {
                        "type": "text",
                        "value": "暂不可用"
                    }
                ],
                "userCoupons": {
                    "disableList": [
                        {
                            "id": 37596597192541,
                            "cid": 69286,
                            "startTime": 1743997854,
                            "endTime": 1744257054,
                            "current": 0,
                            "usable": 0,
                            "name": "普通咨询满减券",
                            "subTitle": "限时特惠",
                            "worth": "0.5元",
                            "description": [
                                {
                                    "list": [
                                        "1.券适用范围：优惠券仅适用于在百度健康问医生产品进行线上咨询，购买普通咨询服务使用",
                                        "2.券门槛：满3元可用",
                                        "3.券使用限制：每人仅限领取一次",
                                        "4.当使用优惠券的订单，优惠部分金额不能开具发票",
                                        "5.普通咨询服务仅在医生资源充足时可用，每日名额有限，先到先得。",
                                        "6.优惠券不能与其他优惠同时使用"
                                    ],
                                    "title": "使用须知"
                                },
                                {
                                    "list": [
                                        "1.医生回复仅为建议，如需确诊请您线下就医",
                                        "2.咨询内容：不包含高危疾病类、动植物类问题",
                                        "3.对于任何不正当方式参与活动的用户，包括但不限于侵犯第三人合法权益、作弊、扰乱系统、批量注册等违规行为，百度健康有权撤销违规交易并追回优惠。",
                                        "4.如出现不可抗力等情况，百度健康可依相关法律法规主张免责，活动最终解释权归'百度健康问医生'所有。"
                                    ],
                                    "title": "注意事项"
                                }
                            ],
                            "category": 1,
                            "maxPrice": 50,
                            "fullPrice": 51,
                            "salePrice": 50
                        }
                    ]
                }
            }
        }
    },
    msgId: '1909182073347567616'
};
