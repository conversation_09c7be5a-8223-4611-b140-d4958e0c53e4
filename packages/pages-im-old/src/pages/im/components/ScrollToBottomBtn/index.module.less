.scrollToBottomBtn {
    position: relative;
    font-size: 0;
    opacity: 0;
    z-index: -999;

    &.visible {
        opacity: 0.8;
        z-index: 90;
        animation: slide-up 300ms ease-out forwards;
    }
}

@keyframes slide-up {
    from {
        transform: translateY(30%);
        opacity: 0;
    }

    to {
        transform: translateY(0);
        opacity: 0.8;
    }
}

.scrollToBottomBtnBg {
    width: 120px;
    height: 120px;
    background-color: #fff;
    border: 1.68px solid #ededf0;
    box-shadow: 0 20.01px 69.99px 0 #1e1f2433;
    border-radius: 50%;
}

.scrollToBottomBtnIcon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 66px;
    height: 66px;
}

.scrollAnchor {
    width: 100%;

    /* prettier-ignore */
    height: 1PX;
    opacity: 0;
    z-index: -999;
}
