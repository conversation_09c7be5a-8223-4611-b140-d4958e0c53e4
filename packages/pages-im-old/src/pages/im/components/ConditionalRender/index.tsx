import {memo, type FC, type NamedExoticComponent} from 'react';

interface IProps {
    condition: boolean;
    baseComponent: NamedExoticComponent<unknown>;
    experimentComponent: NamedExoticComponent<unknown>;
}

const ConditionalRender: FC<IProps> = props => {
    const {condition = false, baseComponent, experimentComponent, ...restProps} = props;
    const ComponentToRender = condition ? experimentComponent : baseComponent;

    return <ComponentToRender {...restProps}></ComponentToRender>;
};

export default memo(ConditionalRender);
