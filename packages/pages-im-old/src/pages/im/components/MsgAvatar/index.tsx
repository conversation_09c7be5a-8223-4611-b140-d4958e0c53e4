import cx from 'classnames';
import {View} from '@tarojs/components';
import {memo, useCallback, useMemo, type FC} from 'react';

import {USER_DEFAULT_PIC} from '../../../../constants/common';
import {navigate} from '../../../../utils/basicAbility/commonNavigate';
import {useGetUserData} from '../../../../hooks/triageStream/pageDataController';
import type {MsgItemType} from '../../../../store/triageStreamAtom/index.type.ts';
import {OwnerTypeEnum, type OwnerTypeValue, type PosterInfo} from '../../../../typings/msg.type';

import styles from './index.module.less';

interface IProps {
    data: PosterInfo;
    role: OwnerTypeValue;
    posterRole: MsgItemType<unknown>['meta']['showPosterRole'];
}

/**
 * 消息头像组件
 *
 * @param props 组件属性
 * @returns 返回渲染后的 JSX 元素
 */
const MsgAvatar: FC<IProps> = props => {
    const {data, role, posterRole} = props;
    const {userData} = useGetUserData();

    const defaultAvatar =
        role === OwnerTypeEnum['需求方']
            ? USER_DEFAULT_PIC
            : 'https://med-fe.cdn.bcebos.com/wz/assistant.png';

    const avatarUrl = useMemo(() => {
        return role === OwnerTypeEnum['需求方'] ? userData?.avatar : data?.avatar;
    }, [data?.avatar, role, userData?.avatar]);

    const avatarIconUrl = useMemo(() => {
        if (role === OwnerTypeEnum['服务方']) {
            const posterRoleMap: {
                [k in MsgItemType<unknown>['meta']['showPosterRole']]?: string;
            } = {
                4: '',
                // 4: 'https://med-fe.cdn.bcebos.com/wz-mini%2FtriageStream%2FaiIcon.png',
                7: ''
                // 7: 'https://med-fe.cdn.bcebos.com/wz-mini%2FtriageStream%2FaiIcon.png'
            };

            return posterRoleMap[posterRole] || null;
        }

        return null;
    }, [posterRole, role]);

    const clickAvatar = useCallback(() => {
        if (data?.link && role === OwnerTypeEnum['服务方']) {
            navigate({
                url: data?.link,
                openType: 'navigate'
            });
        }
    }, [data?.link, role]);

    return (
        <View
            className={cx(
                styles.avatar,
                role === OwnerTypeEnum['服务方'] ? 'wz-mr-24' : 'wz-ml-24'
            )}
            style={{
                backgroundImage: `url(${avatarUrl || defaultAvatar})`
            }}
            onClick={clickAvatar}
        >
            {avatarIconUrl ? (
                <View
                    className={styles.avatarIcon}
                    style={{
                        backgroundImage: `url(${avatarIconUrl})`
                    }}
                />
            ) : null}
        </View>
    );
};

export default memo(MsgAvatar);
