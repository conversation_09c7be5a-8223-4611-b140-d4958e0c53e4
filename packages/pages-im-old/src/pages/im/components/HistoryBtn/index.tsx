import cx from 'classnames';
import {useAtomValue} from 'jotai';
import {View} from '@tarojs/components';
import {type FC, memo, useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {eventCenter} from '@tarojs/taro';

import {useGetSessionId} from '../../../../hooks/triageStream/pageDataController';
import {useMsgDataGetController} from '../../../../hooks/triageStream/dataController';
import {useInitDataController} from '../../../../hooks/triageStream/useInitDataController';
import {useScrollControl} from '../../../../hooks/common/useScrollControl';

import {triageSessionHasHistoryMsgAtom} from '../../../../store/triageStreamAtom/msg';
import {SCROLL_ANIMATION_DISABLE_ONCE} from '../../../../constants/common';

import {MsgId} from '../../../../typings';

import styles from './index.module.less';

const HistoryBtn: FC<{msgId: MsgId | undefined}> = ({msgId}) => {
    const curSessionId = useGetSessionId();
    const {getHistoryMsg: getHistoryMsgFn} = useInitDataController();
    const oldFirstMsgId = useRef<MsgId | undefined>(msgId);
    const {scrollToElement, scrollToMessage} = useScrollControl();
    const {data} = useMsgDataGetController({msgId: msgId || ''});
    const hasHistoryMsg = useAtomValue(triageSessionHasHistoryMsgAtom(curSessionId || ''));

    const [loading, setLoading] = useState(false);

    const getHistoryMsg = useCallback(async () => {
        try {
            setLoading(true);
            await getHistoryMsgFn({
                sessionId: curSessionId || '',
                currentMsgId: data?.meta?.msgId
            });
        } finally {
            setLoading(false);
        }
    }, [curSessionId, data, getHistoryMsgFn]);

    const genDom = useMemo(() => {
        if (hasHistoryMsg) {
            return (
                <View
                    className={cx(
                        styles.historyBtn,
                        'wz-fs-36 wz-fw-400 wz-mtb-42 wz-flex wz-justify-center'
                    )}
                    onClick={getHistoryMsg}
                >
                    {loading ? (
                        <View className={styles.loading} />
                    ) : (
                        <View className='wz-flex'>
                            <View className={cx(styles.historyLine, styles.leftLine)} />
                            <View className='wz-plr-27'>点击查看历史对话</View>
                            <View className={styles.historyLine} />
                        </View>
                    )}
                </View>
            );
        }
        return (
            // Tips: 为解决手百小程序 scrollView 问题，无固定元素会导致出现滚动条内容超出
            <View className={styles.emptyDom} />
        );
    }, [hasHistoryMsg, getHistoryMsg, loading]);

    // 用于加载完历史消息后的位置恢复
    useEffect(() => {
        const tempOldFirstMsgId = oldFirstMsgId.current;
        if (tempOldFirstMsgId && msgId !== tempOldFirstMsgId) {
            eventCenter.trigger(SCROLL_ANIMATION_DISABLE_ONCE, true);
            if (process.env.TARO_ENV === 'swan') {
                scrollToElement(`msgItemScrollAnchor_${tempOldFirstMsgId}`, 'historyBtn');
            } else {
                // H5场景实际位置距离预期位置向下偏移较大，所以使用 scrollToMessage 滚动到指定消息
                scrollToMessage(tempOldFirstMsgId, 'historyBtn');
            }
        }
        oldFirstMsgId.current = msgId;
    }, [msgId, scrollToElement, scrollToMessage]);

    return genDom;
};

export default memo(HistoryBtn);
