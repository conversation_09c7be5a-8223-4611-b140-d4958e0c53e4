@import url('../../../../style/variable.less');

.toolsLayout {
    position: relative;
    z-index: 98;

    .toolsContainer {
        background-color: #eff3f9;
    }

    .toolsContainerAbsoluteLayout {
        position: absolute;
        bottom: -1px; /* 避免在H5下，底部有1px的缝 */
        left: 0;
        width: 100%;
    }

    .scrollToBottomBtn {
        position: absolute;
        top: -240px;
        right: 51px;
    }
}

.toolsMain {
    position: relative;
    z-index: @zIndex-capsuleTools;
}

.fadeTransition {
    max-height: 0; /* 初始隐藏状态 */
    opacity: 0;
    width: 100%;
    box-sizing: border-box;
    transition:
        opacity 300ms ease-in-out,
        max-height 300ms ease-in-out;
}

.fadeVisible {
    opacity: 1;
    max-height: 500px; /* 设置足够大的值，确保能容纳内容 */
    transition:
        opacity 300ms ease-in-out,
        max-height 300ms ease-in-out;
}
