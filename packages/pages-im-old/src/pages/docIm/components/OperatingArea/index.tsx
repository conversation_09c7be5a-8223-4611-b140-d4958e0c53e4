import cx from 'classnames';
import {useAtomValue} from 'jotai';
import {nextTick, eventCenter} from '@tarojs/taro';
import {View} from '@tarojs/components';
import {memo, useCallback, useEffect, useMemo, useRef, useState, type FC} from 'react';

import {uploadFileToBos} from '../../../../utils/basicAbility/upload';
import {BUCKET_NAME, ONLINE_HOST} from '../../../../constants/common';

import {API_HOST} from '../../../../models/apis/host';
import TipImgUploadPopup from '../../../../components/pagePopup/TipImgUploadPopup';

import {useGetTipsData, useHandleTipsAction} from '../../../../hooks/docIm/useHandleTipsAction';
import {curSessionMsgIdsAtom} from '../../../../store/docImAtom/msg';
import {useGetUserData, useSetImageSource} from '../../../../hooks/docIm/pageDataController';
import {useGetServiceDeclareShow} from '../../../../store/docImAtom/imTools';
import {useGetImWelcomeMouleDisplayStatus} from '../../../../hooks/docIm/useImWelcomeMoule';
import {useScrollControl} from '../../../../hooks/common/useScrollControl';
import type {IPicProps} from '../../../../typings/upload';
import type {SceneTypeOfParams} from '../../../../models/services/docIm/sse/index.d';

import {useTextareaSendFocus} from './components/ImInput/hook/useTextareaSendFocus';
import {useHandleImage} from './components/ImInput/capsuleTools/imageListTools/hooks/useHandleImage';
import VTextarea from './components/ImInput';
import CapsuleTools from './components/ImInput/capsuleTools';
import ImageListTools from './components/ImInput/capsuleTools/imageListTools';

import ScrollToBottomBtn from './components/ScrollToBottomBtn';
import ServiceStatement from './components/ServiceStatement';

import styles from './index.module.less';

const bucketConfName =
    ONLINE_HOST.indexOf(API_HOST as string) > -1 ? BUCKET_NAME[2] : `${BUCKET_NAME[2]}-test`;
const count = 1;

const OperatingArea: FC = () => {
    const [isVisible, setIsVisible] = useState(false);
    const [shouldRender, setShouldRender] = useState(true);
    const capsuleRef = useRef(null);

    const tipsData = useGetTipsData();
    const showServiceDeclareShow = useGetServiceDeclareShow();
    const {isFocus} = useTextareaSendFocus();
    const {userData} = useGetUserData();
    const {setImageSource} = useSetImageSource();
    const {status: isWelcomeMouleDisplay} = useGetImWelcomeMouleDisplayStatus();
    const {open, sceneType, closeUploadPopup, openUploadPopup} = useHandleTipsAction();
    const {statusList, imgList, setImgList, setStatusList} = useHandleImage();
    const curSessionMsgIds = useAtomValue(curSessionMsgIdsAtom);
    const {scrollToBottom} = useScrollControl('docImScrollControl');
    const hasImg = useMemo(() => {
        return imgList?.length > 0;
    }, [imgList]);

    const handleSendImg = useCallback(
        async (preData: IPicProps[]) => {
            if (!preData) return;

            // TODO: 后续会支持多张图片上传，先写上@zhangzhiyu03

            // try {
            //     setImgList([...imgList,Object?.assign(preData[0],{uploadStatus: 'pending'})]);
            //     const picData =  await uploadFileToBos(preData || [], {
            //         count,
            //         bucketConfName
            //     });
            //     setImgList([...imgList,Object.assign({...picData[0]}, {uploadStatus: 'success'})]);
            // } catch (error) {
            //     console.error(error);
            //     setImgList([...imgList,Object?.assign(preData[0],{uploadStatus: 'failed'})]);
            // }

            try {
                setImgList([Object?.assign(preData[0], {uploadStatus: 'pending'})]);
                setStatusList(['pending']);
                const picData = await uploadFileToBos(preData || [], {
                    count,
                    bucketConfName
                });
                setStatusList(['success']);
                setImgList([Object.assign({...picData[0]}, {uploadStatus: 'success'})]);
            } catch (error) {
                console.error(error);
                setImgList([Object?.assign(preData[0], {uploadStatus: 'failed'})]);
                setStatusList(['failed']);
            }
        },
        [setImgList, setStatusList]
    );

    const transitionStyles = useMemo(() => {
        // 小程序下，会先唤起软键盘再出现输入框，如果此时有延时会造成闪烁，所以这里需要特殊处理
        if (process.env.TARO_ENV === 'swan') {
            return {
                transition: `all ${isFocus ? 0 : 300}ms ease-in-out`
            };
        }

        return {
            transition: 'all 300ms ease-in-out'
        };
    }, [isFocus]);

    // 控制当前组件的渲染时序在SessionContent组件数据获取之后，
    // 避免子组件过早渲染出现闪烁问题，例如：CapsuleTools组件的展示逻辑 @zhengchanglong
    const isPrepared = useMemo(() => {
        return curSessionMsgIds.length > 0;
    }, [curSessionMsgIds]);

    // 处理外部入口图片选择，更新图片上传区域；@wanghaoyu08
    useEffect(() => {
        eventCenter.on(
            'handleSelectImage',
            (arg: {imgList: IPicProps[]; sceneType: SceneTypeOfParams; _symbol: string}) => {
                handleSendImg(arg.imgList);
            }
        );

        return () => {
            eventCenter.off('handleSelectImage');
        };
    }, [handleSendImg]);

    // 监听显示状态变化
    useEffect(() => {
        if (!isPrepared) {
            return;
        }

        if (!userData?.isLogin) {
            // 未登录时始终显示
            tempShowCapsuleToolsFn();
        } else {
            // 已登录时根据条件显示
            if (isFocus) {
                tempHideCapsuleToolsFn();
                return;
            }
            if (!isWelcomeMouleDisplay) {
                tempShowCapsuleToolsFn();
            } else if (isVisible) {
                tempHideCapsuleToolsFn();
            }
        }

        function tempShowCapsuleToolsFn() {
            setShouldRender(true);
            nextTick(() => {
                setIsVisible(true);
            });
        }
        function tempHideCapsuleToolsFn() {
            setIsVisible(false);
            setTimeout(() => {
                setShouldRender(false);
            }, 300); // 与 CSS 过渡时间一致
        }
    }, [isFocus, isVisible, isWelcomeMouleDisplay, userData?.isLogin, isPrepared]);

    useEffect(() => {
        eventCenter.on('clearImageList', () => {
            setImgList([]);
            setStatusList([]);
            setImageSource('');
        });

        return () => {
            eventCenter.off('clearImageList');
        };
    }, [setImageSource, setImgList, setStatusList]);

    // 图片列表有图片时，页面滚动到底
    useEffect(() => {
        if (hasImg) {
            scrollToBottom('ImageListTools');
        }
    }, [hasImg, scrollToBottom]);

    return (
        <>
            <View className={styles.toolsLayout}>
                <View
                    className={cx(styles.toolsContainer, {
                        [styles.toolsContainerAbsoluteLayout]: !hasImg
                    })}
                >
                    {/* 滚动到底部按钮 */}
                    {/* 避免滚动到底按钮先于 SessionContent 组件渲染，滚动到底按钮闪烁 */}
                    {isPrepared ? <ScrollToBottomBtn className={styles.scrollToBottomBtn} /> : null}
                    {/* 服务声明 */}
                    <View className={styles.toolsMain}>
                        {showServiceDeclareShow && !hasImg && <ServiceStatement />}
                    </View>
                    {/* 胶囊工具 */}
                    {hasImg || isFocus ? null : (
                        <View
                            className={cx(styles.fadeTransition, {[styles.fadeVisible]: isVisible})}
                            ref={capsuleRef}
                            style={{
                                // 防止组件隐藏时被挤压
                                minHeight: shouldRender ? 'auto' : '0',
                                ...transitionStyles
                            }}
                        >
                            {isVisible && <CapsuleTools openUploadPopup={openUploadPopup} />}
                        </View>
                    )}

                    {/* 图片列表 */}
                    <ImageListTools
                        imgList={imgList}
                        statusList={statusList}
                        setImgList={setImgList}
                        setStatusList={setStatusList}
                    />
                </View>
            </View>

            <VTextarea
                hasImg={hasImg}
                imgList={imgList}
                statusList={statusList}
                isFocus={isFocus}
                openUploadPopup={openUploadPopup}
                setImgList={setImgList}
                setStatusList={setStatusList}
            />

            {/* 图片上传弹窗 */}
            <TipImgUploadPopup
                open={open}
                sceneType={sceneType}
                closeUploadPopup={closeUploadPopup}
                tipsData={tipsData}
                onSelectedPics={handleSendImg}
            />
        </>
    );
};

export default memo(OperatingArea);
