// Author: z<PERSON><PERSON>yu03
// Date: 2025-02-21 16:30:04
// Description: 胶囊工具组件声明
import type {SceneTypeOfParams} from '../../../../../../../models/services/docIm/sse/index.d';
import type {MsgItemType} from '../../../../../../../store/docImAtom/index.type';

export interface SendToolsMsgType {
    chatData: {
        sessionId: string;
    };
    message: MsgItemType<unknown>[];
    userData: {
        name: string;
        isLogin: boolean;
        avatar: string;
    };
}

export interface CapsuleToolsProps {
    openUploadPopup: (sceneType: SceneTypeOfParams) => void;
}

export interface BubbleInfo {
    text?: string;
    icon?: string;
    showTime?: number;
}
