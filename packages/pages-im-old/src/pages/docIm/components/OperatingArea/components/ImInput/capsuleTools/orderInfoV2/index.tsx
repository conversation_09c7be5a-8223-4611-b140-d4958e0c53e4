// Author: z<PERSON><PERSON>yu03
// Date: 2025-04-26 11:49:47
// Description: 我的订单迁移到胶囊

import {useCallback, useEffect, useState, useImperativeHandle, forwardRef} from 'react';
import {View} from '@tarojs/components';
import cx from 'classnames';

import {isEmpty} from '../../../../../../../../utils';
import {useGetCardEventCallback} from '../../../../../../../../hooks/useGetCardEventCallback';
import {ubcCommonClkSend} from '../../../../../../../../utils/generalFunction/ubc';
import {useGetUrlParams} from '../../../../../../../../hooks/common';
import {useClickOutside} from '../../../../../../../../hooks/useClickOutside';
import {
    useGetOngoingToast,
    useGetViewOrderInfo
} from '../../../../../../../../hooks/docIm/dataController';

import type {InteractionInfo} from '../../../../../../../../typings/msg.type';

import type MyOrderPopInfo from './component/index.d';
import styles from './index.module.less';

const UBC_VALUE = 'capsule_tools_order';

const MyOrderList = forwardRef<{closeOrderBubble: () => void}>((_, ref) => {
    const {viewOrderInfo} = useGetViewOrderInfo();
    const {ongoingToast} = useGetOngoingToast();
    const {onOpenLink} = useGetCardEventCallback();
    const {isDirected = 0} = useGetUrlParams();
    const [isShowOrder, setIsShowOrder] = useState<boolean>(!!viewOrderInfo);
    const [isShowSelect, setIsShowSelect] = useState<boolean>(false);
    const isLinkOrder = isEmpty(viewOrderInfo?.entrys); // TODO: 6月底冲刺项目后端viewOrderEntrys上线后可删除

    const handleClickJump = useCallback(
        (interactionInfo: InteractionInfo, ubcVal: string) => {
            setIsShowSelect(false);
            interactionInfo &&
                onOpenLink({
                    info: interactionInfo
                });
            ubcCommonClkSend({
                value: `${UBC_VALUE}_${ubcVal}_clk`,
                ext: {
                    product_info: {
                        isDirected
                    }
                }
            });
        },
        [isDirected, onOpenLink]
    );

    // 倒计时函数
    const countdown = useCallback((seconds: number) => {
        const timer = setTimeout(function () {
            countdown(seconds - 1);
        }, 1000);

        if (seconds < 1) {
            clearTimeout(timer);
            return setIsShowOrder(false);
        }
    }, []);

    const orderSelectHandle = useCallback(
        e => {
            e.stopPropagation();
            setIsShowOrder(false);
            setIsShowSelect(!isShowSelect);
        },
        [isShowSelect]
    );

    // 初始化倒计时
    useEffect(() => {
        if (viewOrderInfo) {
            countdown(10); // 倒计时从10开始
        }
    }, [countdown, viewOrderInfo]);

    // 暴露关闭方法
    useImperativeHandle(
        ref,
        () => ({
            closeOrderBubble: () => {
                setIsShowOrder(false);
            }
        }),
        []
    );

    useClickOutside(['orderPopover', 'orderItem'], () => {
        setIsShowSelect(false);
    });

    return viewOrderInfo ? (
        <View className={cx(styles.viewOrderInfo, 'wz-flex wz-row-center')} id='orderPopover'>
            {isShowOrder ? <MyOrderPopInfo ongoingToast={ongoingToast} /> : null}
            {/* TODO: 6月底冲刺项目后端viewOrderEntrys上线后可删除isLinkOrder的逻辑 */}
            {isLinkOrder ? (
                <View
                    className='wz-flex'
                    onClick={() => {
                        if (viewOrderInfo?.actionInfo?.interactionInfo) {
                            handleClickJump(
                                viewOrderInfo?.actionInfo?.interactionInfo,
                                'view_order'
                            );
                        }
                    }}
                >
                    <View className={cx(styles.icon)} />
                    <View className={cx(styles.text, 'wz-fs-42 wz-ml-12 wz-fw-500')}>
                        {viewOrderInfo?.title}
                    </View>
                </View>
            ) : (
                <View className='wz-flex' onClick={orderSelectHandle}>
                    <View className={cx(styles.icon)} />
                    <View className={cx(styles.text, 'wz-fs-42 wz-ml-12 wz-fw-500')}>
                        {viewOrderInfo?.title}
                    </View>
                    <View className={cx(styles.iconRight, isShowSelect ? styles.iconShow : '')} />
                </View>
            )}

            {isShowSelect ? (
                <View className={styles.orderPopover} id='orderItem'>
                    <View className={styles.arrow} />
                    <View className={styles.content}>
                        {viewOrderInfo?.entrys?.map((item, index) => (
                            <View
                                key={item.value}
                                className={cx(
                                    styles.item,
                                    viewOrderInfo?.entrys?.length === index + 1
                                        ? styles.noBorder
                                        : ''
                                )}
                                onClick={() => handleClickJump(item?.interactionInfo, 'view_order')}
                            >
                                {item?.value}
                            </View>
                        ))}
                    </View>
                </View>
            ) : null}
        </View>
    ) : null;
});

MyOrderList.displayName = 'MyOrderList';

export default MyOrderList;
