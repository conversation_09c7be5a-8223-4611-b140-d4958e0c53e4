// Author: z<PERSON><PERSON>yu03
// Date: 2025-02-24 17:07:54
// Description: 语音蒙层

import React, {
    forwardRef,
    memo,
    useCallback,
    useRef,
    useState,
    useImperativeHandle,
    useEffect
} from 'react';
import dayjs from 'dayjs';
import cx from 'classnames';
import {setStorageSync, getStorageSync, getSetting, createSelectorQuery} from '@tarojs/taro';
import {Popup, Button, SafeArea} from '@baidu/wz-taro-tools-core';
import {View, Text, ScrollView} from '@tarojs/components';
import {isValidDate} from '@baidu/vita-utils-shared';
import {useConversationDataController} from '../../../../../../../../hooks/docIm/useConversationDataController';
import {WZ_CHAT_VOICE_INIT, WZ_CHAT_VOICEID_EXPIRE} from '../../../../../../../../constants/storageEnv';
import {postNeedLoginAudioToken} from '../../../../../../../../models/services/audio';
import {showToast} from '../../../../../../../../utils/customShowToast';

import type {VoiceModalProps} from '../index.d';
import {APP_ID} from './const';
import useVoice from './useVoice';


// import { useTextareaSendFocus } from '../../hook/useTextareaSendFocus';
import styles from './index.module.less';

interface AIVoiceTokenType {
    expiresIn: number;
    tmpSecretId: string;
    tmpSecretKey: string;
    token: string;
    ttl: number;
}

const VoiceModal = memo(
    forwardRef((props: VoiceModalProps, ref) => {
        const {open = false, handleClose} = props;
        const [slideType, setSlideType] = useState<'init' | 'moveIn' | 'moveOut' | 'moveTop'>(
            'init'
        );
        const [lineCount, setLineCount] = useState(1);
        const [scrollHeight, setScrollHeight] = useState(0);

        // TODO: 暂时写死取消语音安全高度
        const cancelVoiceSafeHieght = 200;

        // 录音实例
        const aiVoiceToken = useRef<AIVoiceTokenType>({
            expiresIn: 0,
            tmpSecretId: '',
            tmpSecretKey: '',
            token: '',
            ttl: 0
        });

        const {voiceStart, voiceStop, setVoiceResult, voiceResult} = useVoice({
            aiVoiceToken
        });
        const {createConversation} = useConversationDataController();

        const saveTokenToStorage = useCallback((tokenData: AIVoiceTokenType) => {
            try {
                const expireTime = dayjs()
                    .add(tokenData?.ttl || 0, 'second')
                    .toISOString();

                setStorageSync(WZ_CHAT_VOICEID_EXPIRE, expireTime?.toString());
                setStorageSync(
                    WZ_CHAT_VOICE_INIT,
                    JSON.stringify({
                        secretid: tokenData?.tmpSecretId,
                        secretkey: tokenData?.tmpSecretKey,
                        token: tokenData?.token
                    })
                );
            } catch (error) {
                console.error('token存储缓存失败:', error);
            }
        }, []);

        /**
         * @description 检查是否过期
         */
        const checkStorage = useCallback((expireKey: string) => {
            const storageExpireTime = getStorageSync(expireKey) || '';

            return isValidDate(storageExpireTime)
                ? dayjs().isAfter(dayjs(storageExpireTime))
                : true;
        }, []);

        /**
         * @description 获取语音token
         */
        const fetchAudioToken = useCallback(async () => {
            const [error, res] = await postNeedLoginAudioToken({
                data: {
                    appid: APP_ID
                }
            });

            if (error || !res?.data) {
                console.error('token获取失败:', error);

                return null;
            }

            return res.data;
        }, []);

        // 点击语音输入
        const onTabVoice = useCallback(async () => {
            setSlideType('moveIn');
            getSetting({
                success: async setting => {
                    const canRecord = setting?.authSetting['scope.record'];
                    if (canRecord) {
                        const isExpire = checkStorage(WZ_CHAT_VOICEID_EXPIRE);
                        if (isExpire) {
                            const tokenData = await fetchAudioToken();
                            if (tokenData) {
                                saveTokenToStorage(tokenData);
                                aiVoiceToken.current = tokenData;
                                voiceStart({
                                    secretid: tokenData?.tmpSecretId,
                                    secretkey: tokenData?.tmpSecretKey,
                                    token: tokenData?.token
                                });
                            }
                        } else {
                            try {
                                const stroageData = getStorageSync(WZ_CHAT_VOICE_INIT);
                                voiceStart(stroageData && JSON.parse(stroageData));
                            } catch (error) {
                                console.error(error, '语音token获取失败');
                            }
                        }
                    } else {
                        showToast({
                            title: '请允许录音权限',
                            icon: 'none'
                        });
                    }
                }
            });
            // eslint-disable-next-line react-hooks/exhaustive-deps
        }, []);

        const handleTouchMove = useCallback((info: unknown) => {
            // @ts-expect-error 后续修复
            const {deltaY} = info;
            if (deltaY + cancelVoiceSafeHieght < 0) {
                setSlideType('moveTop');
            } else {
                setSlideType('moveIn');
            }
        }, []);

        /**
         * @description 结束录制语音，前置包含 【上滑取消】【发送】 两种状态
         */
        const handleTouchEnd = useCallback(() => {
            switch (slideType) {
                // 发送
                case 'moveIn':
                    if (voiceResult) {
                        createConversation({
                            msg: {
                                type: 'text',
                                content: voiceResult,
                                sceneType: 'inputAudio'
                            }
                        });
                    }
                    handleClose && handleClose();
                    break;
                // 上滑
                case 'moveTop':
                    handleClose && handleClose();
                    break;
                default:
                    break;
            }
            voiceStop();
            setSlideType('init');
        }, [createConversation, handleClose, slideType, voiceResult, voiceStop]);

        useEffect(() => {
            // 计算语音输入框高度
            // eslint-disable-next-line @typescript-eslint/restrict-template-expressions
            createSelectorQuery()
                ?.select(`.${styles.voiceTextarea}`)
                ?.boundingClientRect()
                ?.exec((dom: Array<{height: number}>) => {
                    // @ts-expect-error 后续修复
                    if (dom?.length) {
                        const height = dom[0]?.height;
                        setLineCount(Math.ceil(height / 49));
                        setScrollHeight(height);
                    }
                });

            return () => {
                // setVoiceResult('');
            };
        }, [setVoiceResult, voiceResult]);

        useImperativeHandle(ref, () => ({
            handleTouchStart: () => onTabVoice(),
            handleTouchMove,
            handleTouchEnd
        }));

        return (
            <Popup open={open} onClose={handleClose} rounded placement='bottom'>
                <View className={styles.voiceModalMain}>
                    <View className={styles.shadowBox} />
                    <ScrollView
                        scrollY
                        scrollTop={scrollHeight}
                        scrollIntoView={`scrollBottom_${voiceResult?.length}`}
                        className={cx(styles.scrollResult, 'wz-pt-45')}
                    >
                        <View
                            className={cx(
                                styles.voiceTextarea,
                                lineCount > 1 ? styles.textLeft : styles.textCenter,
                                'wz-mlr-63'
                            )}
                        >
                            {voiceResult}
                        </View>
                        <View id={`scrollBottom_${voiceResult?.length}`} style={{height: 1}} />
                    </ScrollView>
                    <View className={cx(styles.sendVoiceBtn, 'wz-flex', 'wz-mb-63')}>
                        <Text
                            className={cx(
                                styles.sendVoiceText,
                                'wz-mb-51',
                                slideType === 'moveTop' ? styles.cancelTextColor : ''
                            )}
                        >
                            松开发送 上滑取消
                        </Text>
                        <Button
                            className={cx(
                                styles.sendBtnItem,
                                slideType === 'moveTop' ? styles.cancelBackgroundColor : ''
                            )}
                            id='voiceBtnItem'
                            color='primary'
                            shape='round'
                            // onLongTap={handleLongTap}
                            // onTouchStart={onTabVoice}
                            // onTouchMove={handleTouchMove}
                            // onTouchEnd={handleTouchEnd}
                        >
                            <View
                                className={
                                    slideType === 'moveTop'
                                        ? styles.voiceCancelGif
                                        : styles.voiceIngGif
                                }
                            />
                        </Button>
                        <SafeArea position='bottom' style={{backgroundColor: '#ffffff'}} />
                    </View>
                </View>
            </Popup>
        );
    })
);

VoiceModal.displayName = 'VoiceModal';

export default VoiceModal;
