// Author: z<PERSON><PERSON>yu03
// Date: 2025-02-24 17:07:54
// Description: 语音蒙层

import React, {
    memo,
    useCallback,
    useRef,
    useState,
    forwardRef,
    useImperativeHandle,
    useEffect
} from 'react';
import {createSelectorQuery} from '@tarojs/taro';
import {Popup, Button, SafeArea} from '@baidu/wz-taro-tools-core';
import {View, Text, ScrollView} from '@tarojs/components';
import cx from 'classnames';

// import type {AIVoiceToken} from '@src/components/im/ImVoice/VoiceInputModal/index.d';
import {useConversationDataController} from '../../../../../../../../hooks/docIm/useConversationDataController';
import type {VoiceModalProps} from '../index.d';
import useVoice from './useVoice';

// import { useTextareaSendFocus } from '../../hook/useTextareaSendFocus';
import styles from './index.module.less';

interface AIVoiceToken {
    expiresIn: number;
    tmpSecretId: string;
    tmpSecretKey: string;
    token: string;
    ttl: number;
}

const VoiceModal = memo(
    forwardRef((props: VoiceModalProps, ref) => {
        const {open = false, handleClose} = props;
        const [slideType, setSlideType] = useState<'init' | 'moveIn' | 'moveOut' | 'moveTop'>(
            'init'
        );
        const [lineCount, setLineCount] = useState(1);

        // TODO: 暂时写死取消语音安全高度
        const cancelVoiceSafeHieght = 200;

        // 录音实例
        const aiVoiceToken = useRef<AIVoiceToken>(null);

        const {createConversation} = useConversationDataController();
        const {voiceStart, voiceStop, setVoiceResult, voiceResult} = useVoice({
            aiVoiceToken
        });

        // const { textareaRef } = useTextareaSendFocus();

        const resetData = useCallback(() => {
            setSlideType('init');
        }, []);

        const handleTouchStart = useCallback(() => {
            voiceStart();
            setSlideType('moveIn');
        }, [voiceStart]);

        // TODO:unknown暂时添加类型
        const handleTouchMove = useCallback((info: unknown) => {
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            // @ts-expect-error
            const {deltaY} = info;
            if (deltaY + cancelVoiceSafeHieght < 0) {
                setSlideType('moveTop');
            } else {
                setSlideType('moveIn');
            }
        }, []);

        /**
         * @description 结束录制语音，前置包含 【上滑取消】【发送】 两种状态
         */
        const handleTouchEnd = useCallback(() => {
            switch (slideType) {
                // 发送
                case 'moveIn':
                    if (voiceResult) {
                        createConversation({
                            msg: {
                                type: 'text',
                                content: voiceResult,
                                sceneType: 'inputAudio'
                            }
                        });
                    }
                    handleClose && handleClose();
                    break;
                // 上滑
                case 'moveTop':
                    handleClose && handleClose();
                    break;
                default:
                    break;
            }
            voiceStop();
            resetData();
        }, [createConversation, handleClose, resetData, slideType, voiceResult, voiceStop]);

        useEffect(() => {
            // 计算语音输入框高度
            // eslint-disable-next-line @typescript-eslint/restrict-template-expressions
            createSelectorQuery()
                ?.select(`.${styles.voiceTextarea}`)
                ?.boundingClientRect()
                ?.exec((dom: Array<{height: number}>) => {
                    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                    // @ts-ignore
                    if (dom?.length) {
                        const height = dom[0]?.height;
                        setLineCount(Math.ceil(height / 69));
                    }
                });
        }, [voiceResult]);

        useEffect(() => {
            if (!open) {
                setVoiceResult('');
            }
        }, [open, setVoiceResult]);

        useImperativeHandle(ref, () => ({
            handleTouchStart,
            handleTouchMove,
            handleTouchEnd
        }));

        return (
            <Popup
                open={open}
                onClose={handleClose}
                rounded
                catchMove
                style={{
                    backgroundColor: '#e8e8f2'
                }}
                placement='bottom'
            >
                <View className={styles.voiceModalMain}>
                    <View className={styles.shadowBox}>
                        {slideType === 'moveTop' ? (
                            <Text className={cx(styles.cancelText, 'wz-plr-63', 'wz-ptb-15')}>
                                松开手指 取消发送
                            </Text>
                        ) : null}
                    </View>
                    <ScrollView
                        scrollY
                        reverse
                        // scrollTop={scrollHeight}
                        // scrollIntoViewAlignment='end'
                        scrollIntoView={`scrollBottom_${voiceResult?.length}`}
                        className={cx(styles.scrollResult, 'wz-pt-45')}
                    >
                        <View
                            className={cx(
                                styles.voiceTextarea,
                                lineCount > 1 ? styles.textLeft : styles.textCenter,
                                'wz-mlr-63'
                            )}
                        >
                            {voiceResult}
                        </View>
                        <View id={`scrollBottom_${voiceResult?.length}`} />
                    </ScrollView>
                    <View className={cx(styles.sendVoiceBtn, 'wz-flex', 'wz-mb-63')}>
                        <Text
                            className={cx(
                                styles.sendVoiceText,
                                'wz-mb-51'
                                // slideType === 'moveTop' ? styles.cancelTextColor : ''
                            )}
                        >
                            {/* {slideType === 'moveTop' ? '松开手指 取消发送' : '松开发送  上滑取消'} */}
                            松开发送 上滑取消
                        </Text>
                        <Button
                            className={cx(
                                styles.sendBtnItem
                                // slideType === 'moveTop' ? styles.cancelBackgroundColor : ''
                            )}
                            id='voiceBtnItem'
                            color='primary'
                            shape='round'
                            // onLongTap={handleLongTap}
                            // onTouchStart={handleTouchStart}
                            // onTouchMove={handleTouchMove}
                            // onTouchEnd={handleTouchEnd}
                        >
                            <View className={styles.voiceIngGif} />
                        </Button>
                    </View>
                    <SafeArea position='bottom' style={{backgroundColor: '#e8e8f2'}} />
                </View>
            </Popup>
        );
    })
);

VoiceModal.displayName = 'VoiceModal';

export default VoiceModal;
