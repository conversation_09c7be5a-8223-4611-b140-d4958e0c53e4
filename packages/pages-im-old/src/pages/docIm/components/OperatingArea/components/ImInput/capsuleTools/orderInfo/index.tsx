// Author: z<PERSON><PERSON>yu03
// Date: 2025-04-26 11:49:47
// Description: 我的订单迁移到胶囊

import {memo, useCallback, useEffect, useState} from 'react';
import {View} from '@tarojs/components';
import cx from 'classnames';

import {useGetCardEventCallback} from '../../../../../../../../hooks/useGetCardEventCallback';
import {
    useGetOngoingToast,
    useGetViewOrderInfo
} from '../../../../../../../../hooks/docIm/dataController';

import {ubcCommonClkSend} from '../../../../../../../../utils/generalFunction/ubc';
import {useGetUrlParams} from '../../../../../../../../hooks/common';

import type {InteractionInfo} from '../../../../../../../../typings/msg.type';

import MyOrderPopInfo from './component/index.d';
import styles from './index.module.less';

const UBC_VALUE = 'capsule_tools_order';

const MyOrderList = memo(() => {
    const {viewOrderInfo} = useGetViewOrderInfo();
    const {ongoingToast} = useGetOngoingToast();
    const {onOpenLink} = useGetCardEventCallback();
    const {isDirected = 0} = useGetUrlParams();
    const [isShowOrder, setIsShowOrder] = useState<boolean>(!!viewOrderInfo);
    const [isShowSelect, setIsShowSelect] = useState<boolean>(false);

    const handleClickJump = useCallback(
        (interactionInfo: InteractionInfo, ubcVal: string) => {
            interactionInfo &&
                onOpenLink({
                    info: interactionInfo
                });
            ubcCommonClkSend({
                value: `${UBC_VALUE}_${ubcVal}_clk`,
                ext: {
                    product_info: {
                        isDirected
                    }
                }
            });
        },
        [isDirected, onOpenLink]
    );

    const countdown = useCallback((seconds: number) => {
        const timer = setTimeout(function () {
            countdown(seconds - 1);
        }, 1000);

        if (seconds < 1) {
            clearTimeout(timer);
            return setIsShowOrder(false);
        }
    }, []);

    const orderSelectHandle = useCallback(() => {
        setIsShowOrder(false);
        setIsShowSelect(!isShowSelect);
    }, [isShowSelect]);

    useEffect(() => {
        countdown(10); // 倒计时从10开始;
    }, [countdown]);

    return viewOrderInfo ? (
        <View className={cx(styles.viewOrderInfo, 'wz-flex wz-row-center')}>
            {isShowOrder ? <MyOrderPopInfo ongoingToast={ongoingToast} /> : null}
            <View className='wz-flex' onClick={orderSelectHandle}>
                <View className={cx(styles.icon)} />
                <View className={cx(styles.text, 'wz-fs-42 wz-ml-12 wz-fw-500')}>
                    {viewOrderInfo?.title}
                </View>
                <View className={styles.iconRight} />
            </View>

            {isShowSelect ? (
                <View className={styles.orderPopover}>
                    <View className={styles.arrow}></View>
                    <View className={styles.content}>
                        {viewOrderInfo?.entrys?.map(item => (
                            <View
                                key={item.value}
                                className={styles.item}
                                onClick={() => handleClickJump(item?.interactionInfo, 'view_order')}
                            >
                                {item?.value}
                            </View>
                        ))}
                    </View>
                </View>
            ) : null}
        </View>
    ) : null;
});

MyOrderList.displayName = 'MyOrderList';

export default MyOrderList;
