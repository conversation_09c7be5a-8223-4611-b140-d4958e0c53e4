// Author: z<PERSON><PERSON>yu03
// Date: 2025-04-28 15:58:59
// Description: 我的订单提醒弹框

import {memo, useCallback, useState} from 'react';
import {View} from '@tarojs/components';
import {WiseShut} from '@baidu/wz-taro-tools-icons';
import cx from 'classnames';

import {MyOrderInfoPopProps} from './index.d';
import style from './index.module.less';

const MyOrderPopInfo = memo((props: MyOrderInfoPopProps) => {
    const [isOpen, setIsOpen] = useState(true);

    const {ongoingToast} = props;

    const handleClose = useCallback(() => {
        setIsOpen(false);
    }, []);

    if (!ongoingToast) return;

    const {actionInfo, hasOngoing} = ongoingToast;

    return hasOngoing && isOpen ? (
        <View className={cx(style.OrderInfoPop, 'wz-flex')}>
            {actionInfo.value}
            <WiseShut
                onClick={handleClose}
                size={50}
                color='#fff'
                className={cx(style.OrderCloseIcon, 'wz-plr-24')}
            />
        </View>
    ) : null;
});

MyOrderPopInfo.displayName = 'MyOrderPopInfo';

export default MyOrderPopInfo;
