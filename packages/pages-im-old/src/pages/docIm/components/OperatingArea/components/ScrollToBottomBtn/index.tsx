import {useEffect, useRef, useState} from 'react';
import {
    eventCenter,
    getCurrentInstance,
    createIntersectionObserver,
    type PageInstance
} from '@tarojs/taro';
import {View, Image} from '@tarojs/components';
import cx from 'classnames';
import {debounce} from 'lodash-es';
import {useAtom} from 'jotai';
import {scrollToBottomBtnVisibleAtom} from '../../../../../../store/docImAtom';
import {imgUrlMap} from '../../../../../../constants/resourcesOnBos';
import {useScrollControl} from '../../../../../../hooks/common/useScrollControl';
import {ubcCommonViewSend, ubcCommonClkSend} from '../../../../../../utils/generalFunction/ubc';
import styles from './index.module.less';

const ZeroWidthNoBreakSpace = '\uFEFF';
const SCROLL_ANCHOR_ID = 'scroll-anchor';
const SCROLL_ANCHOR_VISIBLE_EVENT = 'scrollAnchor:visible';
export const SCROLL_TO_BOTTOM_BTN_VISIBLE_EVENT = 'scrollToBottomBtn:visible';

interface Position {
    position?: 'absolute' | 'fixed';
    top?: number;
    right?: number;
    bottom?: number;
    left?: number;
    transform?: string;
}

interface DynamicStyle extends Position {
    opacity?: number;
    zIndex?: number;
}

interface ScrollToBottomBtnProps {
    className?: string;
    dynamicPositionComputeFn?: () => Position;
}

export const ScrollToBottomBtn = ({
    className,
    dynamicPositionComputeFn
}: ScrollToBottomBtnProps) => {
    const [visible, setVisible] = useAtom(scrollToBottomBtnVisibleAtom);
    const [dynamicStyle, setDynamicStyle] = useState<DynamicStyle>({});
    const {scrollToBottom} = useScrollControl('docImScrollControl');

    const scrollIntoView = () => {
        scrollToBottom('scrollToBottomBtn');
    };
    const handleClick = () => {
        scrollIntoView();
        // 埋点：点击滚动到底部按钮
        ubcCommonClkSend({
            value: 'ImScrollToBottomBtn'
        });
    };

    useEffect(() => {
        const debouncedScrollToBottomBtnVisible = debounce((visible: boolean) => {
            setVisible(visible);
            if (visible) {
                // 埋点：滚动到底部按钮曝光
                ubcCommonViewSend({
                    value: 'ImScrollToBottomBtn'
                });
            }
        }, 1000);
        const handleScrollAnchorVisible = (scrollAnchorVisible: boolean) => {
            debouncedScrollToBottomBtnVisible(!scrollAnchorVisible);
            // 为了让滚动到底时按钮尽快隐藏
            if (scrollAnchorVisible) {
                setVisible(false);
            }
        };
        const handleScrollToBottomBtnVisible = debounce((scrollToBottomBtnVisible: boolean) => {
            setVisible(scrollToBottomBtnVisible);
        }, 300);
        eventCenter.on(SCROLL_ANCHOR_VISIBLE_EVENT, handleScrollAnchorVisible);
        eventCenter.on(SCROLL_TO_BOTTOM_BTN_VISIBLE_EVENT, handleScrollToBottomBtnVisible);
        return () => {
            eventCenter.off(SCROLL_ANCHOR_VISIBLE_EVENT, handleScrollAnchorVisible);
            eventCenter.off(SCROLL_TO_BOTTOM_BTN_VISIBLE_EVENT, handleScrollToBottomBtnVisible);
        };
    }, []);

    // TODO 这部分功能未调试，后续需要调试或者移除
    useEffect(() => {
        const pos = dynamicPositionComputeFn?.() ?? {};
        setDynamicStyle(state => {
            const newState = {
                ...state,
                ...pos
            };
            return newState;
        });
    }, [dynamicPositionComputeFn, visible]);

    if (!visible) {
        return null;
    }

    return (
        <View
            className={cx(styles.scrollToBottomBtn, className, {[styles.visible]: visible})}
            onClick={handleClick}
            style={{...dynamicStyle}}
        >
            <View className={styles.scrollToBottomBtnBg}></View>
            <Image src={imgUrlMap.arrowDownIcon} className={styles.scrollToBottomBtnIcon} />
        </View>
    );
};

export const ScrollAnchor = ({
    swanRelativeSelector,
    className
}: {
    swanRelativeSelector: string;
    className?: string;
}) => {
    const {observe, disconnect} = useIntersectionObserver({swanRelativeSelector});

    useEffect(() => {
        observe(`#${SCROLL_ANCHOR_ID}`);
        return () => {
            disconnect();
        };
    }, []);
    return (
        <View id={SCROLL_ANCHOR_ID} className={cx(styles.scrollAnchor, className)}>
            {ZeroWidthNoBreakSpace}
        </View>
    );
};

function useIntersectionObserver({swanRelativeSelector}: {swanRelativeSelector: string}) {
    const observerSwan = useRef<any>(null);
    const observerH5 = useRef<IntersectionObserver | null>(null);

    const handleIntersectionH5 = (entries: IntersectionObserverEntry[]) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                eventCenter.trigger(SCROLL_ANCHOR_VISIBLE_EVENT, true);
            } else {
                eventCenter.trigger(SCROLL_ANCHOR_VISIBLE_EVENT, false);
            }
        });
    };

    const handleIntersectionSwan = (entry: IntersectionObserverEntry) => {
        const visibleRange = 0.5;
        if (entry.intersectionRatio > visibleRange) {
            eventCenter.trigger(SCROLL_ANCHOR_VISIBLE_EVENT, true);
        } else if (entry.intersectionRatio <= visibleRange) {
            eventCenter.trigger(SCROLL_ANCHOR_VISIBLE_EVENT, false);
        }
    };

    const observe = (selector: string) => {
        if (process.env.TARO_ENV === 'swan') {
            if (!observerSwan.current) {
                observerSwan.current = createIntersectionObserver(
                    getCurrentInstance().page as PageInstance,
                    {
                        thresholds: [0, 1],
                        observeAll: true
                    }
                );
            }
            observerSwan.current
                .relativeTo(swanRelativeSelector)
                .observe(selector, handleIntersectionSwan);
        } else if (process.env.TARO_ENV === 'h5') {
            if (!observerH5.current) {
                observerH5.current = new IntersectionObserver(handleIntersectionH5, {
                    threshold: [0, 1]
                });
            }
            observerH5.current.observe(document.querySelector(selector) as Element);
        }
    };

    const disconnect = () => {
        if (process.env.TARO_ENV === 'swan' && observerSwan.current) {
            observerSwan.current.disconnect();
        } else if (process.env.TARO_ENV === 'h5' && observerH5.current) {
            observerH5.current.disconnect();
        }
    };

    return {observe, disconnect};
}

export default ScrollToBottomBtn;
