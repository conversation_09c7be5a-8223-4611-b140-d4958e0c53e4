import {requirePlugin} from '@tarojs/taro';
import {VoiceTokenInit} from '../useVoice';
import {IAsrHandler, IHandlerCallbackNode, IAsrError} from './index.d';

// 相关文档：
// https://mp.weixin.qq.com/wxopen/pluginbasicprofile?action=intro&appid=wx3e17776051baf153&token=1499762518&lang=zh_CN
interface IAsr extends IAsrHandler, IHandlerCallbackNode {}

class AsrHandler implements IAsr {
    private voiceRecognizer;
    public canIUse = true;
    private configParams = {
        signCallback: null, // 鉴权函数
        secretkey: '',
        secretid: '',
        token: '',
        appid: '1321501819', // 腾讯云账号appid（非微信appid）
        engine_model_type: '16k_zh'
    };
    // 多端统一打平对外暴漏的回调
    public onError: IHandlerCallbackNode['onError'];
    public onStart: IHandlerCallbackNode['onStart'];
    public onRecognize: IHandlerCallbackNode['onRecognize'];
    public onRecognizeStop: IHandlerCallbackNode['onRecognizeStop'];
    public onRecorderStop: IHandlerCallbackNode['onRecorderStop'];
    public onStop: IHandlerCallbackNode['onStop'];
    public onCancel: IHandlerCallbackNode['onCancel'];
    constructor() {
        const _plugin = requirePlugin('QCloudAIVoice');
        this.voiceRecognizer = _plugin.speechRecognizerManager();

        // 绑定事件回调：

        // 开始识别回调
        this.voiceRecognizer.OnRecognitionStart = res => {
            this.onStart && this.onStart(res);
        };
        // 一句话开始时回调
        this.voiceRecognizer.OnSentenceBegin = res => {
            const result = res?.result?.voice_text_str;
            this.onRecognize && this.onRecognize({result});
        };
        // 识别结果变化回调
        this.voiceRecognizer.OnRecognitionResultChange = res => {
            const result = res?.result?.voice_text_str;
            this.onRecognize && this.onRecognize({result});
        };
        // 一句话结束时回调： 看起来一句话结束并没有触发，导致音浪gif不能切换
        this.voiceRecognizer.OnSentenceEnd = res => {
            const result = res?.result?.voice_text_str;
            this.onRecognizeStop && this.onRecognizeStop({result});
        };
        // 识别完成回调
        this.voiceRecognizer.OnRecognitionComplete = res => {
            this.onStop && this.onStop(res);
        };
        // 录音结束回调
        this.voiceRecognizer.OnRecorderStop = res => {
            this.onRecorderStop && this.onRecorderStop(res);
        };

        this.voiceRecognizer.OnError = res => {
            const err = this.handleErr(res || {});
            this.onError && this.onError(err as IAsrError);
        };
    }

    private handleErr = res => {
        const errCode = +res?.code;
        const __err: IAsrError = {errCode};
        switch (errCode) {
            case -30011:
                // 试图在识别正在进行中是再次调用start，返回错误，正在进行的识别任务正常进行
                __err.errToast = '当前网络拥挤中，请稍候重试';
                __err.errHandle = 'igorne';
                break;
            case 4008:
                // 超过15s未识别到语音，忽略，交由业务处理
                __err.errHandle = 'igorne';
                break;

            default:
                __err.errToast = '录音失败，请尝试使用文字输入';
                break;
        }

        return {...res, ...__err};
    };
    // @ts-ignore
    start(res: VoiceTokenInit) {
        if (!this.canIUse) {
            return;
        }
        this.configParams = {
            ...this.configParams,
            ...res
        };
        // 开始识别
        this.voiceRecognizer?.start(this.configParams);
    }

    stop() {
        this.voiceRecognizer?.stop();
    }

    cancel() {
        // 微信plugin没有cancel方法，调用下stop
        this.voiceRecognizer?.stop();
        this.onCancel();
    }
}

export default AsrHandler;
