import cx from 'classnames';
import {useAtomValue} from 'jotai';
import {View} from '@tarojs/components';
import {memo, useCallback, useMemo, type FC} from 'react';

import {useGetCardEventCallback} from '../../../../../../hooks/useGetCardEventCallback';

import {agreementInfoAtom} from '../../../../../../store/docImAtom/otherData';

import type {AgreementDataType} from '../../../../../../models/services/docIm/index.d';

import styles from './index.module.less';

const ServiceStatement: FC = () => {
    const agreementInfo = useAtomValue(agreementInfoAtom);
    const {onOpenLink} = useGetCardEventCallback();

    const handleOpenLink = useCallback(
        (item: AgreementDataType['list'][number][number]) => {
            onOpenLink({info: item.interactionInfo});
        },
        [onOpenLink]
    );

    const genCon = useMemo(() => {
        return agreementInfo?.list?.map((item, idx) => {
            return (
                <View
                    className={cx(
                        styles.serviceStatementItemContainer,
                        'wz-flex wz-fs-36 wz-fw-400 wz-text-center'
                    )}
                    key={idx}
                >
                    {item?.map((i, iidx) => {
                        return (
                            <View
                                className={cx(
                                    i.type === 'highLightText' ? styles.highlightItem : styles.item,
                                    'wz-fs-36 wz-fw-400 wz-text-center'
                                )}
                                key={`${idx}-${iidx}`}
                                onClick={() => handleOpenLink(i)}
                            >
                                {i.value}
                            </View>
                        );
                    })}
                </View>
            );
        });
    }, [agreementInfo?.list, handleOpenLink]);

    if (!agreementInfo) return null;

    return <View className={cx(styles.serviceStatementContainer, 'wz-flex')}>{genCon}</View>;
};

export default memo(ServiceStatement);
