// Author: z<PERSON><PERSON>yu03
// Date: 2025-02-14 17:53:19
// Description: 语音Icon组件

import {View} from '@tarojs/components';
import React, {useMemo, memo} from 'react';
import {WiseKeyboardSolid} from '@baidu/wz-taro-tools-icons';
import {WImage} from '@baidu/wz-taro-tools-core';
import cx from 'classnames';

import CLoginButton from '../../../../../../../components/CLoginButton';
import {useGetUserData, useUpdateUserData} from '../../../../../../../hooks/docIm/pageDataController';
import {useGetSwanMsgListSceneStatus} from '../../../../../../../hooks/docIm/useGetSwanMsgListSceneStatus';

import type {VoiceSwitch} from './index.d';
import styles from './index.module.less';

const normalSrc = 'https://med-fe.cdn.bcebos.com/vita/voiceIocn.png';
const swanMsgSrc = 'https://med-fe.cdn.bcebos.com/vita/swanMsgVoice.png';

const ImTriageVoice = memo((props: VoiceSwitch) => {
    const {handleChangeIcon, isShowVoice} = props;

    const {userData} = useGetUserData();
    const {updateUserData} = useUpdateUserData();
    const {status} = useGetSwanMsgListSceneStatus();

    const VoiceSwitchCom = useMemo(() => {
        if (isShowVoice) {
            return <WiseKeyboardSolid size={84} onClick={handleChangeIcon} />;
        }

        // return <WiseVoicePlay size={72} onClick={handleChangeIcon} />;
        return (
            <WImage
                src={status ? swanMsgSrc : normalSrc}
                className={styles.voiceIcon}
                onClick={handleChangeIcon}
            />
        );
    }, [handleChangeIcon, isShowVoice, status]);

    if (process.env.TARO_ENV === 'weapp') {
        return (
            <CLoginButton
                useH5CodeLogin
                isLogin={userData?.isLogin}
                isLoginPopup={userData?.isLogin}
                wxLoginInteractionType='popup'
                onLoginSuccess={e => {
                    updateUserData({
                        isLogin: e.loginStatus
                    });
                    handleChangeIcon();
                }}
            >
                <View className={cx(styles.imTriageVoiceIcon)}>{VoiceSwitchCom}</View>
            </CLoginButton>
        );
    }

    return <View className={cx(styles.imTriageVoiceIcon)}>{VoiceSwitchCom}</View>;
});

ImTriageVoice.displayName = 'ImTriageVoice';

export default ImTriageVoice;
