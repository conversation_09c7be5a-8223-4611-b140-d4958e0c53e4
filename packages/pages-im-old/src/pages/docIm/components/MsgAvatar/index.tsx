import cx from 'classnames';
import {View} from '@tarojs/components';
import {memo, useCallback, useMemo, type FC} from 'react';

import {navigate} from '../../../../utils/basicAbility/commonNavigate';

import {type MsgItemType, PosterRole} from '../../../../store/docImAtom/index.type';
import {OwnerTypeEnum, type OwnerTypeValue, type PosterInfo} from '../../../../typings/msg.type';

import styles from './index.module.less';

interface IProps {
    data: PosterInfo;
    role: OwnerTypeValue;
    posterRole: MsgItemType<unknown>['meta']['showPosterRole'];
}

/**
 * 消息头像组件
 *
 * @param props 组件属性
 * @returns 返回渲染后的 JSX 元素
 */
const MsgAvatar: FC<IProps> = props => {
    const {data, role, posterRole} = props;

    const defaultAvatar = 'https://med-fe.cdn.bcebos.com/wz/assistant.png';

    const avatarUrl = useMemo(() => {
        return data?.avatar;
    }, [data?.avatar]);

    const avatarIconUrl = useMemo(() => {
        if (role === OwnerTypeEnum['服务方']) {
            const posterRoleMap: {
                [k in MsgItemType<unknown>['meta']['showPosterRole']]?: string;
            } = {
                7: 'https://med-fe.cdn.bcebos.com/wz-mini/triageStream/vitaAiIcon.png'
            };

            return posterRoleMap[posterRole] || null;
        }

        return null;
    }, [posterRole, role]);

    const clickAvatar = useCallback(() => {
        if (data?.link && role === OwnerTypeEnum['服务方']) {
            navigate({
                url: data?.link,
                openType: 'navigate'
            });
        }
    }, [data?.link, role]);

    return (
        <View className={cx('wz-mb-24 wz-flex wz-col-center')} style={{width: '100%'}}>
            <View
                className={cx(styles.avatar, 'wz-mr-24')}
                style={{
                    backgroundImage: `url(${avatarUrl || defaultAvatar})`
                }}
                onClick={clickAvatar}
            >
                {avatarIconUrl ? (
                    <View
                        className={styles.avatarIcon}
                        style={{
                            backgroundImage: `url(${avatarIconUrl})`
                        }}
                    />
                ) : null}
            </View>
            <View className={cx(styles.docName, 'wz-fs-45')}>
                {posterRole === PosterRole['RoleDocAgent'] && data?.name
                    ? `${data?.name}的AI助理`
                    : data?.name}
            </View>
        </View>
    );
};

export default memo(MsgAvatar);
