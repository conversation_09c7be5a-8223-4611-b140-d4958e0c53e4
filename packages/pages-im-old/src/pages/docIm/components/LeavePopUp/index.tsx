import {memo, FC} from 'react';

import LeaveDialog from './LeaveDialog';

import type {LeavePopUpProps} from './index.d';

const LeavePopUp: FC<LeavePopUpProps> = (props: LeavePopUpProps) => {
    const {leaveData, showLeave, closeDialog} = props;

    return (
        <>
            {/* IM优惠券挽留弹窗，暂不迁移 */}
            {/* 兜底挽留弹窗 */}
            {leaveData?.leaveInfo?.content && showLeave && (
                <LeaveDialog leaveInfo={leaveData.leaveInfo} closeDialog={closeDialog} />
            )}
        </>
    );
};

export default memo(LeavePopUp);
