export interface ILeaveProps {
    leaveInfo?: {
        title?: string;
        content?: string;
        btns?: LeaveBtn[];
        position?: string;
    };
    closeDialog?: (isStay: boolean) => void;
}

export interface LeaveBtn {
    type?: string;
    text?: string;
}

export interface LeavePopUpProps {
    /**
     * 兜底挽留
     */
    leaveData?: ILeaveProps;
    showDialog?: boolean;
    showLeave?: boolean;
    closeDialog?: (isStay) => void;
}
