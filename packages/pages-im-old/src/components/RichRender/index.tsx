/*
 * @Author: lijing106
 * @Description: 富文本格式渲染
 */

import cx from 'classnames';
import React, { memo, FC } from 'react';
import { View, Text } from '@tarojs/components';
import { WImage } from '@baidu/wz-taro-tools-core';
import type { IRichRenderProps } from './index.d';
import styles from './index.module.less';

const RichRender: FC<IRichRenderProps> = (props: IRichRenderProps) => {
    const { richData, iconStyles, higtLightStyle, redStyle, textStyle, richClassName, showComma = false } = props;

    return (
        <>
            {richData.length > 0
                && richData.map((item, index) => {
                    return (
                        <View key={index} className={cx(styles.inlineBlock)}>
                            {item?.type === 'text' && (
                                <View className={cx(styles.inlineBlock)} style={textStyle}>
                                    {item?.value}
                                </View>
                            )}
                            {item?.type === 'redText' && (
                                <View className={cx(styles.richRedText)} style={redStyle}>
                                    {item?.value}
                                </View>
                            )}
                            {item?.type === 'highLightText' && (
                                <View className={cx(styles.richHighLightText, richClassName)} style={higtLightStyle}>
                                    {item?.value}
                                </View>
                            )}
                            {item?.type === 'icon' && <WImage style={iconStyles} src={item?.value} />}
                            {showComma && index < richData.length - 1 && <Text>、</Text>}
                        </View>
                    );
                })}
        </>
    );
};

export default memo(RichRender);
