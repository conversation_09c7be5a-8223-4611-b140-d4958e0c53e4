.modalContainer {
    height: 100%;
    flex-direction: column;

    .modalContent {
        width: 993px;
        background: #fff;
        color: #1f1f1f;
        padding: 63px;
        box-sizing: border-box;
        border-radius: 63px;

        .title {
            font-size: 57px;
            line-height: 1;
            text-align: center;
        }

        .desc {
            width: fit-content;
            color: #525252;
            line-height: 69px;
            margin: 0 auto;
        }

        .descRich {
            font-size: 48px;
            line-height: 69px;
        }

        .phoneWrap {
            width: 870px;
            height: fit-content;
            display: flex;
            align-items: center;
            background-color: #f3f4f5;
            border-radius: 27px;
            box-sizing: border-box;
            padding: 0 36px;

            .phoneNumber {
                position: relative;
                flex: 1;
                font-size: 48px;
                line-height: 1;
                box-sizing: border-box;

                :global(.wz-flex) {
                    flex: 1;
                }
            }

            .seePhoneFix {
                :global(.wz-taro-input__clear) {
                    position: absolute;
                    right: 36px;
                }
            }

            .seePhoneBtn {
                color: #00c8c8;
                word-break: keep-all;
                white-space: nowrap;
                display: inline-block;
                padding-top: 3px;
                padding-bottom: 3px;
                border-left: 3px solid #e0e0e0;
            }

            .disableBtn {
                color: #b5b5b5;
            }
        }

        .btn {
            width: 870px;
            margin-top: 45px;
            font-size: 48px;
            font-weight: bolder;
        }
    }

    .close {
        margin-top: 72px;
    }
}
