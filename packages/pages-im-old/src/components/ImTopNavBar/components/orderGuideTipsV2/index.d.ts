import type {TitleInfoType} from '../../../../store/triageStreamAtom/index.type';
import type {InteractionInfo, InteractionType} from '../../../../typings';

export interface OrderGuideTipType {
    titleInfo?: TitleInfoType;
    btnTools: ToolBtnItem[];
}

export interface ToolBtnItem {
    avatar: string;
    title: string;
    type: 'myOrderList';
    actionInfo?: {
        interaction: InteractionType;
        interactionInfo: InteractionInfo;
    };
}
