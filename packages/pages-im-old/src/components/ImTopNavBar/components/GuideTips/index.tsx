import {setStorageSync, getStorageSync} from '@tarojs/taro';
import {memo, FC, useEffect, useCallback, useMemo, ReactNode, useState} from 'react';
import {View, Text} from '@tarojs/components';
import {WImage} from '@baidu/wz-taro-tools-core';
import cx from 'classnames';
import {useTimeout} from '@baidu/vita-ui-cards-common/ImFlow/hooks';

import {WZ_CHAT_VOICEID} from '../../../../constants/storageEnv';

import styles from './index.module.less';

interface TipsProps {
    time?: number | undefined;
    stepTime?: number;
    storageKey: string;
    className?: string;
    showTimer?: boolean;
    tipsNode?: string | ReactNode;
    setOpenTips: (v: boolean) => void;
}

const GuideTips: FC<TipsProps> = props => {
    const {
        time = 5,
        setOpenTips,
        storageKey = WZ_CHAT_VOICEID,
        className,
        tipsNode,
        showTimer = true,
        stepTime = 1000
    } = props;

    const [durtion, setDurtionTime] = useState(time);

    const handleCancelTips = useCallback(() => setOpenTips(false), [setOpenTips]);

    const handleTimeOut = useTimeout(() => {
        setDurtionTime(durtion - 1);
        if (durtion <= 0) {
            handleCancelTips();
            handleTimeOut(false);
        }
    }, stepTime);

    const memeoGetStorageShowTips = useMemo(() => getStorageSync(storageKey), [storageKey]);

    useEffect(() => {
        if (!memeoGetStorageShowTips) {
            handleTimeOut(true);
            setStorageSync(storageKey, true);
        } else {
            handleCancelTips();
        }

        return () => {
            handleTimeOut(false);
        };
    }, [handleCancelTips, handleTimeOut, memeoGetStorageShowTips, storageKey]);

    const memoTipsNode = useMemo(() => {
        return (
            <View className={cx(styles.tipsContainer)}>
                {tipsNode || '点这里可以发送语音了'}
                {showTimer ? <Text className={styles.showTime}>（{durtion}s）</Text> : null}
            </View>
        );
    }, [showTimer, durtion, tipsNode]);

    const tipsDomRender = useMemo(() => {
        if (memeoGetStorageShowTips) {
            return null;
        }

        return (
            <View
                className={cx(
                    'wz-flex wz-col-center wz-row-between wz-fs-42 wz-br-27',
                    styles.voiceTips,
                    className
                )}
            >
                {memoTipsNode}
                <WImage onClick={handleCancelTips} className={styles.voiceIcon} />
            </View>
        );
    }, [className, handleCancelTips, memeoGetStorageShowTips, memoTipsNode]);

    return tipsDomRender;
};

export default memo(GuideTips);
