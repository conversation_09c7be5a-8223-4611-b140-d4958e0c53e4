import React, {memo, useEffect, useMemo, useState} from 'react';
import {View, Text} from '@tarojs/components';
import cx from 'classnames';
import {ContentList} from '../../hooks/useBubbleData';
import styles from './index.module.less';

interface FeatureBubbleProps {
    isVisible: boolean;
    onHide: () => void;
    contentList: ContentList;
}
const BUBBLE_STAY_TIME = 5000; // 气泡停留时间

const FeatureBubble: React.FC<FeatureBubbleProps> = memo(
    ({isVisible, onHide, contentList = []}: FeatureBubbleProps) => {
        const [content, setContent] = useState('');
        // 获取随机文案
        const randomContent = useMemo(() => {
            return (
                contentList[Math.floor(Math.random() * contentList.length)]?.content?.[0]?.value ||
                ''
            );
        }, [contentList]);
        useEffect(() => {
            setContent(randomContent);
        }, [randomContent]);

        // 5秒后自动隐藏
        useEffect(() => {
            let timeoutId;
            if (isVisible) {
                timeoutId = setTimeout(() => onHide(), BUBBLE_STAY_TIME);
            }
            return () => {
                if (timeoutId) clearTimeout(timeoutId);
            };
        }, [isVisible, onHide]);

        if (!isVisible || !randomContent) {
            return null;
        }

        return (
            <View
                className={cx(styles.bubble)}
                style={{
                    zIndex: 31
                }}
                // 确保气泡展示在卡片已失效的蒙层之上
            >
                <Text className={cx(styles.content)}>{content}</Text>
                <View className={cx(styles.triangle)} />
            </View>
        );
    }
);

FeatureBubble.displayName = 'FeatureBubble';

export default FeatureBubble;
