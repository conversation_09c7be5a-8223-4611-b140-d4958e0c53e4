import {DispatchEventCallbackParams} from '../../typings/msg.type';
import type {InteractionInfo} from '../../typings';

export interface ImExpFocusDoctorProps {
    data: ImExpFocusDoctorData;
    // 卡片事件统一处理
    onThrowEvent?: (args: DispatchEventCallbackParams) => void;
}

export interface ImExpFocusDoctorData {
    cardStyle?: {
        // 本期新增renderType为2
        renderType?: number;
    };
    content?: ImExpFocusDoctorContent;
    actionInfo?: ActionInfo;
}

export interface ImExpFocusDoctorContent {
    expertName?: string;
    expertHome?: string;
    expertPic?: string;
    expertDepartment?: string;
    expertLevel?: string;
    expertHospital?: string;
    source?: string;
    hospitalFiling?: string;
    hospitalTags?: HospitalTagsData[];
    goodAt?: string;
    enableGuahao?: number;
    type?: string;
    license?: string;
    isCerified?: boolean;
    isOnline?: boolean;
    receiptCount?: RateData;
    consultCount?: RateData;
    replySpeed?: RateData;
    commentScore?: RateData;
    goodCommentRate?: RateData;
    serviceIndicators?: RateData;
    assistants?: AssistantsData;
}

export interface RateData {
    text?: string;
    value?: string;
    highLightColor?: string;
}

export interface HospitalTagsData {
    key?: string;
    text?: string;
}

export interface ActionInfo {
    interaction: string;
    interactionInfo: InteractionInfo;
}

export interface AssistantsData {
    title?: string;
    list?: AssistantsItem[];
}

export interface AssistantsItem {
    name?: string;
    level?: string;
}
