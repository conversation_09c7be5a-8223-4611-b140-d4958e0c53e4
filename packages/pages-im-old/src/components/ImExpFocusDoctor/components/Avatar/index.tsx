import {WImage} from '@baidu/wz-taro-tools-core';
import {FC} from 'react';

import {AvatarProps} from './index.d';

const Avatar: FC<AvatarProps> = ({src}) => {
    return (
        <WImage
            src={src}
            shape='circle'
            style={{
                width: 70,
                height: 70
            }}
            mode='aspectFill'
        />
    );
};

Avatar.displayName = 'Avatar';
export default Avatar;
