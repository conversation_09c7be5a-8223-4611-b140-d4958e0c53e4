import {View, Image} from '@tarojs/components';
import {FC, memo} from 'react';
import cx from 'classnames';

import {imgUrlMap} from '../../../../../src/constants/resourcesOnBos';
import {RateInfoProps} from './index.d';

import styles from './index.module.less';

// 复旦排行标签组件
const RateInfo: FC<RateInfoProps> = ({replySpeed, commentScore}) => {
    const {expRuzhu} = imgUrlMap;
    const needSplit = replySpeed && commentScore;
    const needShow = replySpeed || commentScore;

    return (
        <>
            {needShow && (
                <View className={cx(styles.newRankContainer, 'wz-flex')}>
                    <Image src={expRuzhu} className={cx(styles.huangguan)} />
                    <View className={cx(styles.listContainer, 'c-line-clamp1, wz-flex')}>
                        <View
                            className={cx(
                                'c-line-clamp1, wz-ml-9, wz-fs-33, wz-flex',
                                styles.rankText
                            )}
                        >
                            <View className='wz-flex'>
                                <View>{replySpeed?.text}</View>
                                <View className='wz-ml-9'>{replySpeed?.value}</View>
                            </View>
                            {needSplit && (
                                <View className={cx(styles.splitLine, 'wz-ml-18, wz-mr-18')} />
                            )}
                            <View className='wz-flex, wz-mr-18'>
                                <View>{commentScore?.text}</View>
                                <View className='wz-ml-9'>{commentScore?.value}</View>
                            </View>
                        </View>
                    </View>
                </View>
            )}
        </>
    );
};

export default memo(RateInfo);
