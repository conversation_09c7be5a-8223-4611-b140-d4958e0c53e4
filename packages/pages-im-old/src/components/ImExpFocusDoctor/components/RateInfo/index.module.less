.newRankContainer {
    width: 100%;
    padding-right: 12px;
    margin-top: 12px;
    height: 48px;
    color: #aa6508;

    .listContainer {
        height: 48px;
        line-height: 48px;
        background: #fffbe5e5;
        border-radius: 12px;
        margin-left: -48px;
        padding: 0 6px 0 48px;
    }

    .rankText {
        color: #aa6508;
        font-family: PingFang SC;
        font-weight: 700;

        .splitLine {
            width: 3px;
            height: 30px;
            background: #d562004d;
        }
    }

    .huangguan {
        position: relative;
        width: 284px;
        height: 48px;
        margin-right: 9px;
        flex-shrink: 0;
    }
}
