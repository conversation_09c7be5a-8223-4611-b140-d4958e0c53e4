import cx from 'classnames';
import {memo, FC} from 'react';
import {View} from '@tarojs/components';
import {pxTransform} from '@tarojs/taro';
import {Tag} from '@baidu/wz-taro-tools-core';
import {WiseAuthoritySolid} from '@baidu/wz-taro-tools-icons';

import type {IProps} from './index.d';
import styles from './index.module.less';

const SanjiaTag: FC<IProps> = (props: IProps) => {
    const {content, variant = 'outlined', bgColor = 'inherit', color = '#00c8c8'} = props;

    return (
        <Tag
            size='medium'
            shape='square'
            color='primary'
            variant={variant}
            className={cx(styles.iconTag, 'wz-fw-500')}
            style={{
                backgroundColor: bgColor,
                color,
                paddingTop: pxTransform(8),
                paddingBottom: pxTransform(8),
                fontSize: pxTransform(33),
                fontFamily: 'PingFang SC',
                whiteSpace: 'nowrap'
            }}
        >
            <WiseAuthoritySolid className={styles.icon} size={30} />
            <View className={styles.iconTxt}>{content}</View>
        </Tag>
    );
};

export default memo(SanjiaTag);
