import {Tag} from '@baidu/wz-taro-tools-core';
import {View, Text} from '@tarojs/components';
import {pxTransform} from '@tarojs/taro';
import cx from 'classnames';
import {FC, useMemo} from 'react';
import SanjiaTag from '../SanjiaTag';
import {isEmpty} from '../../../../utils';
import RateInfo from '../RateInfo';
import {IExpInfoProps} from './index.d';
import styles from './index.module.less';

const ExpDoctorInfo: FC<IExpInfoProps> = props => {
    const {
        expertDepartment,
        expertName,
        expertLevel,
        expertHospital,
        hospitalTags,
        commentScore,
        replySpeed,
        isCertified
    } = props;

    // 渲染三甲、本地等标签
    const renderhospitalTagsList = useMemo(() => {
        return (
            hospitalTags &&
            hospitalTags?.length > 0 &&
            hospitalTags.map((i, idx) => {
                return i.key && i.key === 'hospitalLevel' ? (
                    <View key={idx} className={cx('wz-mr-18')}>
                        <SanjiaTag content={i.text} />
                    </View>
                ) : (
                    <Tag
                        key={idx}
                        size='medium'
                        shape='square'
                        variant='outlined'
                        style={{
                            padding: `${pxTransform(9)}
                                ${pxTransform(14)} ${pxTransform(8)}`,
                            fontSize: pxTransform(33),
                            backgroundColor: 'inherit',
                            color: '#FF6600',
                            borderColor: 'rgba(255,102,0,0.50)',
                            fontWeight: 'bold'
                        }}
                        className='wz-mr-18'
                    >
                        {i.text}
                    </Tag>
                );
            })
        );
    }, [hospitalTags]);

    return (
        <View className={cx(styles.infoWrapper)}>
            {/* 名称、职称、科室 */}
            <View className={cx(styles.titleWrapper, 'wz-flex')}>
                {/* 名称 */}
                <View className={cx(styles.expertName, 'wz-mr-18')}>{expertName}</View>

                <View className={cx(styles.expertInfo, 'wz-flex')}>
                    {/* 职称 */}
                    <Text className='wz-fw-400 wz-mr-18'>{expertLevel}</Text>
                    {/* 科室 */}
                    <Text className='wz-fw-400 wz-mr-18'>{expertDepartment}</Text>
                </View>
            </View>

            {/* 医院 */}
            <View className={cx(styles.expertHosWrapper, 'wz-flex, wz-mt-30')}>
                <View className={cx(styles.expertHosName, 'wz-taro-ellipsis wz-mr-18')}>
                    {expertHospital}
                </View>
                {!isEmpty(hospitalTags) && (
                    <View className='wz-flex'>{renderhospitalTagsList}</View>
                )}
            </View>

            {/* 已认证·备案、评分 */}
            <View className='wz-mt-27'>
                <RateInfo
                    commentScore={commentScore}
                    replySpeed={replySpeed}
                    isCertified={isCertified}
                />
            </View>
        </View>
    );
};

ExpDoctorInfo.displayName = 'ExpDoctorInfo';
export default ExpDoctorInfo;
