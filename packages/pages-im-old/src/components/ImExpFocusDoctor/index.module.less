.expCardWrapper {
    position: relative;

    .avatarWrapper {
        margin-left: 24px;
        position: relative;
        z-index: 1;
        justify-content: center;
        padding: 36px;

        /* 波纹背景 */
        background-image: url('https://med-fe.cdn.bcebos.com/wenzhen-mini-app/expAvatarBg.png');
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center;
    }

    .expFocusDoctorWrapper {
        margin-top: -84px;
        width: 100%;
        border-radius: 63px;
        box-sizing: border-box;
        background-image: url('https://med-fe.cdn.bcebos.com/wenzhen-mini-app/vita/expFocusPic.png');
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center top;
        position: relative;
        z-index: 3;

        .recWrapper {
            padding: 6px 45px 36px;
            box-sizing: border-box;
            width: 100%;
            border-radius: 0 0 63px 63px;

            .split {
                height: 1px;
                background: #e0e0e0;
                width: 100%;
            }

            .recContent {
                text-align: justify;
                line-height: 1.5;
                position: relative;

                .goodAtIcon {
                    position: relative;
                    top: 3px;
                }

                .textSplit {
                    display: inline-block;
                    width: 3px;
                    height: 36px;
                    background: #848691;
                    position: relative;
                    top: 3px;
                }

                .showMoreBtn {
                    position: absolute;
                    bottom: 0;
                    right: 0;
                    background: linear-gradient(89.79deg, rgb(255 255 255 / 0%) 0.18%, #fff 35.47%);
                    padding-left: 90px;
                    color: #525252;
                }
            }

            .tenLines {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: pre-wrap;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 10;
            }
        }
    }
}
