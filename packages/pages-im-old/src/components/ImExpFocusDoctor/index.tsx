import {View} from '@tarojs/components';
import cx from 'classnames';
import {WImage} from '@baidu/wz-taro-tools-core';
import {WiseDownArrow} from '@baidu/wz-taro-tools-icons';
import {type FC, memo, useCallback, useEffect, useMemo, useRef, useState} from 'react';

import {imgUrlMap} from '../../../src/constants/resourcesOnBos';
import {getSystemInfo, truncatedString} from '../../../../utils-shared';
import {ubcCommonViewSend, ubcCommonClkSend} from '../../utils/generalFunction/ubc';

import {getDataForUbcAtom} from '../../store/docImAtom/index';

import type {InteractionType} from '../../typings';

import Avatar from './components/Avatar';
import ExpDoctorInfo from './components/DoctorInfo';

import type {ImExpFocusDoctorProps} from './index.d';

import styles from './index.module.less';

/**
 * 定向诊前医生大卡迁移
 * 为防止和wz-taro-tools冲突，重命名组件为ImExpFocusDoctor
 */
const ImExpFocusDoctor: FC<ImExpFocusDoctorProps> = ({data, onThrowEvent}) => {
    const sys = getSystemInfo();

    const {content} = data;
    const [showMoreBtn, setShowMoreBtn] = useState(false);
    const [showTenLines, setShowTenLines] = useState(false);
    const goodAtRef = useRef<HTMLElement>();
    const {interaction, interactionInfo} = data?.actionInfo || {};

    // 整卡点击事件
    const clickFocusDoctor = useCallback(() => {
        onThrowEvent &&
            onThrowEvent({
                interaction: interaction as InteractionType,
                interactionInfo: interactionInfo!
            });

        ubcCommonClkSend({
            value: 'ImVitaDoctor',
            ext: {
                product_info: {
                    ...getDataForUbcAtom()?.product_info
                }
            }
        });
    }, [interaction, interactionInfo, onThrowEvent]);

    // 获取文本是否被截断
    useEffect(() => {
        if (
            process.env.TARO_ENV === 'h5' &&
            goodAtRef.current?.scrollHeight &&
            goodAtRef?.current?.clientHeight
        ) {
            setShowMoreBtn(goodAtRef.current?.scrollHeight > goodAtRef?.current?.clientHeight);
        }

        // 手百截断
        else if (process.env.TARO_ENV === 'swan') {
            const ratio = sys.screenWidth / 414;
            let boxWidth = 0;

            // 兜底
            if (!ratio) {
                boxWidth = 414 - 30 - 24;
            }

            boxWidth =
                sys.screenWidth - Number((30 * ratio).toFixed(1)) - Number((24 * ratio).toFixed(1));

            const {isTruncated} = truncatedString(
                content?.goodAt,
                boxWidth,
                sys.bigFontSizeClass ? 18 : 15,
                2,
                '',
                '展开',
                30
            );
            if (isTruncated) {
                setShowMoreBtn(true);
            }
        }
    }, [content?.goodAt, sys.bigFontSizeClass, sys.screenWidth]);

    const handleMoreEvent = useCallback(e => {
        e?.stopPropagation();
        setShowMoreBtn(false);
        setShowTenLines(true);
        ubcCommonClkSend({
            value: 'ImVitaDoctor_more_btn',
            ext: {
                product_info: {
                    ...getDataForUbcAtom()?.product_info
                }
            }
        });
    }, []);

    useEffect(() => {
        showMoreBtn &&
            ubcCommonViewSend({
                value: 'ImVitaDoctor_more_btn',
                ext: {
                    product_info: {
                        ...getDataForUbcAtom()?.product_info
                    }
                }
            });
    }, [showMoreBtn]);

    useEffect(() => {
        ubcCommonViewSend({
            value: 'ImVitaDoctor',
            ext: {
                product_info: {
                    ...getDataForUbcAtom()?.product_info
                }
            }
        });
    }, []);

    const renderRecContent = useMemo(() => {
        return (
            <>
                <WImage
                    src={imgUrlMap?.goodAt}
                    style={{
                        width: 25,
                        height: 12
                    }}
                    className={cx(styles.goodAtIcon)}
                />
                <View className={cx(styles.textSplit, 'wz-ml-18, wz-mr-18')} />
                {content?.goodAt}
                {showMoreBtn && (
                    <View
                        className={cx(styles.showMoreBtn, 'wz-flex')}
                        onClick={e => handleMoreEvent(e)}
                    >
                        展开
                        <WiseDownArrow />
                    </View>
                )}
            </>
        );
    }, [content?.goodAt, handleMoreEvent, showMoreBtn]);

    return (
        <View className={cx(styles.expCardWrapper, 'wz-flex-col')}>
            {/* 医生头像 */}
            <View className={cx(styles.avatarWrapper, 'wz-flex')} onClick={clickFocusDoctor}>
                <Avatar src={content?.expertPic} />
            </View>
            <View
                className={cx(styles.expFocusDoctorWrapper, 'wz-fs-42')}
                onClick={clickFocusDoctor}
            >
                {/* 医生信息 */}
                <ExpDoctorInfo
                    expertDepartment={content?.expertDepartment}
                    expertName={content?.expertName}
                    expertLevel={content?.expertLevel}
                    expertHospital={content?.expertHospital}
                    hospitalTags={content?.hospitalTags}
                    commentScore={content?.commentScore}
                    replySpeed={content?.replySpeed}
                    isCertified={content?.isCerified}
                />

                {/* 推荐理由 */}
                {content?.goodAt && (
                    <View className={cx(styles.recWrapper)}>
                        <View className={cx(styles.split)}></View>
                        <View
                            id='expGoodAt'
                            ref={goodAtRef}
                            className={cx(
                                styles.recContent,
                                'wz-fs-42, wz-fw-400, wz-mt-36',
                                !showTenLines ? 'wz-taro-ellipsis--l2' : styles.tenLines
                            )}
                        >
                            {renderRecContent}
                        </View>
                    </View>
                )}
            </View>
        </View>
    );
};

ImExpFocusDoctor.displayName = 'ImExpFocusDoctor';
export default memo(ImExpFocusDoctor);
