.passContainer {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    background: rgba(200, 200, 200, 0.7);
    z-index: 9999;
    justify-content: center;
    align-items: center;

    .passBox {
        border-radius: 12px;
        overflow: hidden;
    }
}

.passArea {
    width: 100%;
    height: 100%;
    position: fixed;
}

.passView {
    width: 100%;
    height: 100%;
}
