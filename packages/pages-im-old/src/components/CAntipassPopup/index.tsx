import { nextTick } from '@tarojs/taro';
import { View } from '@tarojs/components';
import { type FC, memo, useState, useCallback, useEffect } from 'react';

import { API_HOST } from '../../models/apis/host';
import { getUseprotocolDetail } from '../../models/services/common';

import { generateHumanVerification } from '../../utils/basicAbility/vac';
import { type FkParamsVal } from '../../utils/generalFunction/riskControler';

import type { InteractionInfo, Qid } from '../../typings';

import styles from './index.module.less';

interface IProps {
    fkParams: FkParamsVal;
    data: {
        interaction: string;
        interactionInfo: InteractionInfo;
    };
    refreshParams: {
        sessionId?: string;
        qid?: Qid;
    };
    callback: () => void;
}

const CAntipassPopup: FC<IProps> = props => {
    const { data, fkParams = {}, refreshParams, callback } = props;
    const { interactionInfo } = data;
    const { url = '', method } = interactionInfo;

    const [showPass, setShowPass] = useState(false);

    const getVerifySucc = useCallback(
        async param => {
            if (url && fkParams) {
                try {
                    const params = {
                        url: `${API_HOST}${url}`,
                        data: {
                            verifyInfo: {
                                ds: param && param.ds,
                                tk: param && param.tk,
                                ...(refreshParams || {})
                            },
                            riskInfo: {
                                ...fkParams,
                                sdk_version: 2
                            }
                        },
                        method,
                        isNeedLogin: false,
                        isFirstScreen: false
                    };
                    const [err, res] = await getUseprotocolDetail(params);
                    if (!err) {
                        const { verified } = res?.data || {};
                        if (verified === 1) {
                            setShowPass(false);
                            callback?.();
                        }
                    }
                } catch (err) {
                    console.error('globalModal getData 出错：', err);
                }
            }
        },
        [url, fkParams, refreshParams, method, callback]
    );

    useEffect(() => {
        if (process.env.TARO_ENV === 'h5' && fkParams) {
            nextTick(() => {
                generateHumanVerification({
                    type: 'spin',
                    nodeId: document.getElementById('passBox'),
                    verifySuccessCallback: d => {
                        getVerifySucc(d);
                    }
                });
            });
            setShowPass(true);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return (
        <>
            {showPass && (
                <View className={styles.passContainer}>
                    {process.env.TARO_ENV === 'h5' && (
                        <View>
                            <View id='passBox' className={styles.passBox} />
                        </View>
                    )}
                </View>
            )}
        </>
    );
};

export default memo(CAntipassPopup);
