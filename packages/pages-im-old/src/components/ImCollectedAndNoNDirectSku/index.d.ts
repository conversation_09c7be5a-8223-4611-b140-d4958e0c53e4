import {MsgId} from '../../typings';

import type {CollectedInfoProps} from './PatientCard/index.d';
import type {RequireDescProps} from './ConsultForm/index.d';

export interface ImCollectedAndNoNDirectSkuProps {
    cardId: string;
    cardName: string;
    version: number;
    data: {
        content: ImNoNDirectSkuPData;
        ext?: {
            scene?: string;
        };
    };
    msgId: MsgId;
}

// 就诊卡片和sku数据结构
export interface ImNoNDirectSkuPData {
    requireDesc: RequireDescProps;
    collectedInfo: CollectedInfoProps;
    skuData: ImUndirectServicetCon;
    lineText?: string;
    isExpired?: boolean;
    hideTopSkuArea?: boolean;
}

export type OpenPatientType = 'sku' | 'patient';

export type CONSULT_TYPE = 'consultList' | 'consult'; // consultList是就诊人选择弹层，consult是就诊人编辑弹层

export interface IDetailInfo {
    title?: string;
    doctorTitle?: string;
    discount?: string;
    features?: string[];
    explainTitle?: string;
    explains?: string[];
    oldPrice?: string; // 服务价格
    finalPrice?: string; // 合计价格
    btn?: string;
    promotionReductionPrice?: string; // 平台立减 活动优惠(减价)
    couponReductionPrice?: string; // 用券优惠(减价)
}

export interface IConfirm {
    title?: string;
    confirmText?: string;
    cancelText?: string;
}

export interface IBtn {
    interaction?: string;
    interactionInfo?: {
        url?: string;
        confirm?: IConfirm;
    };
    value?: string;
    disabled?: boolean;
}

export interface undirectService {
    title?: string;
    priceText?: string;
    orgPrice?: string;
    sub?: string;
    detailInfo?: IDetailInfo;
    skuId?: string;
    tag?: string;
    btn?: IBtn;
}
export interface DoctorDisplayArea {
    avatars: string[];
    words: string;
    isCarousel: boolean;
}
export interface TopArea {
    doctorDisplayArea: DoctorDisplayArea;
}
export interface ImUndirectServicetCon {
    btnInfo?: IBtn;
    subText?: string;
    list?: undirectService[] | [];
    topArea?: TopArea;
    titleImg?: string;
    tipText?: string;
    serviceTip?: string;
    toastTip?: string;
}
