import cx from 'classnames';
import {View, Text} from '@tarojs/components';
import {memo, FC, useMemo, useCallback, useState} from 'react';
import {Tag, Popup, WImage} from '@baidu/wz-taro-tools-core';
import {WiseRightArrow} from '@baidu/wz-taro-tools-icons';
import {HButton} from '@baidu/health-ui';
import {pxTransform} from '@tarojs/taro';
import {CLoginButton, Portal} from '@baidu/vita-ui-cards-common';

import {undirectService} from '../index.d';
import {isEmpty} from '../../../utils';
import {imgUrlMap} from '../../../constants/resourcesOnBos';
import {showToast} from '../../../utils/customShowToast';
import {useScrollControl} from '../../../hooks/common/useScrollControl';

import ServiceTitle from './component/ServiceTitle';
import ServicePopup from './component/ServicePopup';
import type {UndirectServiceDataV2Props} from './index.d';
import styles from './index.module.less';

/**
 *
 * @description 定向服务卡 v2 版本
 * @returns
 */
const UndirectService: FC<UndirectServiceDataV2Props> = props => {
    const {
        skuData = {},
        updateCollectedInfoAndSku,
        lineText,
        isLogin,
        updateSkuDetailData,
        isSkuDetailPopupShow,
        setIsSkuDetailPopupShow,
        isSkuDisabled,
        skuDisabledToastText,
        hideTopSkuArea,
        adjectiveRecommendUnDirectMsgId
    } = props;
    const {subText, topArea, titleImg, list, btnInfo, serviceTip, toastTip} = skuData;
    const [selectSku, setSelectSku] = useState();
    const {scrollToMessage} = useScrollControl();

    // 展示 提示浮层
    const showTip = useCallback(
        (detail, e) => {
            e.stopPropagation();
            if (detail && !isEmpty(detail.detailInfo)) {
                setIsSkuDetailPopupShow(true);
                setSelectSku(detail);
            } else {
                setSelectSku(detail);
            }
            updateSkuDetailData(detail);
        },
        [setIsSkuDetailPopupShow, updateSkuDetailData]
    );

    // 渲染sku列表
    const renderList = useMemo(() => {
        return (
            list &&
            list.length > 0 &&
            list.map((sku: undirectService) => {
                const {skuId = '', title, sub, detailInfo, priceText, orgPrice, tag} = sku;
                return (
                    <View key={skuId} className={cx(styles.skuItem, 'wz-flex')}>
                        <View className={styles.left}>
                            <CLoginButton
                                isLogin={isLogin}
                                closeShowNewUserTag={true}
                                useH5CodeLogin={true}
                                onLoginFail={error => {
                                    console.error('error', error);
                                }}
                                onLoginSuccess={() => {
                                    updateCollectedInfoAndSku('sku', sku);
                                }}
                            >
                                <View
                                    className={cx(
                                        styles.consult,
                                        'wz-flex wz-col-center wz-fs-57 wz-fw-500'
                                    )}
                                >
                                    {title && <View>{title}</View>}
                                    {tag && (
                                        <Tag
                                            className={cx(styles.tagWrapper, 'wz-ml-18')}
                                            size='medium'
                                            shape='square'
                                            variant='outlined'
                                            style={{
                                                padding: `${pxTransform(9)} ${pxTransform(14)} ${pxTransform(8)}`,
                                                fontSize: pxTransform(33),
                                                backgroundColor: 'inherit',
                                                color: '#fd503e',
                                                borderColor: 'rgba(253, 80, 62, 0.5)'
                                            }}
                                        >
                                            <View className={styles.tagTxt}>{tag}</View>
                                        </Tag>
                                    )}
                                </View>
                            </CLoginButton>
                            {sub && (
                                <View
                                    className={cx(
                                        styles.skus,

                                        'wz-flex wz-col-center wz-fs-42 wz-mt-27'
                                    )}
                                    onClick={e => showTip(sku, e)}
                                >
                                    <Text className={styles.line1}>{sub}</Text>
                                    {!isEmpty(detailInfo) && (
                                        <WiseRightArrow color='#B8B8B8' size={36} />
                                    )}
                                </View>
                            )}
                        </View>
                        <CLoginButton
                            isLogin={isLogin}
                            closeShowNewUserTag={true}
                            useH5CodeLogin={true}
                            onLoginFail={error => {
                                console.error('error', error);
                            }}
                            onLoginSuccess={() => {
                                updateCollectedInfoAndSku('sku', sku);
                            }}
                        >
                            <View />
                        </CLoginButton>
                        <CLoginButton
                            isLogin={isLogin}
                            closeShowNewUserTag={true}
                            useH5CodeLogin={true}
                            onLoginFail={error => {
                                console.error('error', error);
                            }}
                            onLoginSuccess={() => {
                                updateCollectedInfoAndSku('sku', sku);
                            }}
                        >
                            <View className={styles.right}>
                                <View
                                    className={cx(
                                        'wz-flex',
                                        'wz-col-center',
                                        'wz-text-right',
                                        'wz-mb-15',
                                        'wz-row-right',
                                        'wz-pr-45',
                                        styles.priceArea
                                    )}
                                >
                                    <View
                                        className={cx('wz-fs-36', 'wz-mr-18', styles.originPrice)}
                                    >
                                        {orgPrice}
                                    </View>
                                    <View className={cx('wz-fs-48', styles.curPrice)}>
                                        {priceText}
                                    </View>
                                </View>
                                <View
                                    className={cx(
                                        'wz-flex',
                                        'wz-col-center',
                                        'wz-row-center',
                                        'wz-fs-42',
                                        'wz-fw-500',
                                        'wz-ml-12',
                                        styles.toConsult
                                    )}
                                >
                                    {sku?.btn?.value}
                                </View>
                            </View>
                        </CLoginButton>
                    </View>
                );
            })
        );
    }, [isLogin, list, showTip, updateCollectedInfoAndSku]);

    /**
     * 渲染顶部 非定向SKU 首卡组件
     *
     * @returns 返回渲染的 JSX 元素，如果 hideTopSkuArea 为 true，则返回 null
     */
    const renderTopSku = () => {
        if (hideTopSkuArea) return null;

        return (
            <>
                <ServiceTitle
                    subText={subText}
                    topArea={topArea}
                    titleImg={titleImg}
                    serviceTip={serviceTip}
                />
                {btnInfo && (
                    <CLoginButton
                        isLogin={isLogin}
                        closeShowNewUserTag={true}
                        useH5CodeLogin={true}
                        onLoginFail={error => {
                            console.error('error', error);
                        }}
                        onLoginSuccess={() => {
                            if (!btnInfo?.disabled) {
                                updateCollectedInfoAndSku('sku', {btn: btnInfo});
                            } else {
                                showToast({
                                    title: toastTip,
                                    icon: 'none'
                                });
                            }
                        }}
                        disableSuccessToast={true}
                    >
                        <HButton
                            text={btnInfo?.value}
                            height={114}
                            rootStyle={{width: '100%', boxSizing: 'border-box'}}
                            className={cx(styles.btn, 'wz-mt-27 wz-mb-45 wz-fw-500')}
                            disabled={btnInfo?.disabled}
                        />
                    </CLoginButton>
                )}

                {lineText && (
                    <View className={cx(styles['horizontal-line-container'], 'wz-pb-9 wz-pt-12')}>
                        <View className={styles['horizontal-line']} />
                        <View className='c-color-desc wz-ml-48 wz-mr-48 wz-fs-42, wz-text-center'>
                            <Text className={cx(styles['horizontal-line-tip'], 'wz-plr-48')}>
                                {lineText || '更多服务推荐'}
                            </Text>
                        </View>
                    </View>
                )}
            </>
        );
    };

    return (
        <View className={styles.undirectService}>
            {renderTopSku()}
            <View className={styles.skuWrapper}>{renderList}</View>
            <Portal>
                <Popup
                    open={isSkuDetailPopupShow}
                    onClose={() => setIsSkuDetailPopupShow(false)}
                    rounded
                    placement='bottom'
                    title='服务详情'
                    titleStyle={{borderBottom: 'none'}}
                >
                    <Popup.Close />
                    <ServicePopup
                        selectSku={selectSku}
                        updateCollectedInfoAndSku={updateCollectedInfoAndSku}
                        isLogin={isLogin}
                    />
                </Popup>
            </Portal>
            {/* 非最新的sku卡，不可点击，有遮罩层 */}
            {isSkuDisabled && (
                <View
                    className={styles.isDisabledContainer}
                    onClick={() => {
                        showToast({
                            title: skuDisabledToastText || '当前推荐已失效，请点击新的服务卡',
                            icon: 'none'
                        });
                        adjectiveRecommendUnDirectMsgId &&
                            scrollToMessage(adjectiveRecommendUnDirectMsgId, 'undirectService');
                    }}
                >
                    <WImage src={imgUrlMap.ineffectiveIcon} className={styles.ineffectiveIcon} />
                </View>
            )}
        </View>
    );
};

export default memo(UndirectService);
