.textColor {
    color: #858585;
    position: relative;
}

.title {
    display: flex;
    align-items: center;
}

.mainText {
    color: #1f1f1f;
    font-weight: bold;
    font-style: italic;
}

.expertAvatar {
    width: 63px;
    height: 63px;
    border: 4px solid #fff;
    border-radius: 32px;
    box-sizing: border-box;
}

.rotation {
    position: absolute;
    left: 21px;
    height: 63px;
    width: 168px;
}

.rotationContainer {
    display: flex;
    align-items: center;
}

.onlineDoc {
    padding-left: 200px;
    height: 67px;
    line-height: 67px;
}

.titleImg {
    width: 270px;
    height: 48px;
}
