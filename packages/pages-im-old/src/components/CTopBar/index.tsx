import cx from 'classnames';
import {HIcon} from '@baidu/health-ui';
import {View, Text, Block} from '@tarojs/components';
import Taro, {
    useError,
    showModal,
    setClipboardData,
    pxTransform,
    getMenuButtonBoundingClientRect
} from '@tarojs/taro';
import {CSSProperties, useState, useEffect, memo, FC, useMemo, useCallback, useRef} from 'react';
import {getCurrentPage, getAppInfo, getQueryStr} from '@baidu/vita-utils-shared';
import {getDataForUbcAtom} from '../../store/triageStreamAtom/index';
import {UrlParamsType} from '../../typings';
import {useGetUrlParams} from '../../hooks/common';
import globalData from '../../globalDataStore/globalData';
import {getSystemInfo} from '../../utils/taro';
import {showToast} from '../../utils/customShowToast';
// import { inNianJianHn, inNianJianYc } from '../../utils/env';
import {navigate} from '../../utils/basicAbility/commonNavigate';
import {getPageBaseUrl, isWxMiniProgramWebView} from '../../utils';
import CMenu from '../CMenu';

import './index.less';
import {CTopBarProps, IConfig} from './index.d';

const CTopBar: FC<CTopBarProps> = ({
    title = '',
    subtitle = '',
    subtitleUrl = '',
    subTitleSlot,
    titleSlot,
    titleCustomSlot,
    titleRightSlot,
    titleLeftSlot = '',
    menu,
    isLogin,
    hideTitle = false,
    barHeight = 0,
    textColor = '#1F1F1F',
    delaySetHeaderColor = false,
    delayCompatDevices = false,
    zIndex = 99,
    blank = true,
    barBg = '',
    blankBg = '',
    slotHeight = 0,
    fixedBgHeight = 0,
    fixedBgZIndex = 0,
    isFixed = true,
    hideHome = false,
    hideBack = false,
    icon = '',
    backDetain = false,
    homeUrlParams,
    iconBg = false,
    isForceBack = false,
    isHideHeader = true,
    isInTabBar = false,
    topBarHeightChanged,
    introPageHome,
    onBackDetain,
    className = 'f5',
    children,
    navigateCallBack
}) => {
    const [state, setState] = useState<IConfig>({
        statusBarHeight: 20,
        navigationBarHeight: 44,
        navigateBackShow: false,
        isHigherNavigate: false,
        hidePageHeader: false,
        showHome: false,
        barPageClass: '',
        env: process.env.TARO_ENV,
        backStyle: {
            marLeft: '',
            marTop: ''
        },
        backToHome: false
    });

    const errMsg = useRef({});
    const appInfo = getAppInfo();

    const urlParms = useGetUrlParams();
    const [displayTitle, setDisplayTitle] = useState('');
    const [topBarHeight, setTopBarHeight] = useState(44);
    const [menuButtonWidth, setMenuButtonWidth] = useState(0);
    const currentPage = useMemo(() => getCurrentPage(), []);

    const onLongTap = useCallback(() => {
        // 只有bot页面有此长按功能
        if (currentPage?.route?.indexOf('vita/pages/im/index') === -1) {
            return;
        }
        const productInfo = getDataForUbcAtom()?.product_info || {};
        const _urlParms = urlParms;
        const queryStr = getQueryStr(_urlParms);
        const content =
            `页面参数：${queryStr}\n` +
            `IM信息：${JSON.stringify(productInfo)}\n` +
            `Error：${JSON.stringify(errMsg.current)}`;
        showModal({
            title: '参数信息',
            content,
            confirmText: '复制',
            success(res) {
                if (res.confirm) {
                    setClipboardData({
                        data: content,
                        success: () => {
                            showToast({title: '复制成功', icon: 'none'});
                        }
                    });
                }
            }
        });
    }, [currentPage?.route, urlParms]);

    const toHomePage = useCallback(() => {
        if (process.env.TARO_ENV === 'h5') {
            const curPathname = currentPage.route;
            if (curPathname && curPathname.indexOf('pages') === -1) {
                window.location.href = `${getPageBaseUrl()}/${appInfo.home}`;
                return;
            }
        }
        navigate({
            openType: process.env.TARO_ENV === 'weapp' ? 'switchTab' : 'relaunch',
            url: appInfo.home
        });
    }, [appInfo, currentPage.route]);

    const navigateHome = useCallback(() => {
        if (homeUrlParams) {
            navigate({
                openType: 'navigate',
                ...homeUrlParams
            });
        } else {
            toHomePage();
        }
    }, [homeUrlParams, toHomePage]);

    const navigateBack = useCallback(
        (delta = 1) => {
            if (currentPage.isOnlyPage && !(process.env.TARO_ENV === 'h5')) {
                if (process.env.TARO_ENV === 'swan') {
                    // @ts-expect-error swan is a global object in Baidu Mini Program
                    // eslint-disable-next-line no-undef
                    swan?.exit?.();
                }
            } else {
                if (process.env.TARO_ENV === 'h5') {
                    window.history.go(-delta);
                } else {
                    Taro.navigateBack({delta});
                }
            }
        },
        [currentPage.isOnlyPage]
    );

    const handleNavigation = useCallback(
        (eventName: 'navigateBack' | 'navigateHome') => {
            if (eventName === 'navigateBack' && state.backToHome) {
                toHomePage();
                return;
            }

            introPageHome?.(eventName);
            if (backDetain) {
                onBackDetain?.(eventName, navigateBack, navigateHome);
                return;
            }

            if (eventName === 'navigateBack') {
                navigateCallBack?.() || navigateBack();
            } else {
                navigateHome();
            }
        },
        [
            backDetain,
            state.backToHome,
            toHomePage,
            navigateBack,
            navigateHome,
            onBackDetain,
            introPageHome,
            navigateCallBack
        ]
    );

    const navigateBackBtnShow = useMemo(() => {
        try {
            if (process.env.TARO_ENV === 'weapp') {
                if (hideBack) {
                    return false;
                }
                return currentPage.isOnlyPage ? false : !hideBack || state.navigateBackShow;
            }

            return state.navigateBackShow;
        } catch (err) {
            console.error('计算 navigateBackBtnShow 出错：', err);
            return true;
        }
    }, [currentPage.isOnlyPage, hideBack, state.navigateBackShow]);

    const genBackIcon = useMemo(() => {
        if (navigateBackBtnShow) {
            return (
                <HIcon
                    value='wise-left-arrow'
                    className={cx('icon wz-mr-24', iconBg ? 'icon-bg' : '')}
                    color={textColor}
                    size={66}
                    rootStyle={{
                        marginLeft: state.backStyle.marLeft,
                        marginTop: state.backStyle.marTop
                    }}
                    onClick={() => handleNavigation('navigateBack')}
                />
            );
        }
        return (
            <View
                style={{
                    display: 'inline-block',
                    width: state.backStyle.marLeft
                }}
            />
        );
    }, [
        navigateBackBtnShow,
        iconBg,
        textColor,
        state.backStyle.marLeft,
        state.backStyle.marTop,
        handleNavigation
    ]);

    const navHealthCom = useCallback(() => {
        if (!subtitleUrl) {
            return;
        }
        navigate({
            url: subtitleUrl,
            openType: 'navigate'
        });
    }, [subtitleUrl]);

    const genSubTitle = useMemo(() => {
        if (subTitleSlot) {
            return subTitleSlot;
        } else if (subtitle) {
            return (
                <View
                    className={cx(
                        'wz-flex',
                        'wz-col-center',
                        'wz-row-center',
                        'wz-fs-33',
                        'wz-text-center'
                    )}
                    onClick={navHealthCom}
                >
                    <Text className='subtitle wz-text-center'>{subtitle}</Text>
                    {subtitleUrl && <HIcon value='wise-right-arrow' size={33} />}
                </View>
            );
        }
        return null;
    }, [navHealthCom, subTitleSlot, subtitle, subtitleUrl]);

    const renderRightSection = useCallback(() => {
        if (state.hidePageHeader) return null;

        return (
            <>
                {titleSlot && <View className='c-span6'>{titleSlot}</View>}
                {titleCustomSlot && <View>{titleCustomSlot}</View>}
                {state.env === 'h5' ? null : menu ? (
                    <CMenu menu={menu} menuButtonWidth={menuButtonWidth} isLogin={isLogin} />
                ) : !titleSlot ? (
                    <View className='c-span3' />
                ) : null}
                {state.env === 'h5' && !titleSlot && menu?.length && (
                    <View className='c-span3 wz-mr-36 wz-flex wz-col-bottom wz-row-right'>
                        <CMenu menu={menu} isLogin={isLogin} />
                    </View>
                )}
                {titleRightSlot && !titleSlot && state.env === 'h5' && (
                    <View className='c-span1'>{titleRightSlot}</View>
                )}
            </>
        );
    }, [
        state.hidePageHeader,
        state.env,
        menuButtonWidth,
        titleSlot,
        titleCustomSlot,
        menu,
        isLogin,
        titleRightSlot
    ]);

    const renderNavigationBar = useCallback(() => {
        if (state.hidePageHeader) return null;
        return (
            <View className='bar c-row' style={{height: `${state.navigationBarHeight}Px`}}>
                <View className='c-span3 left wz-flex'>
                    <Block>
                        {genBackIcon}
                        {state.isHigherNavigate && (
                            <View className='icon-blank c-inline-block'>
                                {iconBg && (
                                    <View
                                        className={cx('icon-blank-bg', iconBg ? 'icon-bg' : '')}
                                    />
                                )}
                            </View>
                        )}
                        {state.showHome && !hideHome && (
                            <HIcon
                                value='wise-home'
                                className={cx('icon', iconBg ? 'icon-bg' : '')}
                                rootStyle={
                                    !state.navigateBackShow ? {marginLeft: pxTransform(18)} : {}
                                }
                                color={textColor}
                                size={66}
                                onClick={() => handleNavigation('navigateHome')}
                            />
                        )}
                        {hideHome && titleLeftSlot && (
                            <View
                                className={hideBack ? cx('wz-pl-9', 'title') : 'title'}
                                style={{color: textColor}}
                                onLongPress={onLongTap}
                                onLongTap={onLongTap}
                            >
                                {icon && (
                                    <View
                                        className='title-icon'
                                        style={{backgroundImage: `url(${icon})`}}
                                    />
                                )}
                                {titleLeftSlot}
                            </View>
                        )}
                    </Block>
                </View>
                <View>
                    {displayTitle && !hideTitle && !state.hidePageHeader && (
                        <View
                            className='title'
                            style={{color: textColor}}
                            onLongPress={onLongTap}
                            onLongTap={onLongTap}
                        >
                            {icon && (
                                <View
                                    className='title-icon'
                                    style={{backgroundImage: `url(${icon})`}}
                                />
                            )}
                            {displayTitle}
                        </View>
                    )}
                    {genSubTitle}
                </View>
                {renderRightSection()}
            </View>
        );
    }, [
        state.hidePageHeader,
        state.navigationBarHeight,
        state.isHigherNavigate,
        state.showHome,
        state.navigateBackShow,
        genBackIcon,
        iconBg,
        hideHome,
        textColor,
        displayTitle,
        hideTitle,
        onLongTap,
        icon,
        hideBack,
        genSubTitle,
        renderRightSection,
        handleNavigation,
        titleLeftSlot
    ]);

    const contentStyle = useMemo(() => {
        return {
            position: isFixed ? 'fixed' : 'absolute',
            height: `${state.statusBarHeight + state.navigationBarHeight}Px`,
            paddingTop: `${state.statusBarHeight}Px`,
            zIndex,
            background: barBg
        } as CSSProperties;
    }, [isFixed, state, zIndex, barBg]);

    const compatDevices = systemInfo => {
        if (systemInfo.platform === 'ios' || systemInfo.platform === 'devtools') {
            state.backStyle.marLeft = pxTransform(18);
        }
        if (systemInfo.platform === 'android') {
            state.backStyle.marLeft = pxTransform(8);
        }
        if (systemInfo.brand === 'Redmi' || systemInfo.brand === 'OnePlus') {
            state.backStyle.marTop = pxTransform(8);
            state.backStyle.marLeft = pxTransform(8);
        }
    };

    const setHeaderColor = color => {
        const _color = color === 'white' ? '#fff' : color === 'black' ? '#000' : color;
        const hex = toHexColor(_color).toLowerCase();
        if (hex === '#fff' || hex === '#000') {
            Taro.setNavigationBarColor({
                frontColor: hex,
                backgroundColor: '#fff' // backgroundColor必须，随便写一个
            });
        }
    };

    const toHexColor = (color = '') => {
        if (/#\w{3}/.test(color)) {
            return `#${color
                .replace('#', '')
                .split('')
                .map(d => d + d)
                .join('')}`;
        } else if (/#\w{6}/.test(color)) {
            return color;
        } else if (/rgb?\(.+\)/.test(color)) {
            const arr = [];

            // @ts-expect-error override
            color.replace(/(\d+)/g, (w, $1) => arr.push($1));

            return `#${arr.map(d => parseInt(d, 10).toString(16)).join('')}`;
        }

        return '';
    };

    useError(error => {
        errMsg.current = error;
    });

    useEffect(() => {
        setDisplayTitle(title || '');
    }, [title]);

    useEffect(() => {
        setHeaderColor(textColor);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [textColor]);

    useEffect(() => {
        !delaySetHeaderColor && setHeaderColor(textColor);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [delaySetHeaderColor]);

    useEffect(() => {
        !delayCompatDevices && compatDevices(getSystemInfo());
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [delayCompatDevices]);

    useEffect(() => {
        topBarHeightChanged && topBarHeightChanged(topBarHeightGetter());
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [slotHeight]);

    useEffect(() => {
        let query: UrlParamsType = {};
        const systemInfo = getSystemInfo();

        // 是否隐藏home
        let globalHideHome = false;

        query = currentPage.options as UrlParamsType;

        if (!(process.env.TARO_ENV === 'h5')) {
            // 设置bar高度
            state.statusBarHeight = systemInfo.statusBarHeight || 0;
            state.navigationBarHeight = systemInfo.navigationBarHeight || 44;

            state.navigateBackShow = !isInTabBar || !hideBack || false;
            !delaySetHeaderColor && setHeaderColor(textColor);

            if (query && query.back_route === 'home') {
                state.backToHome = true;
            }

            // 判断机型
            !delayCompatDevices && compatDevices(systemInfo);
            // home
            globalHideHome = globalData.get('hdhome') || false;
        } else if (process.env.TARO_ENV === 'h5') {
            // 判断微信内嵌
            // eslint-disable-next-line max-len
            if (
                isWxMiniProgramWebView() ||
                window.sessionStorage.getItem('hdheader') ||
                globalData.get('hdheader')
            ) {
                state.hidePageHeader = true;
            }

            // 设置bar高度
            state.statusBarHeight = 0;
            state.navigationBarHeight = state.hidePageHeader ? 0 : 44;
            const pages = Taro.getCurrentPages();

            // H5走window.history 判断页面栈（可能是mars主站跳过来的）
            const showBackCondition =
                (pages.length === 1 && window.history.length > 1) || pages.length > 1;
            state.navigateBackShow = (!hideBack && showBackCondition) || isForceBack || false;
            // home
            globalHideHome = globalData.get('hdhome') || window.sessionStorage.getItem('hdhome');

            // 处理微信web-view环境展示
            if (isWxMiniProgramWebView() && isHideHeader) {
                state.barPageClass = 'transparent';
                state.hidePageHeader = true;
            }
        }
        state.showHome = !hideHome && !globalHideHome;

        // 设置title
        setDisplayTitle(title || decodeURIComponent(query?.title || '百度健康'));

        setState({
            ...state
        });
        process.env.TARO_ENV !== 'h5' && getMenuButton(systemInfo.screenWidth);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const getMenuButton = (screenWidth: number) => {
        try {
            const {width = 0, right = 0} = getMenuButtonBoundingClientRect();
            setMenuButtonWidth(width + screenWidth - right + 16);
        } catch (err) {
            console.info('err fail', err);
        }
    };

    const topBarHeightGetter = () => {
        const height = barHeight
            ? barHeight
            : +state.statusBarHeight + +state.navigationBarHeight + +(slotHeight || 0);
        setTopBarHeight(height);

        return height;
    };

    return (
        <View
            className={cx('top-bar-wrapper', state.barPageClass || className)}
            style={
                blank
                    ? {
                        height: `${state.statusBarHeight + state.navigationBarHeight}Px`
                    }
                    : {}
            }
        >
            <View className={cx('top-bar', !isFixed ? 'no-fix-wid' : '')} style={contentStyle}>
                {renderNavigationBar()}
                {children}
            </View>

            {/* 吸顶背景延伸（用于被页面内容盖住） */}
            <View
                className='fixedBg'
                style={{
                    top: `${topBarHeight}Px`,
                    height: `${fixedBgHeight}Px`,
                    zIndex: fixedBgZIndex
                }}
            />

            {/* 占位 */}
            {blank && isFixed ? (
                <View
                    className='blank'
                    style={{
                        height: `${topBarHeight}Px`,
                        background: blankBg
                    }}
                />
            ) : null}
        </View>
    );
};

export default memo(CTopBar);
