/**
 * @file: 图片+文字卡片组件
 * @author: gong<PERSON><PERSON><PERSON>
 */
import cx from 'classnames';
import {View} from '@tarojs/components';
import {useCallback, memo, FC, useMemo} from 'react';
import {ImText, WImage} from '@baidu/wz-taro-tools-core';
import {ImImage} from '@baidu/vita-ui-cards-common';
import {previewImage} from '@tarojs/taro';

import styles from '../../pages/im/components/CardComponents/index.module.less';
import imRichMsgStyles from './index.module.less';
import {ImRichMsgProps} from './index.d';

const ImRichMsg: FC<ImRichMsgProps> = props => {
    const {data, textExt, imageExt} = props || {};
    const {content, cardStyle, ext} = data || {};
    const list = content?.list || [];

    const hasImg = useMemo(()=>{
        const images = list?.find(item => item.type === 'images');

        return images?.images && images?.images?.length > 0;
    },[list])

    const getAllImages = useCallback(() => {
        if (!list || !list?.length) {
            return [];
        }

        const images = list?.find(item => item?.type === 'images')?.images?.map(url => url?.origin);
        return images || [];
    }, [list]);
    // 转换组件需要的格式数据
    const formatComponentData = useCallback(
        (arg, customCardStyle) => {
            return {
                cardStyle: {
                    ...cardStyle,
                    ...(customCardStyle || {})
                },
                content: {
                    ...arg,
                    urls: list ? getAllImages() : [],
                },
                ext
            };
        },
        [cardStyle, ext, getAllImages]
    );

    const handlePreview = useCallback((url)=>{
        const imgUrls = list?.find(item => item?.type === 'images')
        if(imgUrls && imgUrls.images && imgUrls.images?.length > 1){
            const urls = imgUrls.images.map(url => url?.origin)
            return  previewImage({
                urls: urls ? urls : [url],
                current: url
            })
        }
        previewImage({
            urls: [url],
            current: url
        })
    },[list])

    // 图片组件
    const getImageCom = useCallback(
        imgList => {
            if (!imgList?.length) {
                return null;
            }
            return (
                // 防止空标签在h5中不渲染
                <View>
                    <View
                        className={cx(
                            imRichMsgStyles.bubbleWrapperImages,
                            'wz-flex',
                            'wz-pl-45',
                            'wz-pr-24',
                            'wz-pt-45',
                            'wz-pb-21'
                        )}
                    >
                        {imgList?.map((item, index) => {

                            const d = formatComponentData(
                                item,
                                {
                                    width: 228,
                                    height: 228
                                }
                            );
                            return (
                                <View key={index} className={imRichMsgStyles.imgItem}>
                                    {/* <WImage mode='scaleToFill' onClick={()=>handlePreview(item?.origin)} className={imRichMsgStyles.imgSingleItem} src={item?.origin} key={index} /> */}
                                    {/* @ts-ignore */}
                                    <ImImage data={d} {...imageExt} key={index} />
                                    {/* 无法解析mode */}
                                    {item?.maskType ? (
                                        <View
                                            className={cx(imRichMsgStyles.notBeResolved, 'wz-flex')}
                                            onClick={()=>handlePreview(item?.origin)}
                                        >
                                            <WImage
                                                className={imRichMsgStyles.attentionIcon}
                                                src='https://med-fe.cdn.bcebos.com/vita/vita_attention.png'
                                            />
                                            {item?.maskContent}
                                        </View>
                                    ) : null}
                                </View>
                            );
                        })}
                        
                    </View>
                    {textExt?.isPrivate ? (
                        <View className={cx(imRichMsgStyles.imgPrivate, 'wz-ptb-18', 'wz-flex')}>
                            <WImage
                                className={imRichMsgStyles['imgWaterMark']}
                                mode='aspectFit'
                                src='https://med-fe.cdn.bcebos.com/mercury/anquan.png'
                            />
                            平台保证隐私安全
                        </View>
                    ) : null}
                </View>
            );
        },
        [imageExt]
    );

    return (
        <View
            className={cx(
                imRichMsgStyles.bubbleWrapper,
                !textExt?.isPrivate ? imRichMsgStyles.bubbleWrapperSystem : '',
                'wz-flex'
            )}
        >
            {list.map((item, index) => {
                if (item?.type === 'images') {
                    return getImageCom(item?.images);
                }
                if (item?.type === 'system_notice') {
                    return (
                        <View
                            key={index}
                            className={cx(imRichMsgStyles.systemMsg, 'wz-flex wz-mt-45')}
                        >
                            <View
                                className={cx(
                                    imRichMsgStyles.systemNotice,
                                    'wz-flex wz-ptb-36 wz-plr-36'
                                )}
                            >
                                {item?.content}
                            </View>
                        </View>
                    );
                }
                if (item?.type === 'text') {
                    if (!item?.content) return;

                    return (
                        <View
                            className={cx(
                                textExt?.isPrivate
                                    ? styles.bubbleWrapper
                                    : styles.bubbleWrapperServicer,
                                'wz-plr-45 wz-ptb-42 wz-flex-col wz-col-top wz-fs-51',
                                imRichMsgStyles.textWrapper,
                                hasImg && 'wz-mt-51'
                            )}
                            key={index}
                        >
                            <ImText
                                key={index}
                                data={formatComponentData({value: item?.content}, {})}
                                {...textExt}
                            />
                        </View>
                    );
                }
            })}
        </View>
    );
};

export default memo(ImRichMsg);
