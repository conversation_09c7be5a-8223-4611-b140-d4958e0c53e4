import type {ImImageProps} from '@baidu/vita-ui-cards-common/ImImage/index.d';

export interface SimpleImage {
    icon?: string;
    small?: string;
    origin?: string;
}

export interface RichMsgItemProps {
    sectionId?: string;
    type?: 'text' | 'images' | 'system_notice';
    content?: string;
    images: SimpleImage[];
}

export interface ImRichMsgContentProps {
    list?: SimpleImage[];
}

export interface TextExtProps {
    lineheight?: string;
    color?: string;
    isPrivate?: boolean;
}

export interface ImRichMsgProps {
    // 数据
    data: CardsData<{list?: RichMsgItemProps[]}>;
    // 是否展示隐私
    isPrivate?: boolean;
    msgId?: string;
    imageExt?: ImImageProps;
    textExt?: TextExtProps;
}

export interface CardsData<T> {
    cardStyle?: CardStyle;
    content?: T;
    ext?: {
        [key: string]: string | number;
    };
}

export interface CardStyle {
    needHead?: boolean;
    renderType?: number;
    width?: number;
    height?: number;
    positionType?: number;
    isHidden?: boolean;
    disablePreview?: boolean;
}
