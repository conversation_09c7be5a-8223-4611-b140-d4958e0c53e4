import cx from 'classnames';
import {View} from '@tarojs/components';
import {memo, type FC, useCallback, useState, useEffect} from 'react';
import {WImage} from '@baidu/wz-taro-tools-core';
import {ImGuahaoDoctorCard, CLoginButton} from '@baidu/vita-ui-cards-common';
import {ArrowDown} from '@baidu/wz-taro-tools-icons';

import {showToast} from '../../../utils/customShowToast';
import {preloadSwanPackage} from '../../../utils/core';
import {imgUrlMap} from '../../../constants/resourcesOnBos';
import {ubcCommonClkSend} from '../../../utils/generalFunction/ubc';
import {addObjectToUrlQuery} from '../../../utils';
import {useScrollControl} from '../../../hooks/common/useScrollControl';
import {useGetUrlParams} from '../../../hooks/common';
import {useGetLastMsgId} from '../../../hooks/triageStream/pageDataController';
import type {expertItem} from '../index.d';

import type {ExpertRecommendationProps} from './index.d';
import styles from './index.module.less';

/**
 *
 * @description 服务卡 v2 版本
 * @returns
 */
const ExpertRecommendation: FC<ExpertRecommendationProps> = props => {
    const {
        expertData = {},
        isLogin,
        updateCollectedInfoAndExpert,
        isExpertDisabled,
        guahaoRecommendExpertMsgId,
        ext,
        msgId
    } = props;
    const {list = []} = expertData;
    const [showMoreBtn, setShowMoreBtn] = useState((list && list.length > 3) || false);
    const [showExpertList, setShowExpertList] = useState<expertItem[]>([]);
    const {scrollToMessage, scrollToBottom} = useScrollControl();
    const {lastMsgId} = useGetLastMsgId();
    const {sf_ref, from} = useGetUrlParams();

    const handleShowMoreExpert = useCallback(
        e => {
            e.stopPropagation();
            setShowMoreBtn(false);
            // 最后一条消息点击查看更多才滚动到底部
            if (lastMsgId === msgId) {
                scrollToBottom('guahao_doctor_card_seeMore');
            }
            ubcCommonClkSend({
                value: 'ImGuahaoRecommendExpert_seeMore',
                ext: {
                    product_info: {
                        msgId,
                        ...ext
                    }
                }
            });
        },
        [ext, msgId]
    );

    const handleExpertClick = useCallback(() => {
        ubcCommonClkSend({
            value: 'ImGuahaoRecommendExpert_click',
            ext: {
                product_info: {
                    msgId,
                    ...ext
                }
            }
        });
    }, [ext, msgId]);

    // 后端无法取到sf_ref、from，临时方案，前端传递
    const formatList = useCallback(
        (oList: expertItem[]) => {
            // 只包含有值的 queryParams
            const queryParams: Record<string, string> = {};
            if (sf_ref) queryParams.sf_ref = sf_ref;
            if (from) queryParams.from = from;

            // 如果两个都没值，就原样返回
            if (Object.keys(queryParams).length === 0) {
                return oList;
            }

            return oList?.map(item => {
                if (item?.actionInfo?.interactionInfo?.url) {
                    item.actionInfo.interactionInfo.url = addObjectToUrlQuery(
                        item.actionInfo.interactionInfo.url,
                        queryParams
                    );
                }
                if (item?.btnInfo?.interactionInfo?.url) {
                    item.btnInfo.interactionInfo.url = addObjectToUrlQuery(
                        item.btnInfo.interactionInfo.url,
                        queryParams
                    );
                }
                return item;
            });
        },
        [from, sf_ref]
    );

    useEffect(() => {
        const refList = formatList(list);
        if (showMoreBtn && showExpertList && refList) {
            setShowExpertList(refList?.slice(0, 3) || []);
        } else {
            setShowMoreBtn(false);
            setShowExpertList(refList);
        }
        if (process.env.TARO_ENV === 'swan') {
            refList &&
                refList.forEach(expert => {
                    preloadSwanPackage({
                        pageUrl: expert?.actionInfo?.interactionInfo?.url
                    });
                });
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [list, showMoreBtn]);

    return (
        <View className={styles.expertRecommendationWrapper}>
            <View className={styles.expertRecommendation}>
                {showExpertList?.length > 0 &&
                    showExpertList?.map((expert, index) => {
                        const len = showExpertList.length;
                        const showRec = (showMoreBtn && index !== len - 1) || !showMoreBtn;

                        return (
                            <View
                                key={expert?.docID || index}
                                className={cx(styles.doctorCard, 'wz-mb-30')}
                            >
                                <CLoginButton
                                    isLogin={isLogin}
                                    closeShowNewUserTag={true}
                                    useH5CodeLogin={true}
                                    onLoginFail={error => {
                                        console.error('error', error);
                                    }}
                                    onLoginSuccess={() => {
                                        handleExpertClick();
                                        updateCollectedInfoAndExpert({
                                            ...expert,
                                            pos: index + 1
                                        });
                                    }}
                                >
                                    <ImGuahaoDoctorCard data={expert} isDisplayRec={showRec} />
                                </CLoginButton>
                                {/* 最后一个专家卡不显示分割线 */}
                                {index !== len - 1 && (
                                    <View className={cx(styles.line, 'wz-mb-30')} />
                                )}
                            </View>
                        );
                    })}
                {/* 非最新的sku卡，不可点击，有遮罩层 */}
                {isExpertDisabled && (
                    <View
                        className={styles.isDisabledContainer}
                        onClick={() => {
                            showToast({
                                title: '当前推荐已失效，请点击新的服务卡',
                                icon: 'none'
                            });
                            guahaoRecommendExpertMsgId &&
                                scrollToMessage(
                                    guahaoRecommendExpertMsgId,
                                    'expertRecommendationWrapper'
                                );
                        }}
                    >
                        <WImage
                            src={imgUrlMap.ineffectiveNewIcon}
                            className={styles.ineffectiveIcon}
                        />
                    </View>
                )}
            </View>
            {/* 展开更多按钮 */}
            {showMoreBtn && (
                <View
                    className={cx(styles.moreBtn, 'wz-fs-42 wz-flex wz-col-center wz-row-center')}
                    onClick={e => handleShowMoreExpert(e)}
                >
                    <View className='wz-flex wz-row-center wz-col-center'>
                        <View>展开更多</View>
                        <ArrowDown className='wz-ml-9' size={42} />
                    </View>
                </View>
            )}
        </View>
    );
};

export default memo(ExpertRecommendation);
