import {Uploader} from '@baidu/wz-taro-tools-core';

import type {CollectedInfoProps} from '../../../PatientCard/index.d';
import type {PatientInfo, IState, IAction} from '../../index.d';
import type {OpenPatientType} from '../../../index.d';

interface FileProps extends Uploader.File {
    fileName?: string;
}



export interface IChoosePatientListProps {
    collectedInfo: CollectedInfoProps;
    onInputItemChange: (key: string, info: PatientInfo) => void;
    handleAddPatient?: () => void;
    formType?: number;
    selectPatientVal?: string;
    handleSelectPatient?: (info: PatientInfo) => void;
    state: IState;
    dispatch: (args: IAction) => void;
    openPatientPopType: OpenPatientType;
    selectPatientData?: PatientInfo;
    files?: FileProps[];
    TIRAGE_MAX_PIC_NUM?: number;
    IMG: {
        tips: string;
    };
    onUpload?: () => void;
    setFiles?: (files: FileProps[]) => void;
}
