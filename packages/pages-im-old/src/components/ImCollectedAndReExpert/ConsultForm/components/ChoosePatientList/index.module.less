.patient-list-card {
    overflow: scroll;
    box-sizing: border-box;

    .listItem {
        height: 174px;

        .genderAndAge {
            flex-shrink: 0;
        }
    }
}

.form-card-radius-bottom {
    border-radius: 0 0 36px 36px;
    margin-top: 0;
}

.editBtn {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: #fff;
    border: 3px solid #b7b9c1;
    box-sizing: border-box;

    &Active {
        border: 15px solid #00c8c8;
    }
}

.titleWrapper {
    height: 174px;
}

.blue {
    color: #00c8c8;
}

.more {
    color: #858585;
}


.inputTel {
    height: 48px;
}

.circle-with-check {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background-color: #00c8c8; /* 背景颜色 */
    position: relative;
}

.circle-with-check::before {
    content: '';
    width: 23px;
    height: 10px;
    border: solid #fff;
    border-width: 0 0 5px 5px;
    position: absolute;
    top: 50%;
    left: 50%;
    margin-top: -5px;
    transform: translate(-50%, -50%) rotate(-45deg);
}

.form__item__label__icon {
    color: #fd503e;
}

.telInputWrapper {
    border-bottom: 0.5px solid #e6e6e6;
}

.line1 {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
}
