.patientContainer {
    background: #F2FAFF;
    margin-bottom: -45px;
    border-radius: 45px 45px 0 0;
    width: 100%;

    &.disable {
        .patientLeft,
        .patientRightBtn {
            opacity: 0.4;
        }
    }

    .patientLeft {
        color: #525252;
        margin-right: 84px;

        .patientLeftLabel {
            flex-shrink: 0;
            max-width: 300px;
        }
    }

    .patientRightBtn {
        border: 1.5px solid #B7B9C1;
        border-radius: 150px;
        color: #272933;
        flex-shrink: 0;
        height: 84px;
        box-sizing: border-box;
    }

    .line1 {
        white-space: nowrap; /* 强制文本在一行显示 */
        overflow: hidden; /* 隐藏超出部分 */
        text-overflow: ellipsis; /* 超出部分显示省略号 */
        width: 100%;
        max-width: 518px;
    }

    .curPatientInfo {
        flex-shrink: 0;
    }

    .noInfo {
        color: #b8b8b8;
    }
}
