import {MsgId} from '../../../typings';

import {expertDataProps, expertItem} from '../index.d';
export interface ExpertRecommendationProps {
    expertData: expertDataProps;
    isLogin: boolean;
    updateCollectedInfoAndExpert: (type: string, expertData: expertItem) => void;
    isExpertDisabled?: boolean;
    adjectiveRecommendExpertMsgId?: MsgId | undefined;
    msgId?: string;
    ext?: {
        [key: string]: string | number;
    };
}
