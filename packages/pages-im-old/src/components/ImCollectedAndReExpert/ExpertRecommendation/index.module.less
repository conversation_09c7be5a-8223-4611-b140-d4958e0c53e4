@import url('../../../style/variable.less');

.expertRecommendationWrapper {
    position: relative;
}

.expertRecommendation {
    width: 100%;
    background: #fff;
    padding: 48px 45px 15px;
    border-radius: 63px;
    overflow: hidden;
    box-sizing: border-box;
    position: relative;
}

.isDisabledContainer {
    position: absolute;
    background: #fff;
    inset: 0;
    opacity: 0.4;
    z-index: @zIndex-doctorMask;

    .isDisabledIcon {
        position: absolute;
        right: 0;
    }

    .ineffectiveIcon {
        width: 198px;
        height: 156px;
        position: absolute;
        top: 0;
        right: 0;
    }
}

.doctorCard {
    overflow: hidden;
}

.line {
    width: 100%;
    height: 1px;
    background: #e6e6e6;
    overflow: hidden;
}

.recommendationIcon {
    margin-bottom: 63px;
}

.moreBtn {
    color: #858585;
    position: absolute;
    bottom: 0;
    background: #fff;
    height: 145px;
    width: 100%;
    z-index: 3;
    border-radius: 45px;
}
