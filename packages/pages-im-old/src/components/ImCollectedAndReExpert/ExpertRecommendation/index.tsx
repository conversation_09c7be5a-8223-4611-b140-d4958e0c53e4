import cx from 'classnames';
import {View} from '@tarojs/components';
import {memo, type FC, useCallback, useState, useEffect} from 'react';
import {WImage} from '@baidu/wz-taro-tools-core';
import {ImDoctorCard, CLoginButton} from '@baidu/vita-ui-cards-common';
import {ArrowDown} from '@baidu/wz-taro-tools-icons';
import {HImage} from '@baidu/health-ui';

import {showToast} from '../../../utils/customShowToast';
import {preloadSwanPackage} from '../../../utils/core';
import {imgUrlMap} from '../../../constants/resourcesOnBos';
import {ubcCommonClkSend} from '../../../utils/generalFunction/ubc';
import {useScrollControl} from '../../../hooks/common/useScrollControl';
import {useGetLastMsgId} from '../../../hooks/triageStream/pageDataController';
import type {expertItem} from '../index.d';

import type {ExpertRecommendationProps} from './index.d';
import styles from './index.module.less';

/**
 *
 * @description 定向服务卡 v2 版本
 * @returns
 */
const ExpertRecommendation: FC<ExpertRecommendationProps> = props => {
    const {
        expertData = {},
        isLogin,
        updateCollectedInfoAndExpert,
        isExpertDisabled,
        adjectiveRecommendExpertMsgId,
        ext,
        msgId
    } = props;
    const {list = []} = expertData;
    const [showMoreBtn, setShowMoreBtn] = useState((list && list.length > 2) || false);
    const [showExpertList, setShowExpertList] = useState<expertItem[]>([]);
    const {scrollToMessage, scrollToBottom} = useScrollControl();
    const {lastMsgId} = useGetLastMsgId();
    const handleShowMoreExpert = useCallback(
        e => {
            e.stopPropagation();
            setShowMoreBtn(false);
            // 最后一条消息点击查看更多才滚动到底部
            if (lastMsgId === msgId) {
                scrollToBottom('doctor_card_seeMore');
            }
            // 滚动到底部
            ubcCommonClkSend({
                value: 'ImAIRecommendExpert_seeMore',
                ext: {
                    product_info: {
                        msgId,
                        ...ext
                    }
                }
            });
        },
        [ext, lastMsgId, msgId, scrollToBottom]
    );

    useEffect(() => {
        if (showMoreBtn && showExpertList && list) {
            setShowExpertList(list?.slice(0, 2) || []);
        } else {
            setShowMoreBtn(false);
            setShowExpertList(list);
        }
        if (process.env.TARO_ENV === 'swan') {
            list &&
                list.map(expert => {
                    preloadSwanPackage({
                        pageUrl: expert?.actionInfo?.interactionInfo?.url
                    });
                });
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [list, showMoreBtn]);

    return (
        <View className={styles.expertRecommendationWrapper}>
            <View className={styles.expertRecommendation}>
                <HImage
                    src={expertData.title || ''}
                    width={207}
                    height={48}
                    className={styles.recommendationIcon}
                />
                {showExpertList?.length > 0 &&
                    showExpertList?.map((expert, index) => {
                        const len = showExpertList.length;
                        const showRec = (showMoreBtn && index !== len - 1) || !showMoreBtn;

                        return (
                            <View
                                key={expert?.docID || index}
                                className={cx(styles.doctorCard, 'wz-mb-30')}
                            >
                                <CLoginButton
                                    isLogin={isLogin}
                                    closeShowNewUserTag={true}
                                    useH5CodeLogin={true}
                                    onLoginFail={error => {
                                        console.error('error', error);
                                    }}
                                    onLoginSuccess={() => {
                                        updateCollectedInfoAndExpert('expert', {
                                            ...expert,
                                            pos: index + 1
                                        });
                                    }}
                                >
                                    <ImDoctorCard data={expert} isDisplayRec={showRec} />
                                </CLoginButton>
                                {/* 最后一个专家卡不显示分割线 */}
                                {index !== len - 1 && (
                                    <View className={cx(styles.line, 'wz-mt-45 wz-mb-15')} />
                                )}
                            </View>
                        );
                    })}
                {/* 非最新的sku卡，不可点击，有遮罩层 */}
                {isExpertDisabled && (
                    <View
                        className={styles.isDisabledContainer}
                        onClick={() => {
                            showToast({
                                title: '当前推荐已失效，请点击新的服务卡',
                                icon: 'none'
                            });
                            adjectiveRecommendExpertMsgId &&
                                scrollToMessage(
                                    adjectiveRecommendExpertMsgId,
                                    'expertRecommendationWrapper'
                                );
                        }}
                    >
                        <WImage
                            src={imgUrlMap.ineffectiveIcon}
                            className={styles.ineffectiveIcon}
                        />
                    </View>
                )}
            </View>
            {/* 展开更多按钮 */}
            {showMoreBtn && (
                <View
                    className={cx(styles.moreBtn, 'wz-fs-42 wz-flex wz-col-center wz-row-center')}
                    onClick={e => handleShowMoreExpert(e)}
                >
                    <View className='wz-flex wz-row-center wz-col-center'>
                        <View>展开更多</View>
                        <ArrowDown className='wz-ml-9' size={42} />
                    </View>
                </View>
            )}
        </View>
    );
};

export default memo(ExpertRecommendation);
