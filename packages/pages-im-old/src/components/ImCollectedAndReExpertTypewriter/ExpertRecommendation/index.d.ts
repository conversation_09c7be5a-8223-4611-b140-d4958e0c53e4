import type {
    undirectServiceUpdatePayload
    // eslint-disable-next-line max-len
} from '../../../components/ImCollectedAndNoNDirectSkuTypewriter/UndirectService/index.d';
import {MsgId} from '../../../typings';

import {expertDataProps, expertItem} from '../index.d';
export interface ExpertRecommendationProps {
    expertData: expertDataProps;
    isLogin: boolean;
    updateCollectedInfoAndExpert?: (type: string, expertData: expertItem) => void;
    isExpertDisabled?: boolean;
    adjectiveRecommendExpertMsgId?: MsgId | undefined;
    msgId?: string;
    ext?: {
        [key: string]: string | number;
    };
    mode?: 'direct' | 'combination';
    // 是否展示骨架屏
    showSkeleton?: boolean;
    isNoUndirectService?: boolean;
    // 默认展示的医生数量
    maxShowLen?: number;
    // 单独兼容组合卡逻辑，不影响定向大卡
    updateCombinationCollectedInfoAndExpert?: (
        type: string,
        payload?: undirectServiceUpdatePayload
    ) => void;
    // 缓存专用
    storageKey?: string;
    // 是否最后一条消息
    isLast?: boolean;
    forceHideMoreBtn?: boolean;
}
