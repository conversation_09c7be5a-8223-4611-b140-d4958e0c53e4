import cx from 'classnames';
import {View} from '@tarojs/components';
import {memo, type FC, useCallback, useState, useEffect, useMemo} from 'react';
import {ImDoctorCard, CLoginButton} from '@baidu/vita-ui-cards-common';
import {ArrowDown} from '@baidu/wz-taro-tools-icons';
import {HImage} from '@baidu/health-ui';
import {useScrollControl} from '../../../hooks/common/useScrollControl';
import {handlerWenzhenStorage} from '../../../utils/index';
import {VITA_RECOMMEND_EXPERT_UNFOLD} from '../../../constants/storageEnv';
import ExpertSkeleton from '../../../components/ImCollectedAndNoNDirectSkuTypewriter/Skeletons/ExpertSkeleton';
import {showToast} from '../../../utils/customShowToast';
import {preloadSwanPackage} from '../../../utils/core';
import {ubcCommonViewSend, ubcCommonClkSend} from '../../../utils/generalFunction/ubc';
import type {expertItem} from '../index.d';
import ExpertRecTitle from './Components/ExpertRecTitle';

import type {ExpertRecommendationProps} from './index.d';
import styles from './index.module.less';

/**
 *
 * @description 定向服务卡 v2 版本
 * @returns
 */
const ExpertRecommendation: FC<ExpertRecommendationProps> = props => {
    const {
        expertData = {},
        isLogin,
        updateCollectedInfoAndExpert,
        isExpertDisabled,
        adjectiveRecommendExpertMsgId,
        ext,
        msgId,
        mode = 'direct',
        showSkeleton = false,
        maxShowLen = 2,
        updateCombinationCollectedInfoAndExpert,
        isNoUndirectService,
        storageKey,
        isLast,
        forceHideMoreBtn
    } = props;
    const unfoldExpertStorage = handlerWenzhenStorage(VITA_RECOMMEND_EXPERT_UNFOLD, 50);
    const {list = [], leftIcon} = expertData;
    const hasLeftIcon = leftIcon && leftIcon.length > 0;

    // store 里是否存在
    const isUnfolded = useMemo(() => {
        const shouldCache = mode === 'combination';
        const isUnfolded = shouldCache && unfoldExpertStorage('get').includes(storageKey || '');
        return isUnfolded;
    }, [mode, storageKey, unfoldExpertStorage]);

    const [showMoreBtn, setShowMoreBtn] = useState(() => {
        if (forceHideMoreBtn) {
            return false;
        }
        return !isUnfolded && list?.length > maxShowLen;
    });

    const [showExpertList, setShowExpertList] = useState<expertItem[]>([]);

    useEffect(() => {
        if (isUnfolded) {
            setShowExpertList(list || []);
        } else {
            setShowExpertList(list?.slice(0, maxShowLen) || []);
        }
    }, [isUnfolded, list, maxShowLen]);

    const {scrollToMessage, scrollToBottom} = useScrollControl();
    const handleShowMoreExpert = useCallback(
        e => {
            e.stopPropagation();
            if (isLast) {
                scrollToBottom('expertRecommendationWrapper');
            }
            setShowMoreBtn(false);
            setShowExpertList(list);
            if (mode === 'combination') {
                unfoldExpertStorage('add', storageKey);
            }
            ubcCommonClkSend({
                value:
                    mode === 'combination'
                        ? 'ImAIRecommendUnDirect_seeMore'
                        : 'ImAIRecommendExpert_seeMore',
                ext: {
                    product_info: {
                        msgId,
                        ...ext
                    }
                }
            });
        },
        [isLast, mode, msgId, ext, scrollToBottom, unfoldExpertStorage, storageKey, list]
    );

    useEffect(() => {
        if (process.env.TARO_ENV === 'swan') {
            list &&
                list.map(expert => {
                    preloadSwanPackage({
                        pageUrl: expert?.actionInfo?.interactionInfo?.url
                    });
                });
        }
    }, [list]);

    useEffect(() => {
        mode === 'combination' &&
            list?.map(expert => {
                ubcCommonViewSend({
                    value: 'ImCombineExpert',
                    ext: {
                        product_info: {
                            msgId,
                            ...ext
                        },
                        value_type: 'doc',
                        value_id: expert?.docID
                    }
                });
            });
    }, [ext, list, mode, msgId]);

    return (
        <View className={styles.expertRecommendationWrapper}>
            <View
                className={cx(
                    styles.expertRecommendation,
                    mode === 'direct' ? styles.directPad : styles.combPad,
                    mode === 'combination' && isNoUndirectService && styles.noUndirectServicePad
                )}
            >
                {showSkeleton ? (
                    <View className={cx(styles.titleSkeleton, styles.skeletonBg)} />
                ) : mode === 'direct' ? (
                    <HImage
                        src={expertData.title || ''}
                        width={207}
                        height={48}
                        className={styles.recommendationIcon}
                    />
                ) : (
                    // 组合卡状态下，定向部分标题为纯文本或icon+文本组合
                    <View
                        className={cx(
                            !hasLeftIcon && styles.recommendationTitle,
                            hasLeftIcon ? 'wz-fw-500, wz-fs-57' : 'wz-fs-54'
                        )}
                    >
                        <ExpertRecTitle
                            content={expertData?.title || ''}
                            // 如果一个默认不展示
                            show={!(showMoreBtn && maxShowLen === 0)}
                            iconUrl={leftIcon}
                        />
                    </View>
                )}
                {showExpertList?.length > 0 &&
                    showExpertList?.map((expert, index) => {
                        const len = showExpertList.length;
                        const showRec = (showMoreBtn && index !== len - 1) || !showMoreBtn;

                        return (
                            <>
                                {/* 外层控制是否展示骨架屏 */}
                                {!showSkeleton ? (
                                    <View
                                        key={expert?.docID || index}
                                        className={cx(
                                            styles.doctorCard,
                                            mode === 'combination' && styles.combDocMargin,
                                            mode === 'direct' && 'wz-mb-30'
                                        )}
                                    >
                                        <CLoginButton
                                            isLogin={isLogin}
                                            closeShowNewUserTag={true}
                                            useH5CodeLogin={true}
                                            onLoginFail={error => {
                                                console.error('error', error);
                                            }}
                                            onLoginSuccess={() => {
                                                // 定向大卡点击
                                                mode === 'direct' &&
                                                    updateCollectedInfoAndExpert?.('expert', {
                                                        ...expert,
                                                        pos: index + 1
                                                    });

                                                // 组合卡点击
                                                mode === 'combination' &&
                                                    updateCombinationCollectedInfoAndExpert?.(
                                                        'expert',
                                                        {
                                                            expertData: {
                                                                ...expert,
                                                                pos: index + 1
                                                            },
                                                            ubcExtInfo: {
                                                                pos: index + 1,
                                                                value_id: expert?.docID
                                                            }
                                                        }
                                                    );
                                            }}
                                        >
                                            <ImDoctorCard
                                                data={expert}
                                                isDisplayRec={showRec}
                                                mode={mode}
                                            />
                                        </CLoginButton>
                                        {/* 最后一个专家卡不显示分割线 */}
                                        {index !== len - 1 && (
                                            <View
                                                className={cx(
                                                    styles.line,
                                                    'wz-mt-45 wz-mb-15',
                                                    mode === 'combination' && styles.lineMarginLeft
                                                )}
                                            />
                                        )}
                                    </View>
                                ) : (
                                    <View className={cx(styles.doctorCard, 'wz-mb-30')}>
                                        <ExpertSkeleton />
                                        {index !== len - 1 && (
                                            <View
                                                className={cx(styles.line, 'wz-mt-45 wz-mb-15')}
                                            />
                                        )}
                                    </View>
                                )}
                            </>
                        );
                    })}
                {/* 非最新的sku卡，不可点击，有遮罩层 */}
                {isExpertDisabled && mode === 'direct' && (
                    <View
                        className={styles.isDisabledContainer}
                        onClick={() => {
                            showToast({
                                title: '当前推荐已失效，请点击新的服务卡',
                                icon: 'none'
                            });
                            adjectiveRecommendExpertMsgId &&
                                scrollToMessage(
                                    adjectiveRecommendExpertMsgId,
                                    'expertRecommendationWrapper'
                                );
                        }}
                    >
                        {/* 去掉定向实验卡原来的失效图标 */}
                        {/* {mode === 'direct' && (
                            <WImage
                                src={imgUrlMap.ineffectiveIcon}
                                className={styles.ineffectiveIcon}
                            />
                        )} */}
                    </View>
                )}
            </View>
            {/* 展开更多按钮 */}
            {showMoreBtn && (
                <>
                    <View
                        className={cx(
                            mode === 'direct' ? styles.directStyle : styles.combinationStyle,
                            'wz-fs-42 wz-flex wz-col-center wz-row-center'
                        )}
                        onClick={e => handleShowMoreExpert(e)}
                    >
                        <View className='wz-flex wz-row-center wz-col-center'>
                            <View>展开更多</View>
                            <ArrowDown className='wz-ml-9' size={42} />
                        </View>
                    </View>
                </>
            )}

            {/* 非最新的sku卡，不可点击，有遮罩层 */}
            {isExpertDisabled && mode === 'combination' && (
                <View
                    className={styles.isCombDisableContainer}
                    onClick={e => {
                        e?.preventDefault?.();
                        showToast({
                            title: '当前推荐已失效，请点击新的服务卡',
                            icon: 'none'
                        });
                        adjectiveRecommendExpertMsgId &&
                            scrollToMessage(
                                adjectiveRecommendExpertMsgId,
                                'expertRecommendationWrapper'
                            );
                    }}
                >
                    {/* 去掉定向实验卡原来的失效图标 */}
                    {/* {mode === 'direct' && (
                            <WImage
                                src={imgUrlMap.ineffectiveIcon}
                                className={styles.ineffectiveIcon}
                            />
                        )} */}
                </View>
            )}
        </View>
    );
};

export default memo(ExpertRecommendation);
