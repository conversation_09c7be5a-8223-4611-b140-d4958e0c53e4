import cx from 'classnames';
import {View, Block} from '@tarojs/components';
import {Transition} from '@baidu/wz-taro-tools-core';
import Taro, {setNavigationBarTitle} from '@tarojs/taro';
import {memo, FC, useEffect, useState, useMemo, ReactNode} from 'react';
import {mergeStyle} from '../../utils';
import {getSystemInfo} from '../../utils/taro/get_system_info/index';
import CTopBar from '../CTopBar';
import CPageLoading from '../CPageLoading';
import CPageSkeleton from '../CPageSkeleton';
import CMiddlePage from '../CMiddlePage';

import store from '../../globalDataStore/index';
import globalData, {PageNetworkStatus} from '../../globalDataStore/globalData';

import './index.less';
import type {CPageContainerProps} from './index.d';

import {useShareMessage} from './hooks/useShareMessage';
import {useScrollToggleHeader} from './hooks/useScrollToggleHeader';

const hdheaderStorage = store.get('hdheader') || false;
const {bigFontSizeClass} = getSystemInfo();

const PageContainer: FC<CPageContainerProps> = props => {
    const {
        renderTopBar,
        background,
        showSkeleton = false,
        skeletonName,
        shareConfig,
        className,
        customStyle,
        children,
        onEntered,
        shareUbcVal,
        hdheader = false,
        showScrollHeader = false,
        conponentShareInfo,
        navigateCallBack,
        onBackDetain,
        backDetain,
        disableTopBar = false
    } = props;
    const [networkStatus, setNetworkStatus] = useState<PageNetworkStatus>(PageNetworkStatus.init);
    const hideTopBar = useMemo(() => {
        return hdheaderStorage || hdheader;
    }, [hdheader]);

    useShareMessage({shareConfig, shareUbcVal, conponentShareInfo});

    useEffect(() => {
        try {
            globalData.set(
                'network',
                new Proxy(
                    {status: PageNetworkStatus.init},
                    {
                        // 设置页面标示代理
                        set(target, key, value) {
                            if (key === 'status') {
                                setNetworkStatus(value);
                            }

                            return Reflect.set(target, key, value);
                        }
                    }
                )
            );
        } catch (e) {
            console.error(e);
        }
    }, []);

    useEffect(() => {
        if (hideTopBar && onEntered) {
            onEntered();
        }
    }, [hideTopBar, onEntered]);

    const containerStyle = {
        background
    };

    const reloadPage = () => {
        if (process.env.TARO_ENV === 'h5') {
            window.location.reload && window.location.reload();

            return;
        }

        const pages = Taro.getCurrentPages();
        const currentPage = pages && pages.length ? pages[pages.length - 1] : null;
        if (process.env.TARO_ENV === 'swan') {
            // Taro无法获得百度小程序的OnLoad, 使用OnInit代替
            currentPage && currentPage.onInit && currentPage.onInit(currentPage.options);
        } else {
            currentPage && currentPage.onLoad && currentPage.onLoad(currentPage.options);
        }
        // pageShow 放入下一事件循环执行
        setTimeout(() => currentPage && currentPage.onShow && currentPage.onShow());
        // 释放网络状态标示
        try {
            const networkConfig = globalData.get('network');
            if (networkConfig) {
                // networkConfig.cxt = this;
                networkConfig.status = 1;
                setTimeout(() => {
                    globalData.set('network', networkConfig);
                });
            }
        } catch (error) {
            console.error(error);
        }
    };

    useEffect(() => {
        setNavigationBarTitle({
            title: props.topBarProps?.title || '百度健康'
        });
    }, [props.topBarProps?.title]);

    // 滚动展示topbar 相关逻辑
    const [scrollHideHeader, showMyChildren, scrollChildren] = useScrollToggleHeader(
        children,
        hdheader && showScrollHeader
    );
    // 滚动展示topbar 相关逻辑

    return (
        <View
            className={cx('c-page-container', className, bigFontSizeClass)}
            style={mergeStyle(containerStyle, customStyle)}
        >
            <Block>
                {!disableTopBar && ((!showSkeleton && !hdheader) || scrollHideHeader) ? (
                    <>
                        <Transition in appear mountOnEnter name='fade' onEntered={onEntered}>
                            <CTopBar
                                navigateCallBack={navigateCallBack}
                                backDetain={backDetain}
                                onBackDetain={onBackDetain}
                                {...props.topBarProps}
                            >
                                {renderTopBar && renderTopBar()}
                            </CTopBar>
                        </Transition>
                    </>
                ) : null}
                {/* 默认的Loading状态, 非骨架屏的情况下 */}
                {networkStatus === PageNetworkStatus.loading && !showSkeleton && !skeletonName ? (
                    <CPageLoading />
                ) : null}
                {/* 首屏接口请求失败 */}
                {networkStatus === PageNetworkStatus.error ? (
                    <View className='c-page-container__error'>
                        <CMiddlePage onButtonClicked={reloadPage} />
                    </View>
                ) : null}
            </Block>
            {/* 内容数据 */}
            {showSkeleton && skeletonName ? (
                <CPageSkeleton name={skeletonName} />
            ) : (
                <View className='c-page-container__showContent'>
                    {showMyChildren ? (scrollChildren as ReactNode) : children}
                </View>
            )}
        </View>
    );
};

export default memo(PageContainer);
