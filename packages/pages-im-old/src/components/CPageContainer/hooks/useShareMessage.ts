import {useEffect} from 'react';
import {getCurrentPages, hideShareMenu, useShareAppMessage, useShareTimeline} from '@tarojs/taro';
import {
    type Query,
    getQueryObject,
    mapChangeToQuery,
    urlAddQuery,
    isValidChineseCharacter
} from '@baidu/vita-utils-shared';

import {H5_HOST} from '../../../models/apis/host';

import {useGetPagePath} from '../../../hooks/common';

import {initShare} from '../../../utils/share';
import {validateGtOrEqualIOS17} from '../../../utils';

import {weappShareBlacklist} from '../../../constants/path';
// import { ubcCommonClkSend } from '@common/utils/generalFunction/ubc';
// import { reportWeAnalysisEvent } from '@common/utils/generalFunction/reportWeAnalysisEvent';

import type {CPageContainerProps} from '../index.d';

// TODO：环境变量待补齐
// const { BAIDU_APP_ENV = 'wenzhen' } = process.env;
const BAIDU_APP_ENV = 'wenzhen';

export const useShareMessage = (
    props: Pick<CPageContainerProps, 'shareConfig' | 'shareUbcVal' | 'conponentShareInfo'>
) => {
    const {shareConfig, conponentShareInfo, shareUbcVal} = props;
    const pagePath = useGetPagePath();

    useEffect(() => {
        if (shareConfig && shareConfig.title && process.env.TARO_ENV === 'h5') {
            initShare(shareConfig || {});
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [JSON.stringify(shareConfig)]);

    useEffect(() => {
        let shareUrl = shareConfig?.shareUrl || pagePath || '';
        // 只取path部分，不取参数
        const searchIndex = shareUrl.indexOf('?');
        shareUrl = searchIndex !== -1 ? shareUrl.substring(0, searchIndex) : shareUrl;

        // 微信黑名单拦截
        if (process.env.TARO_ENV === 'weapp' && weappShareBlacklist.includes(shareUrl)) {
            hideShareMenu();
        }
    }, [shareConfig, pagePath]);

    function getShareInfo() {
        const {title, content, imageUrl} = shareConfig || {};
        let shareUrl = shareConfig?.shareUrl || pagePath || '';
        const pages = getCurrentPages();
        const curPage = pages[pages.length - 1];
        const urlQuery = getQueryObject(shareUrl);
        let mapQuery;
        // 中间页保持重构前逻辑，不做特殊处理
        if (shareUrl.includes('pages/middlePage/index')) {
            mapQuery = {...curPage.options};
        } else {
            // 只取path部分，不取参数
            const searchIndex = shareUrl.indexOf('?');
            shareUrl = searchIndex !== -1 ? shareUrl.substring(0, searchIndex) : shareUrl;
            mapQuery = {...curPage.options, ...urlQuery, from: 'tg_wxxcx_fenxiang'};
        }
        let query = mapChangeToQuery(mapQuery);
        if (query.length > 0) {
            query = query.slice(1, query.length);
        }

        if (process.env.TARO_ENV === 'swan' && validateGtOrEqualIOS17()) {
            const newQuery = getQueryObject(query);
            // 兼容 ios17 分享白屏问题，单独判断 title 和 word参数，如果存在中文，则进行 encodeURIComponent
            if (newQuery.title && isValidChineseCharacter(newQuery.title)) {
                newQuery.title = encodeURIComponent(newQuery.title);
            }

            if (newQuery.word && isValidChineseCharacter(newQuery.word)) {
                newQuery.word = encodeURIComponent(newQuery.word);
            }

            if (newQuery.department && isValidChineseCharacter(newQuery.department)) {
                newQuery.department = encodeURIComponent(newQuery.department);
            }
            query = urlAddQuery('', newQuery as Query);
            if (query.startsWith('?')) {
                query = query.slice(1, query.length);
            }
        }
        if (
            !(process.env.TARO_ENV === 'weapp') &&
            shareUrl &&
            !shareUrl.startsWith(`/${BAIDU_APP_ENV}`)
        ) {
            // 不携带 /wenzhen 或 /guahao 前缀时 再拼接前缀地址
            if (!shareUrl.includes('/wenzhen') && !shareUrl.includes('/guahao')) {
                shareUrl = `/${BAIDU_APP_ENV}${shareUrl}`;
            }
        }
        const reportInfo = {
            title: mapQuery?.qrCodeTitle || title || '百度健康 您身边的健康管家',
            path: shareUrl,
            urlParams: mapQuery
        };

        const shareInfo = {
            title: mapQuery?.qrCodeTitle || title || '百度健康 您身边的健康管家',
            content: content || '',
            imageUrl: mapQuery?.qrCodeImage || imageUrl || '',
            shareUrl:
                process.env.TARO_ENV === 'weapp'
                    ? `${shareUrl || ''}?${query}`
                    : `${H5_HOST}${shareUrl as string}?${query}`,
            path:
                process.env.TARO_ENV === 'weapp'
                    ? `${shareUrl || ''}?${query}`
                    : `${H5_HOST}${shareUrl as string}?${query}`
        };

        return {
            reportInfo,
            shareInfo
        };
    }

    useShareAppMessage(info => {
        // ubcCommonClkSend({
        //     value: shareUbcVal || 'wz_share_page'
        // });

        if (conponentShareInfo) {
            return conponentShareInfo(info);
        }

        const {reportInfo, shareInfo} = getShareInfo();
        const {title, path, urlParams} = reportInfo;

        // if (process.env.TARO_ENV === 'weapp') {
        //     reportWeAnalysisEvent({
        //         event: 'clk_share_button',
        //         properties: {
        //             title,
        //             share_path: path,
        //             from_of_params: urlParams?.from || '',
        //             ref_of_params: urlParams?.sf_ref || '',
        //             params_of_page: JSON.stringify(urlParams)
        //         }
        //     });
        // }
        // todo:临时打印处理ts报错
        console.log(title, path, urlParams, shareUbcVal);
        return shareInfo;
    });

    useShareTimeline(() => {
        const {shareInfo} = getShareInfo();

        return conponentShareInfo ? conponentShareInfo() : shareInfo;
    });
};
