import React from 'react';

import type { SessionId } from '../../../store/triageStreamAtom/index.type.ts';

import type { ICouponList } from '../../../typings/service';
import { InteractionType, InteractionInfo, MsgId } from '../../../typings';

export interface StreamFocusServiceCardDataV2 {
    expertId: string;
    isGuaranteed: boolean;
    topDes: {
        title: string;
        desc: {
            type: 'text' | 'icon';
            value: string;
            interaction: string;
            interactionInfo: {
                content: FocusServiceSkuInfo;
            };
        }[];
    };
    skuList: FocusServiceSku[];
}

export interface CouponTips {
    type: 'text' | 'icon' | 'fontWeightText' | 'redText';
    value: string;
}

export interface CouponInfo {
    id: number;
    cid: number;
    startTime: number;
    endTime: number;
    name: string;
    subTitle: string;
    category: number;
    maxPrice: number;
    fullPrice: number;
    salePrice: number;
}

export interface StreamFocusServiceCardDataV2Props {
    skuData: StreamFocusServiceCardDataV2;
    coupon?: {
        couponInfo?: CouponInfo;
        couponTips?: CouponTips[];
        userCoupons?: {
            list: ICouponList[];
            disableList: ICouponList[];
        };
    };
    selectedSkuItemData: FocusServiceSku;
    updateSkuDetailData?: (data: FocusServiceSku) => void;
    isLogin: boolean;
    updateCollectedInfoAndSku: (type: string, skuData?: FocusServiceSku) => void;
    isSkuDisabled: boolean;
    msgId: MsgId;
    adjectiveDirectedSkuMsgId?: MsgId | undefined;
    sessionId: SessionId;
    expertId?: string;
    PhoneConsultModalNode?: React.ReactNode;
    isSkuDetailPopupShow: boolean;
    setIsSkuDetailPopupShow: (value: boolean) => void;
}
export interface FocusServiceSkuInfo {
    sloganUrl?: string;
    serviceIcon?: string;
    list?: {
        title?: string;
        icon?: string;
        desc?: string;
    }[];
}

export interface CalculatedSkuPriceInfoType {
    originalPrice: number; // 原售价
    actualSellingPrice: number; // 实际售价
    promotionReductionPrice: number; // 平台优惠
    couponDiscountPrice: number; // 优惠券优惠
    totalDiscountPrice: number; // 优惠总金额
    totalReductionPrice?: number; // 优惠总金额
    servicePrice?: number; // 服务费金额
    totalPrice?: number; // 原售价 + 服务费
    platformFee?: number; // 平台服务费
}

export interface descItem {
    type: 'text';
    value: string;
}

export interface FocusServiceSku {
    icon: string;
    skuId: string;
    title: string;
    label: string;
    subtitle: string;
    formType: number;
    isSelected: 0 | 1;
    stockQuantity?: number;
    priceInfo: {
        salePrice: number;
        promotionPrice: number;
        couponReductionPrice?: number;
        promotionReductionPrice?: number;
        totalReductionPrice?: number; // 优惠总金额
        totalPrice?: number; // 原价格+服务费
        platformFee?: number; // 平台服务费
    };
    btnInfo: {
        value: string;
        interaction: InteractionType;
        interactionInfo: InteractionInfo;
    };
    skuDetail: {
        rights: descItem[];
        rules: descItem[];
        platformFeeDesc?: descItem[];
    };
}


export interface CouponDataType {
    disableList: ICouponList[];
    enableList: ICouponList[];
}

/**
 * sku button
 */
export interface IBtnProps {
    text?: string;
    interaction: InteractionType;
    interactionInfo: InteractionInfo;
}

/**
 * sku
 */
export interface IListProps {
    title?: string;
    subTitle?: string;
    desc?: string;
    orgPrice?: string;
    currentPrice?: number;
    skuId?: string;
    btn?: IBtnProps;
    referencePrice?: number;
    salePrice?: number;
    promotionPrice?: number;
    formType?: number;
    rightDesc?: string;
    totalPrice?: number;
}
