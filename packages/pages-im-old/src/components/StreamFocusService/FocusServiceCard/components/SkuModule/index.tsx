import { View } from '@tarojs/components';
import  { memo, type FC, useMemo } from 'react';

import SkuItem from './SkuItem';
import type { StreamFocusServiceCardDataV2, FocusServiceSku } from '../../index.d';

interface SkuModuleProps {
    data: StreamFocusServiceCardDataV2['skuList'];
    updateSkuDetailData: (data: FocusServiceSku) => void;
    isLogin: boolean;
    updateCollectedInfoAndSku: (type: string, skuData?: FocusServiceSku) => void;
}

const SkuModule: FC<SkuModuleProps> = props => {
    const { data, updateSkuDetailData, updateCollectedInfoAndSku, isLogin } = props;

    const genSkuModule = useMemo(() => {
        return (
            data
            && Array.isArray(data)
            && data.map((i, idx) => {
                return (
                    <SkuItem
                        data={i}
                        key={i?.skuId}
                        classNameProp={idx === data.length - 1 ? '' : 'wz-mb-24'}
                        updateSkuDetailData={updateSkuDetailData}
                        updateCollectedInfoAndSku={updateCollectedInfoAndSku}
                        isLogin={isLogin}
                    />
                );
            })
        );
    }, [data, isLogin, updateCollectedInfoAndSku, updateSkuDetailData]);

    return <View className='wz-mb-18'>{genSkuModule}</View>;
};

export default memo(SkuModule);
