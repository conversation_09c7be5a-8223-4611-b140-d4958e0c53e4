import cx from 'classnames';
import {View} from '@tarojs/components';
import {memo, type FC, useMemo, useCallback} from 'react';
import {Tag} from '@baidu/wz-taro-tools-core';
import {CLoginButton} from '@baidu/vita-ui-cards-common';
import {formatPrice} from '../../../../../utils/generalFunction/price';
import {ubcCommonClkSend} from '../../../../../utils/generalFunction/ubc';

import OrderOperation from '../OrderOperation';
import styles from './skuItem.module.less';
import type {FocusServiceSku} from '../../index.d';

interface SkuItemProps {
    data: FocusServiceSku;
    updateSkuDetailData: (data: FocusServiceSku) => void;
    classNameProp?: string;
    isLogin: boolean;
    updateCollectedInfoAndSku: (type: string, skuData?: FocusServiceSku) => void;
}

const SkuItem: FC<SkuItemProps> = props => {
    const {data, updateSkuDetailData, updateCollectedInfoAndSku, isLogin} = props;

    const openSkuDetailPopup = useCallback(
        (d: FocusServiceSku) => {
            updateSkuDetailData(d);

            ubcCommonClkSend({
                value: `ImFocusService_v2_clkSubtitle_${d.skuId}`
            });
        },
        [updateSkuDetailData]
    );

    /**
     *
     * @description 生成划线价格
     */
    const genLineThroughPrice = useMemo(() => {
        if (
            data?.priceInfo.totalPrice &&
            data?.priceInfo.promotionPrice &&
            data?.priceInfo.promotionPrice < data?.priceInfo.totalPrice
        ) {
            return (
                <View className={cx(styles.skuPrice, 'wz-fs-42 wz-mr-18')}>
                    ¥{formatPrice(data?.priceInfo.totalPrice)}
                </View>
            );
        }

        return <></>;
    }, [data?.priceInfo.promotionPrice, data?.priceInfo.totalPrice]);

    /**
     *
     * @description 生成库存显示
     */
    const genStockQuantity = useMemo(() => {
        if (data.stockQuantity) {
            return (
                <View className={cx(styles.stockQuantity, 'wz-ml-18 wz-fs-39')}>
                    仅剩{data.stockQuantity}个
                </View>
            );
        }

        return <></>;
    }, [data.stockQuantity]);

    return (
        <View className={cx(styles.skuItemContainer, 'wz-pt-45 wz-pb-51')}>
            <CLoginButton
                isLogin={isLogin}
                closeShowNewUserTag={true}
                useH5CodeLogin={true}
                onLoginFail={error => {
                    console.error('error', error);
                }}
                onLoginSuccess={() => {
                    updateCollectedInfoAndSku('sku', data);
                }}
            >
                <View
                    className={cx(styles.skuIcon, 'wz-mr-36')}
                    style={{
                        backgroundImage: `url(${data.icon})`
                    }}
                />
            </CLoginButton>
            <View className={cx(styles.skuCon)}>
                <CLoginButton
                    isLogin={isLogin}
                    closeShowNewUserTag={true}
                    useH5CodeLogin={true}
                    onLoginFail={error => {
                        console.log('error', error);
                    }}
                    onLoginSuccess={() => {
                        updateCollectedInfoAndSku('sku', data);
                    }}
                >
                    <View className={cx(styles.row1)}>
                        <View className={cx(styles.skuName, 'wz-fs-54 wz-fw-500')}>
                            {data.title}
                        </View>
                        {data.label && (
                            // eslint-disable-next-line max-len
                            <Tag
                                style={{
                                    color: '#FD5031',
                                    borderRadius: '4px',
                                    marginLeft: '6px',
                                    fontWeight: 500
                                }}
                                variant='outlined'
                            >
                                {data.label}
                            </Tag>
                        )}
                        {genStockQuantity}
                    </View>
                </CLoginButton>
                {data.subtitle && (
                    <>
                        <View
                            className={cx(styles.row2, 'wz-fs-42')}
                            onClick={() => openSkuDetailPopup(data)}
                        >
                            <View className={cx(styles.row2Title)}>{data.subtitle}</View>
                            <View className={styles.conArrow} />
                        </View>
                    </>
                )}
            </View>
            <View className={cx(styles.rightsCon, 'wz-flex')}>
                <View className={cx(styles.priceCon, 'wz-flex')}>
                    {genLineThroughPrice}
                    <View className={cx(styles.skuSalePrice, 'wz-pr-45')}>
                        ¥{formatPrice(data?.priceInfo.promotionPrice)}
                    </View>
                </View>
                <OrderOperation
                    skuData={data}
                    updateCollectedInfoAndSku={updateCollectedInfoAndSku}
                    isLogin={isLogin}
                />
            </View>
        </View>
    );
};

export default memo(SkuItem);
