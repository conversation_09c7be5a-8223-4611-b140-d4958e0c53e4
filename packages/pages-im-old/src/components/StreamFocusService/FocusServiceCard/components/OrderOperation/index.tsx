import cx from 'classnames';
import {View} from '@tarojs/components';
import {CLoginButton} from '@baidu/vita-ui-cards-common';
import {memo, type FC} from 'react';

import styles from './index.module.less';
import type {FocusServiceSku} from '../../index.d';

interface OrderOperationProps {
    skuData: FocusServiceSku;
    isLogin: boolean;
    // type: patient-就诊人模块，sku-sku列表
    updateCollectedInfoAndSku: (type: 'patient' | 'sku', skuData?: FocusServiceSku) => void;
}

/**
 *
 * @description 定向服务卡底部操作区
 * @param props
 * @returns
 */
const OrderOperation: FC<OrderOperationProps> = props => {
    const {skuData, isLogin, updateCollectedInfoAndSku} = props;

    return (
        <View className={cx(styles.orderOperationContainer, 'wz-flex wz-row-right')}>
            <CLoginButton
                isLogin={isLogin}
                closeShowNewUserTag={true}
                useH5CodeLogin={true}
                onLoginFail={error => {
                    console.error('error', error);
                }}
                onLoginSuccess={() => {
                    updateCollectedInfoAndSku('sku', skuData);
                }}
            >
                <View className={cx(styles.btn, 'wz-fs-42 wz-flex wz-col-center wz-row-center')}>
                    {skuData?.btnInfo.value || '去咨询'}
                </View>
            </CLoginButton>
        </View>
    );
};

export default memo(OrderOperation);
