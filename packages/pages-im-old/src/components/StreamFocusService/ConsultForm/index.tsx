/*
 *  @Author: lijing106
 * @Description: 就诊人操作弹层
 */
import {pxTransform} from '@tarojs/taro';
import {View, Text, Form, Input} from '@tarojs/components';
import {WiseRightArrow} from '@baidu/wz-taro-tools-icons';

import cx from 'classnames';
import {memo, FC, useMemo, useCallback, useState, useEffect, useRef} from 'react';
import {Popup, Button, FormCard, SelectAgePopup, SafeArea} from '@baidu/wz-taro-tools-core';
import type {ItemInfo} from '@baidu/wz-taro-tools-core/form-input-item';
import {getUserBizActionReq} from '../../../models/services/triageStream';
import {useMsgDataSetController} from '../../../hooks/triageStream/dataController';
import {showToast} from '../../../utils/customShowToast';

import type {CONSULT_TYPE} from '../index.d';
import {PATIENT_TEMP, phoneNumberReg, TOAST_TIME, ZHUSU_MAX_TEXT, patientInitInfo} from './const';
import TelInFo from './components/TelInFo';
import ChoosePatientList from './components/ChoosePatientList';
import DiseaseDesc from './components/DiseaseDesc';
import RequireDesc from './components/RequireDesc';

import styles from './index.module.less';
import type {IConsultFormProps, PatientInfo} from './index.d';

const ConsultForm: FC<IConsultFormProps> = (props: IConsultFormProps) => {
    const {
        showConsultForm,
        collectedInfo,
        requireDesc,
        handleAddPatient,
        handleCloseConsultForm,
        formType,
        showConsultList,
        state,
        dispatch,
        openPatientPopType,
        selectPatientData,
        handleSelectPatient,
        selectedSkuItemData,
        loginSuccessfulCallback,
        msgId,
        sessionId,
        expertId,
        skuList,
        setSelectPatientData
    } = props;

    const [openAgeSelect, setOpenAgeSelect] = useState(false);
    const [focus, setFocus] = useState(false);
    const [isAddPatient, setIsAddPatient] = useState(false); // 是否是点击添加就诊人
    const {updateMsgData} = useMsgDataSetController({msgId});
    const [editPopType, setEditPopType] = useState<CONSULT_TYPE>('consult');
    const [isDisabled, setIsDisabled] = useState(false);
    const tempTelPhone = collectedInfo.telPhone || '';

    const patirentRef = useRef(false);

    const memoPatientListStyle = useMemo(() => {
        return {maxHeight: `calc(80vh - ${pxTransform(133)})`};
    }, []);

    /**
     * ImConsultForm 选择年龄 上报
     */
    const chooseAge = useCallback(editStatus => {
        editStatus === 1 && setOpenAgeSelect(true);
    }, []);

    // item 选项切换回调
    const onInputItemChange = useCallback(
        (key, itemData: PatientInfo) => {
            if (key === 'gender') {
                dispatch({payload: {gender: itemData?.gender || ''}});
            } else {
                dispatch({payload: itemData});
            }
        },
        [dispatch]
    );

    // 新增患者
    const addPatient = useCallback(() => {
        setIsAddPatient(true);
        handleAddPatient && handleAddPatient();
        dispatch({
            payload: {
                ...patientInitInfo,
                zhusu: state?.zhusu || ''
            }
        });
    }, [dispatch, handleAddPatient, state?.zhusu]);

    /**
     * 校验按钮是否可提交状态
     *
     * @param state state
     * @return 是否可提交状态
     */
    const validCanSubmitStatus = useCallback(() => {
        const {age, gender, telPhone} = state;

        return (
            (!state?.contactId && !age) ||
            (!state?.contactId && !gender) ||
            (state?.formType === 2 && openPatientPopType === 'sku' && !telPhone)
        );
    }, [openPatientPopType, state]);

    // 根据当前选中的skuId查找最新的sku数据
    const findSelectedSku = useCallback(
        (skuId: string) => {
            return skuList?.find(skuItem => skuItem.skuId === skuId);
        },
        [skuList]
    );

    const showToastMsg = useCallback(() => {
        showToast({
            title: isAddPatient ? '新增成功' : '保存成功',
            icon: 'none'
        });
    }, [isAddPatient]);

    useEffect(() => {
        if (patirentRef.current) {
            const selectSku = findSelectedSku(selectedSkuItemData?.skuId || '');
            if (selectSku) {
                selectSku && loginSuccessfulCallback(selectSku);
                showToastMsg();
            } else {
                showToast({
                    title: '当前服务已无库存, 您可继续选择该医生的其他服务',
                    icon: 'none'
                });
            }
            handleCloseConsultForm(editPopType);
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [patirentRef.current]);

    const handleUpdateMsgData = useCallback(
        async (params, type) => {
            // eslint-disable-next-line no-console
            const {age, gender, zhusu, telPhone, contactId, skuId} = params;
            const patientInfo =
                contactId && type === 'consultList'
                    ? {contactId}
                    : {
                        age,
                        gender
                    };

            setEditPopType(type);
            const paramsOpt = !isAddPatient ? {zhusu} : {};
            const paramsData = {
                bizActionType: 'editZhusu' as const,
                chatData: {
                    sessionId,
                    expertId: Number(expertId || '')
                },
                bizActionData: {
                    editZhusuInfo: {
                        patientInfo,
                        msgId,
                        ...paramsOpt,
                        telPhone,
                        skuId
                    }
                }
            };
            setIsDisabled(true);
            const [err, data] = await getUserBizActionReq<'editZhusu'>(paramsData);
            try {
                if (!err) {
                    data?.data?.message && updateMsgData(data?.data?.message[0] || {});
                    // sku咨询直接走下单
                    if (openPatientPopType === 'sku') {
                        setTimeout(() => {
                            patirentRef.current = true;
                        }, 0);
                    } else {
                        // 就诊人模块关闭弹层，自动更新卡片信息
                        handleCloseConsultForm(type);
                        // eslint-disable-next-line max-len
                        data?.data?.message &&
                            setSelectPatientData(
                                data?.data?.message[0]?.data?.content?.data?.content?.collectedInfo
                                    ?.curPatient || {}
                            );
                        showToastMsg();
                    }
                    setIsDisabled(false);
                    setIsAddPatient(false);
                }
            } catch (error) {
                setIsDisabled(false);
                console.error(error);
            }
        },
        [
            expertId,
            handleCloseConsultForm,
            isAddPatient,
            msgId,
            openPatientPopType,
            sessionId,
            setSelectPatientData,
            showToastMsg,
            updateMsgData
        ]
    );

    /**
     * 提交数据
     */
    const onSubmit = useCallback(
        async type => {
            const {zhusu: content = '', gender = '', telPhone: phone} = state;
            // eslint-disable-next-line max-len
            const isPhoneNo =
                !phoneNumberReg.test(phone || '') &&
                openPatientPopType === 'sku' &&
                phone !== tempTelPhone;
            if (isPhoneNo) {
                showToast({
                    title: '手机号格式填写错误',
                    icon: 'none',
                    duration: TOAST_TIME
                });

                return;
            }
            const disabledSubmit = validCanSubmitStatus();
            if (disabledSubmit) {
                showToast({
                    title: '请输入完整信息',
                    icon: 'none',
                    duration: TOAST_TIME
                });

                return;
            }

            if (!isAddPatient && (state?.zhusu?.length || 0) > ZHUSU_MAX_TEXT) {
                showToast({
                    title: `病情描述不能超过${ZHUSU_MAX_TEXT}个字符`,
                    icon: 'none',
                    duration: TOAST_TIME
                });

                return;
            }

            if (!state?.zhusu?.trim()) {
                showToast({
                    title: '病情描述不能为空',
                    icon: 'none',
                    duration: TOAST_TIME
                });

                return;
            }
            // 在就诊人模块选择就诊人信息时，会根据当前选择的就诊人信息，请求行为接口，刷新页面数据，更新就诊人模块信息
            if (type === 'consultList') {
                // eslint-disable-next-line max-len
                handleUpdateMsgData(
                    {contactId: selectPatientData?.contactId, zhusu: content || ''},
                    type
                );
            } else {
                // 无就诊人时，就诊人模块新增就诊人
                handleUpdateMsgData(
                    {
                        gender,
                        age: state?.age,
                        zhusu: content,
                        skuId: selectedSkuItemData?.skuId || '',
                        ...(formType === 2 && openPatientPopType === 'sku'
                            ? {telPhone: state?.telPhone}
                            : {})
                    },
                    type
                );
            }
        },
        [
            formType,
            handleUpdateMsgData,
            isAddPatient,
            openPatientPopType,
            selectPatientData?.contactId,
            selectedSkuItemData,
            state,
            tempTelPhone,
            validCanSubmitStatus
        ]
    );

    // 选择性别
    const getGenderChoose = useCallback(
        formItem => {
            return (
                <View className='wz-flex wz-row-right'>
                    {['男', '女'].map((genderItem, genderIndex) => {
                        return (
                            <View
                                key={genderIndex}
                                className={cx(
                                    styles.formGenderBtn,
                                    'wz-flex',
                                    'wz-col-center',
                                    'wz-row-center',
                                    {
                                        [styles.formGenderBtnActive]:
                                            formItem?.value === genderItem ||
                                            formItem?.defaultValue === genderItem
                                    },
                                    'wz-fs-45 wz-ml-24 wz-plr-48'
                                )}
                                onClick={() =>
                                    onInputItemChange &&
                                    onInputItemChange(formItem.key, {gender: genderItem})
                                }
                            >
                                {genderItem}
                            </View>
                        );
                    })}
                </View>
            );
        },
        [onInputItemChange]
    );

    // 选择年龄
    const getAgeChoose = useCallback(
        (formItem, editStatus) => {
            return (
                <View
                    className={cx('wz-flex wz-row-right wz-col-center colorGray wz-fs-45', [
                        editStatus === 1 ? '' : ''
                    ])}
                    onClick={() => chooseAge(editStatus)}
                >
                    {state.age ? (
                        <Text> {state?.ageText}</Text>
                    ) : (
                        <Text className={cx(styles.placeholderColor)}> {formItem.placeholder}</Text>
                    )}
                    {editStatus === 1 && <WiseRightArrow color='#B5B5B5' />}
                </View>
            );
        },
        [chooseAge, state.age, state?.ageText]
    );

    const memoPatientEdit = useMemo(() => {
        return <View className='form-title'>{isAddPatient ? '新增患者' : '病情信息'}</View>;
    }, [isAddPatient]);

    // 关闭新增或者编辑弹层
    const handleCloseConsultPop = useCallback(() => {
        setIsAddPatient(false);
        handleCloseConsultForm('consult');
    }, [handleCloseConsultForm]);

    return (
        <View className={styles.consultForm}>
            <Popup
                open={showConsultForm}
                rounded
                placement='bottom'
                catchMove={false}
                title={memoPatientEdit}
                style={{
                    height: `calc(80vh - ${pxTransform(133)})`,
                    background: 'linear-gradient(180deg, #FFFFFF 0%, #F5F5F5 18%, #F5F5F5 100%)',
                    zIndex: 1012,
                    borderRadius: `${pxTransform(63)} ${pxTransform(63)} 0 0`
                }}
                titleStyle={{
                    backgroundColor: 'rgb(245, 245, 245, 0)',
                    border: 'none'
                }}
                onClose={handleCloseConsultPop}
                className='consult-popup'
            >
                <View className={styles.consultPopupContent}>
                    <View className={cx(styles.patientList, 'wz-mlr-30 wz-pb-54 wz-mb-24')}>
                        {!collectedInfo?.curPatient?.contactId && !isAddPatient && (
                            <View
                                className={cx(
                                    styles.patientInfoTitle,
                                    'wz-flex wz-col-center wz-fs-54 wz-fw-500 wz-mlr-39'
                                )}
                            >
                                患者信息
                            </View>
                        )}
                        {formType === 2 && openPatientPopType === 'sku' && (
                            <View className={cx(styles.telInfoWrapper, 'wz-pt-39 wz-plr-39')}>
                                <TelInFo
                                    className={cx(styles.telInfo, 'wz-br-27')}
                                    telPhone={collectedInfo.servicePhone}
                                />
                            </View>
                        )}
                        <Form>
                            {formType === 2 && openPatientPopType === 'sku' && (
                                <FormCard className={styles.telFormWrapper}>
                                    <View
                                        className={cx(
                                            'wz-flex wz-row-between wz-col-center',
                                            'c-color-prime wz-fs-48 wz-mlr-39',
                                            'wz-ptb-54'
                                        )}
                                    >
                                        <View>
                                            <Text className='wz-fw-500'>电话</Text>
                                            <Text className={styles.form__item__label__icon}>
                                                *
                                            </Text>
                                        </View>
                                        <View className={cx(styles.inputTelWrapper, 'wz-flex')}>
                                            <Input
                                                focus={focus}
                                                className={cx(
                                                    styles.inputTel,
                                                    'wz-text-right wz-fs-48'
                                                )}
                                                placeholder='请输入患者手机号'
                                                placeholder-className='gray-font wz-fs-48'
                                                type='text'
                                                maxlength={11}
                                                value={state.telPhone}
                                                onBlur={() => {
                                                    setFocus(false);
                                                }}
                                                onInput={e =>
                                                    dispatch({payload: {telPhone: e.detail.value}})
                                                }
                                            />
                                        </View>
                                    </View>
                                </FormCard>
                            )}
                            <FormCard className={styles.formCard}>
                                {(PATIENT_TEMP as ItemInfo[]).map((item: ItemInfo) => {
                                    const editStatus = 1;

                                    const formItem = Object.assign(
                                        {},
                                        item,
                                        item?.key
                                            ? {
                                                value: state[
                                                      item?.key as
                                                          | 'age'
                                                          | 'ageText'
                                                          | 'gender'
                                                          | 'name'
                                                ],
                                                defaultValue:
                                                      state[
                                                          item?.key as
                                                              | 'age'
                                                              | 'ageText'
                                                              | 'gender'
                                                              | 'name'
                                                      ],
                                                editStatus
                                            }
                                            : {}
                                    );

                                    return (
                                        <View
                                            key={item.key}
                                            className={cx(
                                                'wz-flex wz-row-between wz-col-center',
                                                'c-color-prime wz-fs-48 wz-mlr-39',
                                                'wz-ptb-54'
                                            )}
                                        >
                                            <View>
                                                <Text className='wz-fw-500'>{item.label}</Text>
                                                <Text
                                                    className={cx(styles.form__item__label__icon)}
                                                >
                                                    *
                                                </Text>
                                            </View>
                                            {item?.key === 'age'
                                                ? getAgeChoose(formItem, editStatus)
                                                : getGenderChoose(formItem)}
                                        </View>
                                    );
                                })}
                            </FormCard>
                        </Form>
                        {/* 病情描述 */}
                        {!collectedInfo?.curPatient?.contactId && !isAddPatient && (
                            <DiseaseDesc {...{state, dispatch}} />
                        )}
                    </View>
                    {/* 接诊要求 无就诊人时会展示 */}
                    {requireDesc && !isAddPatient && <RequireDesc requireDesc={requireDesc} />}
                </View>
                <Popup.Button>
                    <View className={cx(styles['patient-list-btn'], ' wz-ptb-24 wz-plr-54')}>
                        <Button
                            color='default'
                            size='large'
                            block
                            className={cx(styles['patient-list-btn-submit'], 'wz-fw-500')}
                            onClick={() => onSubmit('consult')}
                            disabled={isDisabled}
                        >
                            <Text className='wz-fs-54'>
                                {openPatientPopType === 'sku' ? '立即咨询' : '完成'}
                            </Text>
                        </Button>
                        <SafeArea
                            id='safearea'
                            style={{backgroundColor: '#fff'}}
                            position='bottom'
                        />
                    </View>
                </Popup.Button>
                <Popup.Close />
            </Popup>
            <Popup
                open={showConsultList}
                placement='bottom'
                title={<View catchMove>病情信息</View>}
                rounded
                catchMove={false}
                style={{
                    ...memoPatientListStyle,
                    background: 'linear-gradient(180deg, #FFFFFF 0%, #F5F5F5 18%, #F5F5F5 100%)',
                    zIndex: 1011,
                    borderRadius: `${pxTransform(63)} ${pxTransform(63)} 0 0`,
                    height: `calc(80vh - ${pxTransform(133)})`
                }}
                titleStyle={{background: 'rgb(245, 245, 245, 0)', border: 'none'}}
                onClose={() => handleCloseConsultForm('consultList')}
                className={styles['consult-popup']}
            >
                <Popup.Close />
                <View className={styles.consultPopupContent}>
                    <ChoosePatientList
                        {...{
                            collectedInfo,
                            onInputItemChange,
                            handleAddPatient: addPatient,
                            formType,
                            selectPatientData,
                            handleSelectPatient,
                            state,
                            dispatch,
                            openPatientPopType
                        }}
                    />
                    {/* 接诊要求 */}
                    {requireDesc && <RequireDesc requireDesc={requireDesc} />}
                </View>
                <Popup.Button>
                    <View className={cx(styles['patient-list-btn'], ' wz-ptb-24 wz-plr-54')}>
                        <Button
                            color='default'
                            size='large'
                            block
                            className={cx(styles['patient-list-btn-submit'], 'wz-fw-500')}
                            onClick={() => onSubmit('consultList')}
                            disabled={isDisabled}
                        >
                            <Text className='wz-fs-54'>完成</Text>
                        </Button>
                        <SafeArea
                            id='safearea'
                            style={{backgroundColor: '#fff'}}
                            position='bottom'
                        />
                    </View>
                </Popup.Button>
            </Popup>
            <SelectAgePopup
                open={openAgeSelect}
                interval='0,120'
                key={state.age}
                defaultValue={(state.age as string) || '20'}
                onCancel={() => setOpenAgeSelect(false)}
                onConfirm={v => {
                    setOpenAgeSelect(false);
                    dispatch({
                        payload: {
                            age: v?.value
                        }
                    });
                }}
            />
        </View>
    );
};
export default memo(ConsultForm);
