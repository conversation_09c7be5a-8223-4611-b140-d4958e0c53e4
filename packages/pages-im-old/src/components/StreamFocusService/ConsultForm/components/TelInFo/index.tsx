/*
 *  @Author: liu<PERSON>i
 * @Description: 电话提示模块
 */
import React, { memo } from 'react';
import { View, Text } from '@tarojs/components';
import { WiseTipSolid } from '@baidu/wz-taro-tools-icons';
import cx from 'classnames';
import styles from './index.module.less';

interface ITelInFo {
    telPhone?: string;
    className?: string;
}

const TelInFo = ({ telPhone, className }: ITelInFo) => {
    return (
        <View
            // eslint-disable-next-line max-len
            className={cx(
                styles['tel-tips'],
                className,
                'wz-flex wz-ptb-18  wz-plr-39 wz-row-left wz-col-center wz-fs-42'
            )}
            catchMove
        >
            <WiseTipSolid size={45} color='#00C8C8' className='wz-mr-18' />
            <View className={cx(styles.telInfoContent)}>
                仅用于接听医生电话，请注意
                <Text className={cx(styles.tel)}>{telPhone}</Text>
                的来电
            </View>
        </View>
    );
};

export default memo(TelInFo);
