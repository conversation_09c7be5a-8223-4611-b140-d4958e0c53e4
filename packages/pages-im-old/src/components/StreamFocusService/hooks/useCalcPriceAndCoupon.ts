import { useMemo } from 'react';

import type { ICouponList } from '../../../typings/service';
import type { CalculatedSkuPriceInfoType, FocusServiceSku } from '../FocusServiceCard/index.d';

interface ICalcPriceAndCouponType {
    defaultSkuListData: FocusServiceSku[];
    selectedCouponData: ICouponList | null;
    selectedSkuItemData: FocusServiceSku | undefined;
}

export const useCalcPriceAndCoupon = (args: ICalcPriceAndCouponType) => {
    const { selectedCouponData, selectedSkuItemData } = args;

    /**
     *
     * @description 计算当前选中的 sku 的价格信息
     */
    const selectedSkuTotalPriceInfo = useMemo<CalculatedSkuPriceInfoType | null>(() => {
        try {
            const actualSellingPrice = selectedSkuItemData?.priceInfo.promotionPrice
                ? selectedSkuItemData?.priceInfo.promotionPrice
                : (selectedSkuItemData?.priceInfo.salePrice || 0) - (selectedCouponData?.salePrice || 0);

            let totalDiscountPrice = 0;

            if (selectedSkuItemData?.priceInfo?.totalReductionPrice) {
                totalDiscountPrice = selectedSkuItemData?.priceInfo?.totalReductionPrice;
            } else if (
                (selectedSkuItemData?.priceInfo?.salePrice || 0)
                    - (selectedSkuItemData?.priceInfo?.promotionPrice || 0)
                > 0
            ) {
                totalDiscountPrice
                    = (selectedSkuItemData?.priceInfo?.salePrice || 0)
                    - (selectedSkuItemData?.priceInfo?.promotionPrice || 0);
            }

            return {
                actualSellingPrice,
                totalDiscountPrice,
                promotionReductionPrice: selectedSkuItemData?.priceInfo?.promotionReductionPrice || 0,
                originalPrice: selectedSkuItemData?.priceInfo.salePrice || 0,
                couponDiscountPrice: selectedSkuItemData?.priceInfo?.couponReductionPrice || 0,
                totalPrice: selectedSkuItemData?.priceInfo?.totalPrice || 0,
                platformFee: selectedSkuItemData?.priceInfo?.platformFee || 0 // 平台服务费
            };
        } catch (err) {
            console.error('计算当前选中的 sku 的价格信息出错：', err);

            return null;
        }
    }, [selectedCouponData, selectedSkuItemData]);

    return {
        selectedSkuTotalPriceInfo
    };
};
