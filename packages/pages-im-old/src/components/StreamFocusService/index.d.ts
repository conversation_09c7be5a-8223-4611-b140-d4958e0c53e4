import {MsgId} from '../../typings';
import type {ICouponList} from '../../typings/service';
import type {StreamFocusServiceCardDataV2, CouponInfo} from './FocusServiceCard/index.d';
import type {CollectedInfoProps} from './PatientCard/index.d';
import type {RequireDescProps} from './ConsultForm/index.d';

// 就诊卡片和sku数据结构
export interface ImCollectedInfoAndSkuData {
    skuData: StreamFocusServiceCardDataV2;
    requireDesc: RequireDescProps;
    collectedInfo: CollectedInfoProps;
    coupon?: {
        couponInfo?: CouponInfo;
        userCoupons?: {
            list: ICouponList[];
            disableList: ICouponList[];
        };
    };
}

export interface ImCollectedInfoAndSkuProps {
    cardId: string;
    cardName: string;
    version: number;
    data: {
        content: ImCollectedInfoAndSkuData;
    };
    msgId: MsgId;
}


export type OpenPatientType = 'sku' | 'patient';

export type CONSULT_TYPE = 'consultList' | 'consult'; // consultList是就诊人选择弹层，consult是就诊人编辑弹层
