interface FormatPriceOpsType {
    significant?: number;
    multiple?: number;
}

/**
 *
 * @description 价格格式化
 * @param price {number} 价格
 * @param ops {FormatPriceOpsType} 格式化参数(significant 有效位数, multiple 除10的倍数)
 * @returns {number}
 */
export const formatPrice = (price: number, ops?: FormatPriceOpsType): string => {
    try {
        const { significant = 2, multiple = 2 } = ops || {};
        const isDivisible = price % Math.pow(10, multiple) === 0;

        if (isDivisible && significant) {
            return String(price / Math.pow(10, multiple));
        }
        const res = (price / Math.pow(10, multiple)).toFixed(significant);

        if (res.endsWith('0')) {
            return res.slice(0, -1);
        }

        return res;
    } catch (err) {
        console.error('formatPrice 执行出错:', err);

        return String(price);
    }
};

/**
 *
 * @description 年龄格式化
 * @param  {age:string, month:string} 年龄，月数
 * @returns {ageText:string, age:string} 格式化后的年龄
 */
export const formatAge = ({ age, month }: { age: string; month: string }): { ageText: string; age: string } => {
    const _intData = { age: '', ageText: '' };

    if (!age && !month) {
        return _intData;
    }

    try {
        return {
            ageText: +age < 3 ? `${age}岁${month}个月` : `${age}岁`,
            age: +age < 3 ? `${age}-${month}` : age
        };
    } catch (err) {
        console.error('formatPrice 执行出错:', err);

        return _intData;
    }
};

export const formatAgeText = _item => {
    const [age, month = 0] = _item.split('-') || [];
    if (+age === 0 && month) {
        return `${month as number}个月`;
    }

    return age < 3 && month > 0 ? `${age as string}岁${month as number}个月` : `${age as string}岁`;
};
