import {View} from '@tarojs/components';
import {showLoading, hideLoading} from '@tarojs/taro';
import {memo, type FC, useCallback, useState, useReducer, useRef, useEffect} from 'react';
import {Portal} from '@baidu/vita-ui-cards-common';

import {useGetUrlParams} from '../../hooks/common';
import {
    useGetUserData,
    useGetSessionId,
    useUpdateUserData,
    useGetAdjectiveDirectedSkuMsgId
} from '../../hooks/triageStream/pageDataController';
import {useMsgDataSetController} from '../../hooks/triageStream/dataController';
import {getUserBizActionReq} from '../../models/services/triageStream';
import {showToast} from '../../utils/customShowToast';
import {ubcCommonViewSend, ubcCommonClkSend} from '../../utils/generalFunction/ubc';
import {useScrollControl} from '../../hooks/common/useScrollControl';

import PatientCard from './PatientCard';
import ConsultForm from './ConsultForm';
import FocusServiceCard from './FocusServiceCard';
import {formReducer} from './ConsultForm/form-reducer';
import {useGetMakeOrderRelatedInfo} from './hooks/useGetMakeOrderRelatedInfo';
import type {PatientInfo} from './ConsultForm/index.d';
import type {FocusServiceSku} from './FocusServiceCard/index.d';
import type {ImCollectedInfoAndSkuProps, OpenPatientType} from './index.d';

/**
 *
 * @description AI智能体定向服务卡
 * @returns
 */
const StreamFocusService: FC<ImCollectedInfoAndSkuProps> = props => {
    const {content} = props?.data || {};
    const {collectedInfo, skuData, requireDesc, coupon} = content;
    const openPatientPopTypeRef = useRef<boolean>(false); // 存储点开来源类型，用于判断是否发生了变化
    const [showConsultForm, setShowConsultForm] = useState(false); // 是否展示编辑态表单弹层
    const [showConsultList, setShowConsultList] = useState(false); // 是否展示选择就诊人列表弹层
    const [openPatientPopType, setOpenPatientPopType] = useState<OpenPatientType>('patient'); // 点开打开弹窗的来源类型
    const [selectedSkuItemData, setSelectedSkuItemData] = useState<FocusServiceSku>(
        (skuData?.skuList || [])[0]
    );

    const [isSkuDisabled, setIsSkuDisabled] = useState(false);
    const [updateCollectedInfoAndSkuType, setUpdateCollectedInfoAndSku] =
        useState<OpenPatientType>('patient');
    const [selectPatientData, setSelectPatientData] = useState<PatientInfo | undefined>(
        collectedInfo?.curPatient
    );

    const {userData} = useGetUserData();
    const prevIsLoginRef = useRef<boolean>(userData?.isLogin || false); // 存储之前的值，用于判断是否发生了变化

    const sessionId = useGetSessionId();
    const {expert_id} = useGetUrlParams();
    const {updateUserData} = useUpdateUserData();
    const {adjectiveDirectedSkuMsgId} = useGetAdjectiveDirectedSkuMsgId();
    const {updateMsgData} = useMsgDataSetController({msgId: props?.msgId || ''});
    const [isSkuDetailPopupShow, setIsSkuDetailPopupShow] = useState(false); // 是否展示 sku 详情弹窗
    const {scrollToMessage} = useScrollControl();

    useEffect(() => {
        setIsSkuDisabled(adjectiveDirectedSkuMsgId !== props?.msgId);
    }, [adjectiveDirectedSkuMsgId, props?.msgId]);

    const [state, dispatch] = useReducer(formReducer, {
        ...collectedInfo?.curPatient,
        canSubmit: true,
        telPhone: collectedInfo?.telPhone,
        zhusu: collectedInfo?.clinicalDesc || ''
    });

    // 登录 & 下单部分逻辑
    const {loginSuccessfulCallback, PhoneConsultModalNode} = useGetMakeOrderRelatedInfo({
        selectedSkuItemData,
        msgId: props?.msgId,
        skuList: skuData?.skuList || []
    });

    /**
     *
     * @description 更新选中的 skuId，并触发相关计算
     */
    const updateSkuDetailData = useCallback((selectSkuData: FocusServiceSku) => {
        setSelectedSkuItemData(selectSkuData); // 更新选中的 sku 数据
    }, []);

    // 就诊人不存在时，直接展示表单编辑态
    const openEditPatientPop = useCallback(() => {
        setShowConsultList(false);
        setShowConsultForm(true);
        dispatch({
            payload: {
                ...collectedInfo,
                ...collectedInfo?.curPatient,
                zhusu: collectedInfo?.clinicalDesc
            }
        });
    }, [collectedInfo]);

    // 就诊人存在时，打开就诊人选择弹层
    const openEditPatient = useCallback(() => {
        if (collectedInfo?.curPatient?.contactId) {
            setShowConsultList(true);
            setSelectPatientData(
                collectedInfo?.curPatient?.contactId ? collectedInfo?.curPatient : undefined
            );
            dispatch({
                payload: {
                    ...collectedInfo?.curPatient,
                    zhusu: collectedInfo?.clinicalDesc || ''
                }
            });
        }
    }, [collectedInfo]);

    const handleAddPatient = useCallback(() => {
        setShowConsultForm(true);
    }, []);

    // 处理弹层关闭逻辑
    const handleCloseConsultForm = useCallback(
        (type: string) => {
            if (type === 'consult') {
                setShowConsultForm(false);
                dispatch({
                    payload: {
                        ...collectedInfo?.curPatient,
                        zhusu: state?.zhusu || ''
                    }
                });
            }
            if (type === 'consultList') {
                setSelectPatientData(undefined);
                setShowConsultList(false);
            }
        },
        [collectedInfo?.curPatient, state?.zhusu]
    );

    // 选择就诊人
    const handleSelectPatient = useCallback(
        (selectPatient: PatientInfo) => {
            setSelectPatientData(selectPatient);
            dispatch({
                payload: {
                    ...selectPatientData,
                    zhusu: state?.zhusu || ''
                }
            });
        },
        [selectPatientData, state?.zhusu]
    );

    // 根据当前选中的skuId查找最新的sku数据
    const findSelectedSku = useCallback(
        (skuId: string) => {
            return skuData.skuList?.find(skuItem => skuItem.skuId === skuId);
        },
        [skuData]
    );

    const addPatientToast = useCallback(() => {
        showToast({
            title: '请先填写就诊人信息',
            icon: 'none'
        });
    }, []);

    // 监听登录发生变化后的数据处理
    useEffect(() => {
        if (openPatientPopTypeRef.current) {
            if (!collectedInfo?.curPatient?.contactId || !collectedInfo?.clinicalDesc) {
                openPatientPopType === 'sku' && addPatientToast();
                openEditPatientPop();
                setIsSkuDetailPopupShow(false);

                return;
            }
            // 未登录变为登录在状态后，存在就诊人时，点击的为就诊人模块时弹窗就诊人选择列表，否则直接走下单逻辑
            if (updateCollectedInfoAndSkuType === 'patient') {
                openEditPatient();
            } else {
                const selectSku = findSelectedSku(selectedSkuItemData?.skuId || '');
                // 直接走下单逻辑
                selectSku && loginSuccessfulCallback(selectSku);
            }
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [openPatientPopTypeRef.current]);

    // 更新定向服务卡数据，重新渲染卡片组件
    const updateCollectedInfoAndSku = useCallback(
        async (type: OpenPatientType, selectSku?: FocusServiceSku) => {
            if (isSkuDisabled) {
                showToast({
                    title: '请点击最新的服务卡',
                    icon: 'none'
                });
                sessionId &&
                    adjectiveDirectedSkuMsgId &&
                    scrollToMessage(adjectiveDirectedSkuMsgId, 'updateCollectedInfoAndSku');

                ubcCommonClkSend({
                    value: 'streamFocusService_disable'
                });

                return;
            }

            selectSku && setSelectedSkuItemData(selectSku);
            setOpenPatientPopType(type);

            // 未登录
            if (sessionId && !prevIsLoginRef.current) {
                const params = {
                    bizActionType: 'userLogin' as const,
                    chatData: {
                        sessionId,
                        expertId: Number(expert_id || '')
                    },
                    bizActionData: {
                        userLoginInfo: {
                            msgId: props?.msgId || '',
                            patientInfo: collectedInfo?.curPatient
                        }
                    }
                };
                showLoading({
                    title: '登录中...',
                    mask: true
                });
                const [err, data] = await getUserBizActionReq<'userLogin'>(params);
                if (!err) {
                    data?.data?.message[0] && updateMsgData(data?.data?.message[0]);
                    data?.data?.userData && updateUserData(data?.data?.userData);
                    prevIsLoginRef.current = data?.data?.userData?.isLogin || false;
                    setUpdateCollectedInfoAndSku(type);
                    openPatientPopTypeRef.current = true;
                    hideLoading();
                }
            } else {
                // 已登录--无就诊人
                if (!collectedInfo?.curPatient?.contactId || !collectedInfo?.clinicalDesc) {
                    // 点击【去咨询】时，需要先toast后再出弹层
                    type === 'sku' && addPatientToast();
                    openEditPatientPop();

                    return;
                }
                // 已登录--有就诊人--就诊人选择模块
                if (type === 'patient') {
                    openEditPatient();
                } else {
                    // 已登录--有就诊人--服务卡去咨询
                    // 直接走下单逻辑
                    selectSku && loginSuccessfulCallback(selectSku);
                }
            }

            let ubcValue = '';
            if (type === 'sku') {
                ubcValue = `ImAIRecommendUnDirect_${selectSku?.skuId}`;
            } else {
                ubcValue = 'ImAIRecommendUnDirect_edit';
            }

            ubcCommonClkSend({
                value: ubcValue
            });
        },
        [
            addPatientToast,
            collectedInfo?.clinicalDesc,
            collectedInfo?.curPatient,
            expert_id,
            isSkuDisabled,
            loginSuccessfulCallback,
            openEditPatient,
            openEditPatientPop,
            props?.msgId,
            sessionId,
            updateMsgData,
            updateUserData,
            adjectiveDirectedSkuMsgId
        ]
    );

    useEffect(() => {
        ubcCommonViewSend({
            value: 'streamFocusService'
        });
    }, []);

    return (
        <View style={{width: '100%'}}>
            {/* AI重构-就诊人 */}
            <PatientCard
                isLogin={userData?.isLogin || false}
                collectedInfo={collectedInfo}
                updateCollectedInfoAndSku={updateCollectedInfoAndSku}
                isSkuDisabled={isSkuDisabled}
            />
            {/* AI重构-定向服务卡*/}
            <FocusServiceCard
                skuData={skuData}
                coupon={coupon}
                updateSkuDetailData={updateSkuDetailData}
                selectedSkuItemData={selectedSkuItemData}
                updateCollectedInfoAndSku={updateCollectedInfoAndSku}
                isLogin={userData?.isLogin}
                isSkuDisabled={isSkuDisabled}
                msgId={props?.msgId || ''}
                sessionId={sessionId || ''}
                expertId={expert_id}
                PhoneConsultModalNode={PhoneConsultModalNode}
                adjectiveDirectedSkuMsgId={adjectiveDirectedSkuMsgId}
                isSkuDetailPopupShow={isSkuDetailPopupShow}
                setIsSkuDetailPopupShow={setIsSkuDetailPopupShow}
            />
            <Portal>
                {collectedInfo && (showConsultForm || showConsultList) && (
                    <ConsultForm
                        showConsultForm={showConsultForm}
                        showConsultList={showConsultList}
                        collectedInfo={collectedInfo}
                        requireDesc={requireDesc}
                        handleAddPatient={handleAddPatient}
                        handleCloseConsultForm={handleCloseConsultForm}
                        formType={selectedSkuItemData.formType}
                        openEditPatient={openEditPatient}
                        state={state}
                        dispatch={dispatch}
                        openPatientPopType={openPatientPopType}
                        handleSelectPatient={handleSelectPatient}
                        selectPatientData={selectPatientData}
                        selectedSkuItemData={selectedSkuItemData}
                        loginSuccessfulCallback={loginSuccessfulCallback}
                        skuList={skuData?.skuList}
                        msgId={props?.msgId || ''}
                        couponId={coupon?.couponInfo?.id}
                        sessionId={sessionId || ''}
                        expertId={expert_id}
                        setSelectPatientData={setSelectPatientData}
                    />
                )}
            </Portal>
        </View>
    );
};

export default memo(StreamFocusService);
