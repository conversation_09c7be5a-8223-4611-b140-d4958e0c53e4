import cx from 'classnames';
import {memo, type FC} from 'react';
import {View, Text} from '@tarojs/components';

import {CLoginButton} from '@baidu/vita-ui-cards-common';


import {formatAgeText} from '../common/utils';

import styles from './index.module.less';
import type {PatientInfoProps} from './index.d';

/**
 *
 * @description 定向服务卡 v2 版本
 * @returns
 */
const PatientCard: FC<PatientInfoProps> = props => {
    const {collectedInfo, isLogin, updateCollectedInfoAndSku, isSkuDisabled} = props;
    const {curPatient, clinicalDesc} = collectedInfo;

    return (
        <CLoginButton
            isLogin={isSkuDisabled ? isSkuDisabled : isLogin}
            closeShowNewUserTag={true}
            useH5CodeLogin={true}
            onLoginFail={error => {
                console.log('error', error);
            }}
            onLoginSuccess={async () => {
                await updateCollectedInfoAndSku('patient');
            }}
            className='wz-flex'
        >
            <View
                className={cx(
                    styles.patientContainer,
                    isSkuDisabled ? styles.disable : '',
                    'wz-plr-45 wz-pb-90 wz-pt-45 wz-flex wz-col-center wz-row-between'
                )}
            >
                <View className={cx(styles.patientLeft, 'wz-fs-42 wz-fw-400')}>
                    <View className='wz-flex wz-mb-27'>
                        <View className={cx(styles.patientLeftLabel)}>就 诊 人 ：</View>
                        {curPatient && (curPatient?.gender || curPatient?.age || curPatient?.name) ? (
                            <View className='wz-flex'>
                                <Text className={cx(styles.line1, 'wz-mr-24')}>
                                    {curPatient?.name ? curPatient?.name : '匿名'}
                                </Text>
                                <View className={styles.curPatientInfo}>
                                    <Text className='wz-mr-24'>{curPatient?.gender}</Text>
                                    {curPatient?.age && <Text>{formatAgeText(curPatient?.age)}</Text>}
                                </View>
                            </View>
                        ) : (
                            <View className={cx(styles.line1, styles.noInfo)}>请填写</View>
                        )}
                    </View>
                    <View className='wz-flex'>
                        <View className={cx(styles.patientLeftLabel)}>描述信息：</View>
                        <View className={cx(styles.line1)}>{clinicalDesc}</View>
                    </View>
                </View>
                <View
                    className={cx(
                        styles.patientRightBtn,
                        isSkuDisabled ? styles.disable : '',
                        'wz-plr-45 wz-ptb-21 wz-fs-42 wz-fw-500 wz-flex wz-col-center'
                    )}
                >
                    修改
                </View>
            </View>
        </CLoginButton>
    );
};

export default memo(PatientCard);
