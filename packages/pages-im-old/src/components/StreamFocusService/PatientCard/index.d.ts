import type { PatientList } from '../ConsultForm/patientList.d';

export interface CurPatientProps {
    contactId: string;
    name: string;
    age: string;
    gender: string;
}

export interface patientItemProps extends CurPatientProps {
    isCertified: number; // 是否实名
}

export interface CollectedInfoProps {
    editable?: boolean; // 是否可修改
    allowedCreatePatient?: boolean; // 是否允许创建患者
    curPatient?: CurPatientProps; // 当前选中就诊人信息
    patientList?: PatientList[];
    clinicalDesc?: string; // 病情描述
    servicePhone?: string; // 服务电话
    telPhone?: string; // 电话号码
    zhusu?: string;
    age?: string;
    gender?: string;
    contactId?: string;
    openEditPatientPop?: string;
}

export interface PatientInfoProps {
    collectedInfo: CollectedInfoProps;
    isLogin: boolean;
    updateCollectedInfoAndSku: (type: string) => void;
    isSkuDisabled?: boolean;
}
