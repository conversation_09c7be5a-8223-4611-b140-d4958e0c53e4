/**
 * @file nps用户调研弹层组件的类型
 * @author: go<PERSON><PERSON><PERSON><PERSON>@baidu.com
 */

import {CardSuccessInfo} from '../../../models/services/nps/submitNpsInfo/index.d';
import type {
    ScoreItemValue,
    ScoreShow,
    NpsModel,
    TriggerType
} from '../../../models/services/nps/getNpsInfo/index.d';

export interface IProps {
    open?: boolean;
    selectedScale?: ScoreItemValue;
    popInfo?: NpsModel;
    triggerType?: TriggerType;
    appraiseId?: string;
    msgId?: string;
    onClosePopup?: () => void;
    changeNpsCardStatus?: (arg: CardSuccessInfo) => void;
}

interface CustomInput {
    isRequired: string;
    placeholder: string;
    maxLen: number;
}

interface ConfigRation {
    promptMessage?: string;
    tags?: string[];
    isTagRequired?: number;
    customInput?: CustomInput;
}

// 分值配置一维数组
export interface FlatScaleConfiguration extends ScoreShow {
    configRation?: ConfigRation;
}

// 用户选择标签
export interface TagsInfo {
    text?: string;
    isSelected?: boolean;
}
