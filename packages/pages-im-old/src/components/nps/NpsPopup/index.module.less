.popContent {
    position: relative;
    background-color: transparent;
    overflow: hidden;
}

.title {
    font-size: 60px;
    line-height: 1;
    color: #000311;
    padding-top: 84px;
}

// 标签
.tagsWrapper {
    .tag {
        width: calc((100% - 21px) / 2);
        height: 96px;
        background: #f5f6f9;
        color: #272933;
        letter-spacing: 0;
        text-align: center;
        white-space: nowrap;
        box-sizing: border-box;
    }

    .select {
        background: #e5f9f9;
        font-family: PingFangSC-Medium;
        color: #00c8c8;
        letter-spacing: 0;
        text-align: center;
        font-weight: 500;
        border: 1px solid #00bcbc;
        position: relative;
        overflow: hidden;

        &::before {
            content: '';
            position: absolute;
            bottom: 0;
            right: 0;
            width: 42px;
            height: 43px;
            background: url('https://med-fe.cdn.bcebos.com/wenzhen-mini-app/tag_selected_icon.png')
                no-repeat top left;
            background-size: 100%;
        }
    }
}

// 输入框
.inputWrap {
    background: #f8f8f8;
    line-height: 45px;
    color: #1f1f1f;
}

.inputWrapper {
    overflow: hidden;
    position: relative;

    .commentCont {
        background: #f5f5f5;
        min-height: 201px;
        box-sizing: border-box;
    }

    .textareaPlace {
        padding-bottom: 84px;
    }

    .commentTextLen {
        position: absolute;
        right: 45px;
        bottom: 30px;
        color: #858585;
    }
}

// 按钮
.buttonWrapper {
    background: #fff;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;

    .button {
        height: 132px;
        background: #00c8c8;
        box-shadow: 0 10px 20px 0 rgb(0 200 200 / 30%);
        border-radius: 66px;
        font-family: PingFangSC-Medium;
        font-size: 51px;
        color: #fff;
        letter-spacing: 0;
        text-align: center;
        line-height: 42px;
    }
}

.buttonPlacement {
    height: 205px;
}

// 提交成功
.successWrap {
    margin-top: 465px;

    .successImage {
        width: 270px;
        height: 270px;
    }

    .successText {
        color: #848691;
        margin-bottom: 72px;
    }

    .successBtn {
        background: linear-gradient(315deg, #00d3ea 0%, #00cfa3 100%);
        color: #fff;
        border: none;
        width: 378px;
        height: 132px;
        font-size: 54px;
    }
}
