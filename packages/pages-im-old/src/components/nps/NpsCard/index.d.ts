/*
 * @Author: go<PERSON><PERSON><PERSON><PERSON>@baidu.com
 * @Description: nps问卷卡片组件类型定义
 */
import type {
    ScoreShow,
    NpsRenderType,
    NpsModel,
    UserResponseProps
} from '../../../models/services/nps/getNpsInfo/index.d';
import type {CardSuccessInfo} from '../../../models/services/nps/submitNpsInfo/index.d';
import type {CardsData} from '../../../typings/msg.d';

export interface INpsContent {
    // 用户是否已提交调研问卷
    isParticipated?: 0 | 1;
    // 问卷是否已过期
    isNpsAvailable?: 0 | 1;
    // 问卷标题
    question?: string;
    // 问卷副标题
    titleDesc?: string;
    // 用户评分
    rating?: number;
    // 用户选择左右边项
    userResponse?: UserResponseProps[];
    // 用户选择分段量表
    scale?: number[];
    // 分段
    scoreShow?: ScoreShow[];
    npsRenderType?: NpsRenderType;
    popInfo?: NpsModel;
    // 提交成功的展示信息
    successInfo?: CardSuccessInfo;
}

interface IProps {
    data?: CardsData<INpsContent>;
    msgId?: string;
}
