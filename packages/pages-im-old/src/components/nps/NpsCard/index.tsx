/*
 * @Author: go<PERSON><PERSON><PERSON><PERSON>@baidu.com
 * @Description: nps问卷消息组件
 */
import {memo, FC, useCallback, useState, useEffect} from 'react';
import {View, Image} from '@tarojs/components';
import cx from 'classnames';
import {ubcCommonViewSend, ubcCommonClkSend} from '../../../utils/generalFunction/ubc';
import {showToast} from '../../../utils/customShowToast';
import ScoreOption from '../components/ScoreOption';
import Portal from '../../../../../ui-cards-common/Portal';
import NpsPopup from '../NpsPopup';

import type {CardSuccessInfo} from '../../../models/services/nps/submitNpsInfo/index.d';
import type {IProps} from './index.d';

import styles from './index.module.less';

const NpsCard: FC<IProps> = props => {
    const {data, msgId} = props;
    const {
        npsRenderType,
        isParticipated,
        isNpsAvailable,
        question,
        userResponse,
        scoreShow,
        popInfo,
        successInfo
    } = data?.content || {};

    const [popupOpen, setPopupOpen] = useState(false);
    const [selectedScale, setSelectedScale] = useState<string>('');
    const [cardSuccessInfo, setCardSuccessInfo] = useState<CardSuccessInfo>(successInfo || {});
    const [isCardParticipated, setIsParticipated] = useState<number>(isParticipated || 0);

    useEffect(() => {
        // nps卡片展现打点
        ubcCommonViewSend({
            value: 'nps_card_show'
        });
    }, []);

    // 用户点击分值
    const handleScaleClick = useCallback(
        (curScale: string) => {
            if (!isNpsAvailable) {
                return showToast({
                    title: '问卷已过期',
                    icon: 'none'
                });
            }
            setSelectedScale(curScale);
            setPopupOpen(true);
            ubcCommonClkSend({
                value: 'nps_card_scale_click',
                ext: {
                    value_type: 'npsRenderType',
                    value_id: npsRenderType || '',
                    pos: curScale
                }
            });
        },
        [isNpsAvailable, npsRenderType]
    );

    // 更新卡片状态
    const handleChangeNpsCardStatus = useCallback((data: CardSuccessInfo) => {
        if (data) {
            setCardSuccessInfo(data);
            // 用户已提交
            setIsParticipated(1);
        }
    }, []);

    // 卡片内容渲染
    const getCardCon = useCallback(() => {
        if (isCardParticipated || isParticipated) {
            return (
                <View className='wz-flex wz-col-center wz-row-center wz-mt-63'>
                    <Image
                        src={cardSuccessInfo?.iconUrl || ''}
                        mode='aspectFill'
                        className={styles.cardSuccessIcon}
                    />
                    <View className={cx('wz-ml-18 wz-fs-42', styles.cardSuccessText)}>
                        {cardSuccessInfo?.text || '提交成功，感谢您对我们的反馈'}
                    </View>
                </View>
            );
        }

        return (
            <ScoreOption
                npsRenderType={npsRenderType}
                userResponse={userResponse || []}
                scaleArr={scoreShow}
                handleScaleClick={handleScaleClick}
            />
        );
    }, [
        cardSuccessInfo,
        handleScaleClick,
        isCardParticipated,
        isParticipated,
        npsRenderType,
        scoreShow,
        userResponse
    ]);

    return (
        <>
            <View
                className={cx(
                    'wz-br-36 wz-plr-45',
                    styles.npsWrapper,
                    !isNpsAvailable && !isParticipated ? styles.disabled : ''
                )}
            >
                <View className={cx(styles.question, 'wz-fs-54 wz-fw-500 wz-ptb-48 wz-mt-12')}>
                    {question || ''}
                </View>
                {getCardCon()}
            </View>
            <Portal>
                <NpsPopup
                    open={popupOpen}
                    onClosePopup={() => setPopupOpen(false)}
                    popInfo={popInfo}
                    selectedScale={selectedScale}
                    triggerType={2}
                    msgId={msgId}
                    changeNpsCardStatus={data => handleChangeNpsCardStatus(data)}
                />
            </Portal>
        </>
    );
};

export default memo(NpsCard);
