/**
 * @file nps 10分制分值区间
 * @author: go<PERSON><PERSON><PERSON><PERSON>@baidu.com
 */
import {memo, FC} from 'react';
import cx from 'classnames';
import {View, Image} from '@tarojs/components';

import type {FlatScaleConfiguration} from '../../NpsPopup/index.d';
import type {UserResponseProps} from '../../../../models/services/nps/getNpsInfo/index.d';
import styles from './index.module.less';

interface IProps {
    scaleArr?: FlatScaleConfiguration[];
    userResponse: UserResponseProps[];
    // 用户是否参与过调研
    isParticipated?: 0 | 1;
    userScale?: FlatScaleConfiguration['value'];
    handleScaleClick: (string) => void;
}

// 用户响应分支区间
const UserResponseCom: FC<IProps> = props => {
    const {scaleArr, isParticipated, userResponse, userScale, handleScaleClick} = props;

    return (
        <>
            {/* 用户响应左右边项 */}
            {userResponse && userResponse?.length > 0 && (
                <View className='wz-flex wz-col-center wz-row-between'>
                    {userResponse?.map((item, index) => {
                        return (
                            <View className='wz-flex wz-col-center' key={`response_${index}`}>
                                {item?.icon && (
                                    <Image
                                        className={styles.responseImg}
                                        src={item?.icon}
                                        mode='aspectFit'
                                    />
                                )}
                                <View key={index} className={cx(styles.label, 'wz-fs-42 wz-ml-12')}>
                                    {item?.label || ''}
                                </View>
                            </View>
                        );
                    })}
                </View>
            )}
            {/* 分值区间 */}
            {scaleArr && scaleArr.length > 0 && (
                <View className='wz-pt-42 wz-pb-36'>
                    <View
                        className={cx(
                            styles.scale,
                            'wz-flex wz-br-27 wz-row-between wz-col-center'
                        )}
                    >
                        {scaleArr?.map((item, index) => {
                            if (!item?.value) {
                                return null;
                            }

                            return (
                                <View
                                    className={cx(
                                        styles.scaleItem,
                                        item?.value === userScale ? styles.scaleActive : '',
                                        'wz-flex wz-col-center wz-row-center wz-fs-48 wz-text-center wz-fw-700'
                                    )}
                                    key={`scale_${index}`}
                                    onClick={() => {
                                        !isParticipated && handleScaleClick?.(item?.value);
                                    }}
                                >
                                    {item?.text}
                                </View>
                            );
                        })}
                    </View>
                </View>
            )}
        </>
    );
};

export default memo(UserResponseCom);
