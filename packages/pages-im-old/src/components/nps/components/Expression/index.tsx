/**
 * @file nps 5分制分值区间
 * @author: go<PERSON><PERSON><PERSON><PERSON>@baidu.com
 */
import {memo, FC} from 'react';
import cx from 'classnames';
import {View, Image} from '@tarojs/components';

import type {FlatScaleConfiguration} from '../../NpsPopup/index.d';
import type {TriggerType} from '../../../../models/services/nps/getNpsInfo/index.d';
import styles from './index.module.less';

interface IProps {
    scaleArr?: FlatScaleConfiguration[];
    // 用户是否参与过调研
    isParticipated?: 0 | 1;
    triggerType?: TriggerType;
    userScale?: FlatScaleConfiguration['value'];
    handleScaleClick: (string) => void;
}

// 用户响应分支区间
const Expression: FC<IProps> = props => {
    const {scaleArr, isParticipated, userScale, triggerType, handleScaleClick} = props;

    return (
        <>
            {/* 分值区间 */}
            {scaleArr && scaleArr.length > 0 && (
                <View className='wz-pt-12 wz-pb-45'>
                    <View className={cx('wz-flex wz-row-between')}>
                        {scaleArr?.map((item, index) => {
                            if (!item?.value) {
                                return null;
                            }

                            return (
                                <View
                                    className={cx(
                                        styles.expressionItem,
                                        'wz-flex wz-col-center wz-text-center'
                                    )}
                                    key={`expression_${index}`}
                                    onClick={() => {
                                        if (isParticipated) {
                                            return;
                                        }

                                        if (triggerType === 1) {
                                            setTimeout(() => {
                                                handleScaleClick?.(item?.value);
                                            }, 400);

                                            return;
                                        }

                                        handleScaleClick?.(item?.value);
                                    }}
                                >
                                    {userScale && item?.value === userScale ? (
                                        <Image
                                            src={item?.selectedIconUrl || ''}
                                            className={styles.expressionActive}
                                            mode='aspectFit'
                                        />
                                    ) : (
                                        <Image
                                            src={item?.iconUrl || ''}
                                            className={styles.expressionIcon}
                                            mode='aspectFit'
                                        />
                                    )}
                                    <Image
                                        src={item?.selectedIconUrl || ''}
                                        className={styles.expressionHide}
                                        mode='aspectFit'
                                    />
                                    {item?.text && (
                                        <View
                                            className={cx(
                                                styles.expressionText,
                                                'wz-fs-42 wz-mt-39'
                                            )}
                                        >
                                            {item?.text}
                                        </View>
                                    )}
                                </View>
                            );
                        })}
                    </View>
                </View>
            )}
        </>
    );
};

export default memo(Expression);
