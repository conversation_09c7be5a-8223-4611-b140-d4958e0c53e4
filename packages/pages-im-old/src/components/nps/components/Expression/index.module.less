.expressionItem {
    flex-direction: column;
    flex: 1;
}

.expressionIcon,
.expressionActive {
    width: 120px;
    height: 108px;
    transition: all 0.8s ease;
}

.expressionHide {
    visibility: hidden;
    width: 0;
    height: 0;
}

.expressionIcon:active {
    animation: shrink-grow 0.8s forwards;
}

.expressionActive:active {
    animation: reveal 0.8s forwards;
}

@keyframes shrink-grow {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(0.6);
    }

    100% {
        transform: scale(1);
        opacity: 0;
    }
}

@keyframes reveal {
    0% {
        transform: scale(0.6);
        opacity: 0;
    }

    50% {
        transform: scale(0.7);
        opacity: 0;
    }

    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.expressionText {
    color: #848691;
}
