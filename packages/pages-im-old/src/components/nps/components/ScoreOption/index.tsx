/**
 * @file 分值选项
 * @author: gong<PERSON><PERSON><PERSON>@baidu.com
 */
import {memo, FC} from 'react';

import type {FlatScaleConfiguration} from '../../NpsPopup/index.d';
import type {
    UserResponseProps,
    TriggerType
} from '../../../../models/services/nps/getNpsInfo/index.d';
import UserResponse from '../UserResponse';
import Expression from '../Expression';

interface IProps {
    // 1为10分制，2为5分制
    npsRenderType?: 1 | 2;
    scaleArr?: FlatScaleConfiguration[];
    userResponse?: UserResponseProps[];
    // 用户是否参与过调研
    isParticipated?: 0 | 1;
    triggerType?: TriggerType;
    userScale?: FlatScaleConfiguration['value'];
    handleScaleClick: (string) => void;
}

// 用户响应分支区间
const ScoreOption: FC<IProps> = props => {
    const {
        npsRenderType,
        scaleArr,
        isParticipated,
        userResponse,
        userScale,
        triggerType,
        handleScaleClick
    } = props;

    if (npsRenderType === 1) {
        return (
            <UserResponse
                scaleArr={scaleArr}
                userResponse={userResponse || []}
                isParticipated={isParticipated}
                userScale={userScale}
                handleScaleClick={handleScaleClick}
            />
        );
    }

    if (npsRenderType === 2) {
        return (
            <Expression
                scaleArr={scaleArr}
                isParticipated={isParticipated}
                userScale={userScale}
                triggerType={triggerType}
                handleScaleClick={handleScaleClick}
            />
        );
    }

    return null;
};

export default memo(ScoreOption);
