.consultPopupContent {
    padding-bottom: 240px;
}

.patientAddUploader {
    margin: 0 69px 0 45px;
}

.patientListUploader {
    margin: -24px 38px 0;
}

.patientList {
    background: #fff;
    border-radius: 45px 45px 36px 36px;
}

.patientInfoTitle {
    height: 174px;
    background: #fff;
}

.consultForm {
    .telInfo {
        width: 1104px;
    }

    .form-title {
        &-back {
            position: absolute;
            left: 20px;
            top: 42px;
            z-index: 99;
        }
    }

    .tel-tips {
        background: #f5f6fa;
        height: 112px;
        line-height: 112px;
        box-sizing: border-box;
        color: #50525c;

        .tel {
            color: #00c8c8;
        }
    }

    .quickFill {
        background: #fff;

        &Tips {
            line-height: 70px;
        }

        &Btn {
            width: 204px;
            height: 72px;
            line-height: 72px;
            border: none;
            box-sizing: border-box;
            color: #fff;
            border-radius: 300px;
            background: #00c8c8;
        }

        &BtnDisabled {
            background: #8ae6e6;
        }

        &Text {
            color: #848691;
        }
    }

    .form {
        .name {
            font-family: PingFang-SC-Medium;
            color: #1f1f1f;
            font-weight: 700;
        }

        .colorGray {
            color: #858585;
        }
    }

    .textareaContent {
        display: inline-block;
        background: #f5f5f5;
        color: #1f1f1f;
        box-sizing: border-box;
        width: 100%;
        min-height: 240px;
    }

    .patient-list {
        margin-bottom: 185px;

        &-add {
            margin-top: 0;
            margin-bottom: 0;
            border-radius: 0;
        }

        &-loading {
            height: 30vh;
        }
    }

    .patient-list-btn {
        background: #fff;
        position: fixed;
        left: 0;
        right: 0;
        bottom: 0;

        &-submit {
            background: #00c8c8;
            color: #fff;
            border: none;
            outline: none;
            box-shadow: 0 10px 20px 0 rgb(0 200 200 / 30%);
            border-radius: 66px;
            height: 132px;
        }
    }

    .editText {
        padding: 0 0 0 5px;
    }

    .btnWrap {
        padding: 24px 51px;
        background: #fff;
        box-sizing: border-box;

        .btn {
            width: calc(100% - 34px);
            color: #fff;
            height: 44px;
            border-radius: 22px;
            line-height: 44px;
            font-family: PingFang-SC-Medium;
            font-size: 16px;
            text-align: center;
            background-image: linear-gradient(134deg, #00cfa3 0%, #05cfcd 61%, #00d3ea 100%);
        }

        .btn-disabled {
            background: #b5b5b5;
        }
    }

    .tagWrapper {
        font-weight: 700;

        .tagTxt {
            margin-top: 2px;
            line-height: 33px;
        }
    }

    .form-card-radius-top {
        border-radius: 36px 36px 0 0;
    }

    .form-card-radius-top__margin {
        border-radius: 36px 36px 0 0;
        margin-top: 0;
    }

    .form-card-radius-bottom {
        border-radius: 0 0 36px 36px;
        margin-top: 0;
    }

    .form-card {
        margin-top: 0;
        border-radius: 0;
    }

    .brief-color-blue {
        color: #00c8c8;
    }

    .brief-color-default {
        color: #848691;
        font-family: PingFang SC;
        font-size: 39px;
    }
}

.patientLoading {
    position: absolute;
    inset: 0;
    margin: auto;
    width: 300px;
    height: 150px;
}

.consult-popup::after {
    content: '';
    width: 1056px;
    height: 87px;
    margin: 0 auto;
    background-image: url('https://med-fe.cdn.bcebos.com/wz-mini/skuHook/privacyTips.png');
    background-size: 100% 100%;
    position: absolute;
    left: 18px;
    right: 0;
    top: -133px;
    z-index: 999;
}

.consult-popup {
    overflow: visible !important;
}

.phoneInfo {
    background: #fff;
    border-radius: 36px 36px 0 0;
}

.formCard {
    margin: 0;
    border-radius: 36px 36px 0 0;
}

.form__item__label__icon {
    color: #fd503e;
}

.placeholderColor {
    color: #858585;
}

.telFormWrapper {
    margin: 0;

    .telPhoneText {
        line-height: 1.4;
    }
}

.inputTelWrapper {
    position: relative;

    .inputTel {
        box-sizing: border-box;
    }

    .closeIcon {
        position: absolute;
        right: 0;
        pointer-events: none;
    }
}

.formGenderBtn {
    height: 90px;
    border-radius: 45px;
    color: #525252;
    background-color: #f5f5f5;

    &.formGenderBtnActive {
        color: #00c8c8;
        background: #e5f9f9;
        font-family: PingFangSC-Medium;
        font-weight: 500;
    }
}
