import cx from 'classnames';
import {View, Text} from '@tarojs/components';
import React, {memo, type FC, useMemo} from 'react';

import styles from './index.module.less';

interface OrderOperationProps {
    isLoadingCouponData: boolean;
}

const OrderOperation: FC<OrderOperationProps> = () => {
    const genPriceCon = useMemo(() => {
        return (
            <View className={cx(styles.priceCon)}>
                <View className={cx(styles.priceConTotal, 'wz-fs-45')}>
                    合计:
                    <Text className={cx(styles.redPrice, 'wz-fs-54')}>￥</Text>
                    <Text className={cx(styles.redPrice, 'wz-fs-72')}>30</Text>
                </View>
                <View className={cx(styles.discount, 'wz-fs-39 wz-mt-24')}>
                    已优惠<Text>￥5</Text>
                </View>
            </View>
        );
    }, []);

    return (
        <View className={cx(styles.orderOperationContainer, 'wz-flex')}>
            {genPriceCon}
            <View className={cx(styles.btn, 'wz-fs-54')}>立即咨询</View>
        </View>
    );
};

export default memo(OrderOperation);
