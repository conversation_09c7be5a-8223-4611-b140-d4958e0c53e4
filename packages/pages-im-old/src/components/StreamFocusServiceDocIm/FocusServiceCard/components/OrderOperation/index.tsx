import cx from 'classnames';
import {View} from '@tarojs/components';
import {CLoginButton} from '@baidu/vita-ui-cards-common';
import React, {memo, type FC} from 'react';

import {WxLoginData} from '../../../../../store/docImAtom/index.type';

import type {CLoginBtnOpsType, FocusServiceSku} from '../../index.d';
import styles from './index.module.less';

interface OrderOperationProps {
    skuData: FocusServiceSku;
    isLogin: boolean;
    // type: patient-就诊人模块，sku-sku列表
    updateCollectedInfoAndSku: (type: 'patient' | 'sku', skuData?: FocusServiceSku) => void;
    cLoginBtnOps: CLoginBtnOpsType;
    sessionControl?: WxLoginData;
}

/**
 *
 * @description 定向服务卡底部操作区
 * @param props
 * @returns
 */
const OrderOperation: FC<OrderOperationProps> = props => {
    const {skuData, isLogin, updateCollectedInfoAndSku, cLoginBtnOps, sessionControl} = props;

    return (
        <View className={cx(styles.orderOperationContainer, 'wz-flex wz-row-right')}>
            <CLoginButton
                useH5CodeLogin
                isLoginPopup={isLogin}
                isLogin={isLogin}
                ubcValue={cLoginBtnOps.ubcValue}
                callbackUrl={cLoginBtnOps.callbackUrl}
                wxLoginInteractionType={cLoginBtnOps.loginInteractionType}
                sessionControl={sessionControl}
                onLoginSuccess={() => {
                    updateCollectedInfoAndSku('sku', skuData);
                }}
            >
                <View className={cx(styles.btn, 'wz-fs-42 wz-flex wz-col-center wz-row-center')}>
                    {skuData?.btnInfo.value || '去咨询'}
                </View>
            </CLoginButton>
        </View>
    );
};

export default memo(OrderOperation);
