import cx from 'classnames';
import {pxTransform} from '@tarojs/taro';
import {View} from '@tarojs/components';
import React, {memo, type FC, useCallback} from 'react';
import {WiseRightArrow} from '@baidu/wz-taro-tools-icons';
import RichRender from '../../../../../components/RichRender';
import {ubcCommonClkSend} from '../../../../../utils/generalFunction/ubc';
import type {CouponTips} from '../../index.d';
import styles from './index.module.less';

interface IProps {
    onTabCoupon?: () => void;
    couponTips: CouponTips[];
}

const CouponModule: FC<IProps> = props => {
    const {onTabCoupon, couponTips} = props;

    const onTabClk = useCallback(() => {
        ubcCommonClkSend({
            value: 'ImRepurchase_clk_couponTip'
        });
        onTabCoupon && onTabCoupon();
    }, [onTabCoupon]);

    return (
        <View
            className={cx(
                styles.container,
                'wz-flex wz-mt-36 wz-mb-36 wz-br-36 wz-ptb-27 wz-plr-27 wz-row-between'
            )}
            onClick={onTabClk}
        >
            <View className={cx(styles.couponTitle, 'wz-flex')}>
                <View className={styles.couponIcon} />
                <View className='wz-fs-42 wz-ml-15'>优惠信息</View>
            </View>
            <View className={cx(styles.couponContentWrapper, 'wz-flex')}>
                <RichRender
                    redStyle={{
                        marginLeft: pxTransform(9),
                        fontWeight: 'bold',
                        flexShrink: 0,
                        fontSize: pxTransform(39)
                    }}
                    textStyle={{
                        fontSize: pxTransform(39)
                    }}
                    richData={couponTips || []}
                />
                <WiseRightArrow color='#B8B8B8' size={45} />
            </View>
        </View>
    );
};

export default memo(CouponModule);
