import {View} from '@tarojs/components';
import React, {memo, type FC, useMemo} from 'react';

import type {StreamFocusServiceCardDataV2, FocusServiceSku, CLoginBtnOpsType} from '../../index.d';
import SkuItem from './SkuItem';

interface SkuModuleProps {
    data: StreamFocusServiceCardDataV2['skuList'];
    updateSkuDetailData: (data: FocusServiceSku) => void;
    isLogin: boolean;
    updateCollectedInfoAndSku: (type: string, skuData?: FocusServiceSku) => void;
    cLoginBtnOps: CLoginBtnOpsType;
}

const SkuModule: FC<SkuModuleProps> = props => {
    const {data, updateSkuDetailData, updateCollectedInfoAndSku, isLogin, cLoginBtnOps} = props;

    const genSkuModule = useMemo(() => {
        return (
            data &&
            Array.isArray(data) &&
            data.map((i, idx) => {
                return (
                    <SkuItem
                        data={i}
                        key={i?.skuId}
                        classNameProp={idx === data.length - 1 ? '' : 'wz-mb-24'}
                        updateSkuDetailData={updateSkuDetailData}
                        updateCollectedInfoAndSku={updateCollectedInfoAndSku}
                        isLogin={isLogin}
                        cLoginBtnOps={cLoginBtnOps}
                    />
                );
            })
        );
    }, [cLoginBtnOps, data, isLogin, updateCollectedInfoAndSku, updateSkuDetailData]);

    return <View className='wz-mb-18'>{genSkuModule}</View>;
};

export default memo(SkuModule);
