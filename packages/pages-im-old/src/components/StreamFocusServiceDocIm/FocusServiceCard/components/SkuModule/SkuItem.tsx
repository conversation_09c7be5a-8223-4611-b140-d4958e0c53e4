import cx from 'classnames';
import {View, Text} from '@tarojs/components';
import React, {memo, type FC, useMemo, useCallback} from 'react';
import {Tag} from '@baidu/wz-taro-tools-core';
import {CLoginButton, Price} from '@baidu/vita-ui-cards-common';
import {formatPrice} from '../../../../../utils/generalFunction/price';
import {ubcCommonClkSend} from '../../../../../utils/generalFunction/ubc';
import {useGetWxLoginData} from '../../../../../hooks/docIm/pageDataController';

import OrderOperation from '../OrderOperation';
import type {FocusServiceSku, CLoginBtnOpsType} from '../../index.d';
import styles from './skuItem.module.less';

interface SkuItemProps {
    data: FocusServiceSku;
    updateSkuDetailData: (data: FocusServiceSku) => void;
    classNameProp?: string;
    isLogin: boolean;
    updateCollectedInfoAndSku: (type: string, skuData?: FocusServiceSku) => void;
    cLoginBtnOps: CLoginBtnOpsType;
}

const SkuItem: FC<SkuItemProps> = props => {
    const {data, updateSkuDetailData, updateCollectedInfoAndSku, isLogin, cLoginBtnOps} = props;
    const wxLoginData = useGetWxLoginData();

    const openSkuDetailPopup = useCallback(
        (d: FocusServiceSku) => {
            updateSkuDetailData(d);

            ubcCommonClkSend({
                value: `ImFocusService_v2_clkSubtitle_${d.skuId}`
            });
        },
        [updateSkuDetailData]
    );

    /**
     *
     * @description 生成划线价格
     */
    const genLineThroughPrice = useMemo(() => {
        if (
            data?.priceInfo.totalPrice &&
            data?.priceInfo.promotionPrice &&
            data?.priceInfo.promotionPrice < data?.priceInfo.totalPrice
        ) {
            return (
                <View className={cx(styles.skuPrice, 'wz-fs-42 wz-mr-18')}>
                    ¥{formatPrice(data?.priceInfo.totalPrice)}
                </View>
            );
        }

        return <></>;
    }, [data?.priceInfo.promotionPrice, data?.priceInfo.totalPrice]);

    /**
     *
     * @description 生成库存显示
     */
    const genStockQuantity = useMemo(() => {
        if (data.stockQuantity) {
            return (
                <View className={cx(styles.stockQuantity, 'wz-ml-18 wz-fs-36')}>
                    仅剩{data.stockQuantity}个
                </View>
            );
        }

        return <></>;
    }, [data.stockQuantity]);

    // 获取sku单位价格，是否有分位
    const isFenValue = useCallback(price => {
        if (!price) return false;

        return price % 100 !== 0;
    }, []);

    return (
        <View className={cx(styles.skuItemContainer, 'wz-ptb-36')}>
            <CLoginButton
                useH5CodeLogin
                isLoginPopup={isLogin}
                isLogin={isLogin}
                ubcValue={cLoginBtnOps.ubcValue}
                callbackUrl={cLoginBtnOps.callbackUrl}
                wxLoginInteractionType={cLoginBtnOps.loginInteractionType}
                sessionControl={wxLoginData}
                onLoginSuccess={() => {
                    updateCollectedInfoAndSku('sku', data);
                }}
            >
                <View
                    className={cx(styles.skuIcon, 'wz-mr-27')}
                    style={{
                        backgroundImage: `url(${data.icon})`
                    }}
                />
            </CLoginButton>
            <View className={cx(styles.skuCon)}>
                <CLoginButton
                    useH5CodeLogin
                    isLoginPopup={isLogin}
                    isLogin={isLogin}
                    ubcValue={cLoginBtnOps.ubcValue}
                    callbackUrl={cLoginBtnOps.callbackUrl}
                    wxLoginInteractionType={cLoginBtnOps.loginInteractionType}
                    sessionControl={wxLoginData}
                    onLoginSuccess={() => {
                        updateCollectedInfoAndSku('sku', data);
                    }}
                >
                    <View className={cx(styles.row1)}>
                        <View className={cx(styles.skuName, 'wz-fs-54 wz-fw-500')}>
                            {data.title}
                        </View>
                        {data.label && (
                            // eslint-disable-next-line max-len
                            <Tag
                                style={{
                                    color: '#FD5031',
                                    borderRadius: '4px',
                                    marginLeft: '6px',
                                    fontWeight: 500
                                }}
                                variant='outlined'
                            >
                                {data.label}
                            </Tag>
                        )}
                        {genStockQuantity}
                    </View>
                </CLoginButton>
                <View
                    className={cx(styles.row2, 'wz-fs-42')}
                    onClick={() => openSkuDetailPopup(data)}
                >
                    <View className={cx(styles.row2Title)}>{data.subtitle}</View>
                    <View className={styles.conArrow} />
                </View>
            </View>
            <View className={cx(styles.rightsCon, 'wz-flex')}>
                <View className={cx(styles.priceCon, 'wz-flex')}>
                    {genLineThroughPrice}
                    <View className={cx(styles.skuSalePrice, 'wz-pr-45')}>
                        <Text className='wz-fs-36'>￥</Text>
                        <Price
                            price={formatPrice(data?.priceInfo.promotionPrice || 0, {
                                significant: isFenValue(data?.priceInfo.promotionPrice) ? 2 : 1,
                                multiple: 2
                            })}
                        />
                    </View>
                </View>
                <OrderOperation
                    skuData={data}
                    updateCollectedInfoAndSku={updateCollectedInfoAndSku}
                    isLogin={isLogin}
                    cLoginBtnOps={cLoginBtnOps}
                    sessionControl={wxLoginData}
                />
            </View>
        </View>
    );
};

export default memo(SkuItem);
