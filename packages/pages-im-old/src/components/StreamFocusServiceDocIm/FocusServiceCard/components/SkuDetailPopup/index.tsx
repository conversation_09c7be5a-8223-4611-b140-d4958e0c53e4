import cx from 'classnames';
import {nextTick} from '@tarojs/taro';
import {View, Text} from '@tarojs/components';
import React, {type FC, memo, useCallback, useEffect, useMemo, useState} from 'react';
import {Popup} from '@baidu/wz-taro-tools-core';
import {CLoginButton} from '@baidu/vita-ui-cards-common';
import {WiseCheckSelectedSolid, WarningCircle} from '@baidu/wz-taro-tools-icons';

import {formatPrice} from '../../../../../utils/generalFunction/price';
import {ubcCommonViewSend} from '../../../../../utils/generalFunction/ubc';

import type {FocusServiceSku, CalculatedSkuPriceInfoType, CLoginBtnOpsType} from '../../index.d';
import styles from './index.module.less';

interface IProps {
    open: boolean;
    skuData: FocusServiceSku;
    priceData: CalculatedSkuPriceInfoType | null;
    closeCallback: () => void;
    isLogin: boolean;
    updateCollectedInfoAndSku: (type: string, sku?: FocusServiceSku) => void;
    cLoginBtnOps: CLoginBtnOpsType;
}

const SkuDetailPopup: FC<IProps> = props => {
    const {
        open,
        skuData,
        priceData,
        closeCallback,
        isLogin,
        updateCollectedInfoAndSku,
        cLoginBtnOps
    } = props;

    const [openTip, setOpenTip] = useState(false); // 服务费规则说明展示
    const [serviceId, setServiceId] = useState<string | undefined>(undefined); // 控制服务费展开id

    /**
     *
     * @description 生成 sku 使用规则
     */
    const genSkuRights = useMemo(() => {
        if (skuData?.skuDetail?.rights?.length) {
            return skuData.skuDetail?.rights?.map((i, idx) => {
                return (
                    <View
                        className={cx(
                            styles.skuRightItem,
                            'wz-flex wz-col-top',
                            idx === skuData.skuDetail?.rights?.length - 1 ? '' : 'wz-mb-42'
                        )}
                        key={idx}
                    >
                        <WiseCheckSelectedSolid
                            color='#00C8C8'
                            size={54}
                            className={styles.wiseCheckSelected}
                        />
                        <View className={cx(styles.skuRightValue, 'wz-fs-45 wz-ml-15')}>
                            {i.value}
                        </View>
                    </View>
                );
            });
        }

        return null;
    }, [skuData?.skuDetail?.rights]);

    /**
     *
     * @description 生成 sku 使用规则
     */
    const genSkuRules = useMemo(() => {
        if (skuData?.skuDetail?.rules?.length) {
            return skuData.skuDetail?.rules?.map((i, idx) => {
                return <View key={idx}>{i.value}</View>;
            });
        }

        return null;
    }, [skuData?.skuDetail?.rules]);

    /**
     *
     * @description 生成 sku 服务费说明规则
     */
    const genSkuPlatformFeeDesc = useMemo(() => {
        if (skuData?.skuDetail?.platformFeeDesc?.length) {
            return skuData.skuDetail?.platformFeeDesc?.map((i, idx) => {
                return <View key={idx}>{i.value}</View>;
            });
        }

        return null;
    }, [skuData?.skuDetail?.platformFeeDesc]);

    const handleTipChange = useCallback(() => {
        setOpenTip(!openTip);
        // H5滑动到底部
        try {
            if (process.env.TARO_ENV === 'h5') {
                nextTick(() => {
                    const dom = document.getElementById('skuDetailId');
                    dom?.scrollIntoView({
                        inline: 'nearest',
                        behavior: 'smooth'
                        // block: 'end'
                    });
                });
            } else {
                nextTick(() => {
                    setServiceId('bottomId');
                });
            }
        } catch (err) {
            console.error('h5 兼容性滚动定位出错', err);
        }
    }, [openTip]);

    /**
     *
     * @description 生成价格明细
     */
    const genPriceCon = useMemo(() => {
        try {
            if (!priceData?.originalPrice) return null;

            const conList = [
                {
                    type: 'price',
                    text: skuData?.title,
                    value: priceData?.originalPrice || 0
                },
                {
                    type: 'platformFee',
                    text: '平台服务费',
                    value: priceData?.platformFee || 0,
                    iconData: (
                        <>
                            {skuData?.skuDetail?.platformFeeDesc &&
                                skuData?.skuDetail?.platformFeeDesc?.length > 0 && (
                                <WarningCircle
                                    color='#B8B8B8'
                                    size={39}
                                    className='wz-ml-12 wz-flex'
                                    onClick={handleTipChange}
                                />
                            )}
                        </>
                    )
                },
                {
                    type: 'discount',
                    text: '平台立减',
                    node: (
                        <>
                            <Text
                                className={cx(styles.priceTitleIcon, 'wz-mr-18')}
                                style={{
                                    backgroundImage:
                                        'url(https://med-fe.cdn.bcebos.com/wz-mini/ImFocusServiceV2/discountIcon.png)'
                                }}
                            />
                            <Text>平台立减</Text>
                        </>
                    ),
                    value: priceData?.promotionReductionPrice || 0
                },
                {
                    type: 'discount',
                    text: '优惠劵',
                    node: (
                        <>
                            <Text
                                className={cx(styles.priceTitleIcon, 'wz-mr-18')}
                                style={{
                                    backgroundImage:
                                        'url(https://med-fe.cdn.bcebos.com/wz-mini/ImFocusServiceV2/couponIcon.png)'
                                }}
                            />
                            <Text>优惠券</Text>
                        </>
                    ),
                    value: priceData?.couponDiscountPrice || 0
                }
            ];

            return conList.map((i, idx) => {
                if (i.type === 'price' || (i.type === 'platformFee' && i.value > 0)) {
                    return (
                        <>
                            <View
                                className={cx(styles.priceItem, idx === 0 ? '' : 'wz-mt-45')}
                                key={idx}
                            >
                                <View className='wz-flex'>
                                    <View className={cx(styles.priceTitle, 'wz-fs-45')}>
                                        {i.node || i.text}
                                    </View>
                                    {i.iconData ? i.iconData : null}
                                </View>
                                <View className={cx(styles.priceValue)}>
                                    ￥{formatPrice(i.value)}
                                </View>
                            </View>
                            {/* 服务费说明 */}
                            {i.type === 'platformFee' && openTip && (
                                <View
                                    className={cx(
                                        styles.servicePriceTip,
                                        'wz-plr-36 wz-ptb-36 wz-br-36 wz-fs-42 wz-mt-27'
                                    )}
                                >
                                    {genSkuPlatformFeeDesc}
                                </View>
                            )}
                        </>
                    );
                } else if (i.value) {
                    return (
                        <>
                            <View className={cx(styles.priceItem, 'wz-mt-45')} key={idx}>
                                <View className={cx(styles.priceTitle, 'wz-fs-45')}>
                                    {i.node || i.text}
                                </View>
                                <View className={cx(styles.discountValue)}>
                                    {i.type === 'discount' ? '-' : ''}￥{formatPrice(i.value)}
                                </View>
                            </View>
                        </>
                    );
                }
            });
        } catch (err) {
            return null;
        }
    }, [
        priceData?.originalPrice,
        priceData?.promotionReductionPrice,
        priceData?.couponDiscountPrice,
        priceData?.platformFee,
        skuData?.skuDetail?.platformFeeDesc,
        openTip,
        skuData?.title,
        handleTipChange,
        genSkuPlatformFeeDesc
    ]);

    useEffect(() => {
        skuData?.skuId &&
            ubcCommonViewSend({
                value: `ImFocusService_v2_skuDetailPopup_${skuData?.skuId}`
            });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return (
        <Popup
            rounded
            open={open}
            title='服务详情'
            placement='bottom'
            titleStyle={{
                backgroundColor: '#FFF',
                border: 'none',
                borderBottom: 'none'
            }}
            style={{height: '80%'}}
            onClose={closeCallback}
            scrollViewId={serviceId}
        >
            <Popup.Close />
            <View className={cx(styles.skuDetailContainer)} id='skuDetailId'>
                <View className={cx(styles.skuDetailTitle, 'wz-fs-54 wz-mb-54')}>
                    {skuData?.title}
                </View>
                {genSkuRights}
                <View className={cx(styles.skuRules, 'wz-fs-45 wz-mt-54')}>{genSkuRules}</View>
                <View className={cx(styles.skuPriceTitle, 'wz-fs-54  wz-mb-54')}>价格明细</View>
                <View className='wz-mb-90'>{genPriceCon}</View>
                {/* 点开服务费规则时，用于小程序定位到最底部 */}
                <View id='bottomId' />
            </View>
            <Popup.Button>
                <View className={cx(styles.popupBtnContainer, 'wz-flex')}>
                    <View className={cx(styles.priceContainer)}>
                        <View className={cx(styles.salePrice, 'wz-fs-45')}>
                            <Text className={styles.salePriceText}>合计：</Text>
                            <Text className={styles.redText}>￥</Text>
                            <Text className={cx(styles.salePriceValue, styles.redText, 'wz-fs-72')}>
                                {priceData?.actualSellingPrice
                                    ? formatPrice(priceData?.actualSellingPrice)
                                    : 0}
                            </Text>
                        </View>
                        {priceData?.totalDiscountPrice && priceData?.totalDiscountPrice > 0 ? (
                            <View className={cx(styles.discountPrice, 'wz-fs-39 wz-mt-21')}>
                                已优惠￥{formatPrice(priceData.totalDiscountPrice)}
                            </View>
                        ) : null}
                    </View>
                    <CLoginButton
                        useH5CodeLogin
                        isLoginPopup={isLogin}
                        isLogin={isLogin}
                        ubcValue={cLoginBtnOps.ubcValue}
                        callbackUrl={cLoginBtnOps.callbackUrl}
                        wxLoginInteractionType={cLoginBtnOps.loginInteractionType}
                        onLoginSuccess={() => {
                            updateCollectedInfoAndSku('sku', skuData);
                        }}
                    >
                        <View
                            className={cx(
                                styles.popBtn,
                                'wz-fs-54 wz-flex wz-col-center wz-row-center'
                            )}
                        >
                            {skuData?.btnInfo.value || '立即咨询'}
                        </View>
                    </CLoginButton>
                </View>
            </Popup.Button>
        </Popup>
    );
};

export default memo(SkuDetailPopup);
