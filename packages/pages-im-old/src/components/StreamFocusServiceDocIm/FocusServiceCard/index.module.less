.container {
    width: 100%;
    background: #fff;
    padding: 0 45px;
    border-radius: 66px;
    overflow: hidden;
    box-sizing: border-box;
    position: relative;

    .showMore {
        margin-bottom: 60px;
    }

    .isDisabledContainer {
        position: absolute;
        background: #fff;
        inset: 0;
        opacity: 0.4;
        z-index: 4;

        .isDisabledIcon {
            position: absolute;
            right: 0;
        }

        .ineffectiveIcon {
            width: 198px;
            height: 156px;
            position: absolute;
            top: 0;
            right: 0;
        }
    }
}
