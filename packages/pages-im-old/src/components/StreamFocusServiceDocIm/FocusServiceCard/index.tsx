import cx from 'classnames';
import {View, Block} from '@tarojs/components';
import {WiseDownArrow} from '@baidu/wz-taro-tools-icons';
import {Portal, CLoginButton} from '@baidu/vita-ui-cards-common';
import {memo, type FC, useState, useCallback, useMemo, useEffect} from 'react';

import CouponPopup from '../../../components/pagePopup/CouponPopup';
import CouponExplanation from '../../../components/pagePopup/CouponPopup/CouponExplanation';

import {getUseractionReq} from '../../../models/services/docIm';

import {useCalcPriceAndCoupon} from '../hooks/useCalcPriceAndCoupon';
import {useScrollControl} from '../../../hooks/common/useScrollControl';
import {useGetWxLoginData} from '../../../hooks/docIm/pageDataController';
import {useMsgDataSetController} from '../../../hooks/docIm/dataController';

import {showToast} from '../../../utils/customShowToast';
import {ubcCommonViewSend, ubcCommonClkSend} from '../../../utils/generalFunction/ubc';

import {getDataForUbcAtom} from '../../../store/docImAtom';

import type {ICouponList} from '../../../typings/service';

import TopDes from './components/TopDes';
import SkuModule from './components/SkuModule';
import CouponModule from './components/CouponModule';
import SkuDetailPopup from './components/SkuDetailPopup';

import styles from './index.module.less';
import type {StreamFocusServiceCardDataV2Props, FocusServiceSku} from './index.d';

const MAX_SKU_LENGTH = 2;

/**
 *
 * @description 定向服务卡 v2 版本
 * @returns
 */
const FocusServiceCard: FC<StreamFocusServiceCardDataV2Props> = props => {
    const {
        skuData,
        coupon,
        selectedSkuItemData,
        updateSkuDetailData,
        isLogin,
        updateCollectedInfoAndSku,
        isSkuDisabled,
        msgId,
        sessionId,
        expertId,
        PhoneConsultModalNode,
        adjectiveDirectedSkuMsgId,
        cLoginBtnOps,
        isSkuDetailPopupShow,
        setIsSkuDetailPopupShow
    } = props;
    const {userCoupons, couponInfo, couponTips} = coupon || {};
    // 优惠劵部分 state
    const [selectedCouponData, setSelectedCouponData] = useState<ICouponList>(couponInfo || {}); // 当前选中的优惠券数据
    const [explanationData, setExplanationData] = useState<ICouponList['description'] | null>(null); // 优惠券说明数据
    // 弹窗控制部分 state
    // const [isSkuDetailPopupShow, setIsSkuDetailPopupShow] = useState(false); // 是否展示 sku 详情弹窗
    const [isShowExplanation, setIsShowExplanation] = useState<boolean>(false); // 是否展示优惠券说明
    const [showPoupon, setShowPoupon] = useState<boolean>(false); // 优惠券是否展示
    const {updateMsgData} = useMsgDataSetController({msgId});
    const [calingPriceState, setCalingPriceState] = useState<boolean>(false); // 是否正在计算价格
    const wxLoginData = useGetWxLoginData();
    const [showRecommendSku, setRecommendSku] = useState(skuData?.skuList?.length > MAX_SKU_LENGTH);

    const {scrollToMessage} = useScrollControl('docImScrollControl');

    // 计算价格 & 优惠券相关逻辑
    const {selectedSkuTotalPriceInfo} = useCalcPriceAndCoupon({
        selectedCouponData,
        selectedSkuItemData,
        defaultSkuListData: skuData?.skuList
    });

    /**
     *
     * @description 更新 sku 详情数据 & 打开 sku 详情弹窗
     */
    const handleUpdateSkuDetailData = useCallback(
        (detailData: FocusServiceSku) => {
            // 排查问题需要，保留
            // eslint-disable-next-line no-console
            console.info('更新 sku 详情数据', detailData);
            setIsSkuDetailPopupShow(true);
            updateSkuDetailData && updateSkuDetailData(detailData);
        },
        [updateSkuDetailData, setIsSkuDetailPopupShow]
    );

    /**
     *
     * @description 更新优惠券说明数据
     */
    const updateExplanationData = useCallback(
        (explanationDataForShow: ICouponList['description']) => {
            setExplanationData(explanationDataForShow);
            setIsShowExplanation(true);
        },
        []
    );

    /**
     *
     * @description 生成 sku 详情弹窗
     */
    const genSkuDetailPopup = useMemo(() => {
        return isSkuDetailPopupShow ? (
            <SkuDetailPopup
                open={isSkuDetailPopupShow}
                skuData={selectedSkuItemData}
                priceData={selectedSkuTotalPriceInfo}
                closeCallback={() => {
                    setIsSkuDetailPopupShow(false);
                }}
                isLogin={isLogin}
                updateCollectedInfoAndSku={updateCollectedInfoAndSku}
                cLoginBtnOps={cLoginBtnOps}
            />
        ) : (
            <></>
        );
    }, [
        isSkuDetailPopupShow,
        selectedSkuItemData,
        selectedSkuTotalPriceInfo,
        isLogin,
        updateCollectedInfoAndSku,
        cLoginBtnOps,
        setIsSkuDetailPopupShow
    ]);

    // 关闭优惠券
    const onCloseCoupon = useCallback(() => {
        setShowPoupon(false);
    }, []);

    /**
     *
     * @description 更新选中的优惠券数据
     */
    const updateSelectedCouponData = useCallback(
        async couponData => {
            const params = {
                bizActionType: 'switchCoupon' as const,
                chatData: {
                    sessionId,
                    expertId: Number(expertId || '')
                },
                bizActionData: {
                    switchCouponInfo: {
                        msgId,
                        couponId: couponData?.id || 0
                    }
                }
            };
            setCalingPriceState(true);
            // TODO 此处会获取到在优惠券弹窗中获取到的优惠券信息，根据优惠券id和msgid去请求消息接口，然后调用更新整个sku消息
            const [err, data] = await getUseractionReq<'switchCoupon'>(params);
            if (!err) {
                setCalingPriceState(false);
                data?.data?.message && updateMsgData(data?.data?.message[0]);
                setSelectedCouponData(couponData);
                onCloseCoupon();
            }
        },
        [expertId, msgId, onCloseCoupon, sessionId, updateMsgData]
    );

    /**
     *
     * @description 生成 sku 优惠劵弹窗
     */
    const genCouponDetailPopup = useMemo(() => {
        // eslint-disable-next-line max-len
        return (
            showPoupon &&
            ((userCoupons?.list && userCoupons?.list.length > 0) ||
                (userCoupons?.disableList && userCoupons?.disableList.length > 0)) && (
                <CouponPopup
                    calingLoading={calingPriceState}
                    disableCouponList={userCoupons?.disableList || []}
                    enableCouponList={userCoupons?.list || []}
                    defaultSelectCouponData={couponInfo || null}
                    showCouponExplanation={updateExplanationData}
                    changeSelectedCouponCallback={updateSelectedCouponData}
                    closeCallback={() => {
                        setShowPoupon(false);
                    }}
                    sourceVal='imRepurchase'
                />
            )
        );
    }, [
        showPoupon,
        userCoupons?.list,
        userCoupons?.disableList,
        calingPriceState,
        couponInfo,
        updateExplanationData,
        updateSelectedCouponData
    ]);

    /**
     *
     * @description 生成优惠券说明弹窗
     */
    const genCouponExplainPopup = useMemo(() => {
        if (isShowExplanation) {
            return (
                <CouponExplanation
                    data={explanationData}
                    open={isShowExplanation}
                    onClose={() => {
                        setIsShowExplanation(false);
                        setExplanationData(null);
                    }}
                />
            );
        }

        return null;
    }, [explanationData, isShowExplanation]);

    // 点击优惠券
    const onTabCoupon = useCallback(() => {
        if (!isLogin) {
            showToast({
                title: '请先登录',
                icon: 'none'
            });

            return;
        }
        setShowPoupon(true);
    }, [setShowPoupon, isLogin]);

    /**
     *
     * @description 生成优惠券模块
     */
    const genCouponModule = useMemo(() => {
        if (userCoupons?.list && userCoupons?.list.length > 0) {
            return <CouponModule onTabCoupon={onTabCoupon} couponTips={couponTips || []} />;
        }
        // 未登录情况下默认展示新人券
        if (!isLogin && couponTips && couponTips?.length > 0) {
            return (
                <CLoginButton
                    useH5CodeLogin
                    isLoginPopup={isLogin}
                    isLogin={isLogin}
                    ubcValue={cLoginBtnOps.ubcValue}
                    callbackUrl={cLoginBtnOps.callbackUrl}
                    wxLoginInteractionType={cLoginBtnOps.loginInteractionType}
                    sessionControl={wxLoginData}
                    onLoginSuccess={() => {
                        updateCollectedInfoAndSku('newcomerCoupon');
                    }}
                >
                    <CouponModule onTabCoupon={onTabCoupon} couponTips={couponTips || []} />
                </CLoginButton>
            );
        }
        // eslint-disable-next-line max-len
    }, [
        userCoupons?.list,
        isLogin,
        couponTips,
        onTabCoupon,
        cLoginBtnOps.ubcValue,
        cLoginBtnOps.callbackUrl,
        cLoginBtnOps.loginInteractionType,
        wxLoginData,
        updateCollectedInfoAndSku
    ]);

    const memoSkuList = useMemo(() => {
        if (!showRecommendSku) return skuData?.skuList || [];
        return skuData?.skuList?.slice(0, MAX_SKU_LENGTH) || [];
    }, [showRecommendSku, skuData?.skuList]);

    // 动态更新更多展示按钮
    useEffect(() => {
        setRecommendSku(skuData?.skuList?.length > MAX_SKU_LENGTH);
    }, [skuData?.skuList?.length]);

    const getLineTextCom = useCallback(() => {
        if (!showRecommendSku) return null;

        ubcCommonViewSend({
            value: 'streamFocusService_moreSku',
            ext: {
                product_info: {
                    ...getDataForUbcAtom()?.product_info,
                    msgId: props?.msgId || ''
                }
            }
        });

        return (
            <View className={cx(styles.showMore, 'wz-mt-24')}>
                <View className='c-color-desc wz-ml-48 wz-mr-48 wz-fs-42, wz-text-center'>
                    <View
                        className={cx('wz-plr-48 wz-flex wz-col-center wz-row-center')}
                        onClick={() => {
                            setRecommendSku(showRecommendSku => !showRecommendSku);
                            msgId &&
                                scrollToMessage(
                                    msgId,
                                    'components/StreamFocusServiceDocIm/FocusServiceCard/index'
                                );

                            ubcCommonClkSend({
                                value: 'streamFocusService_moreSku',
                                ext: {
                                    product_info: {
                                        ...getDataForUbcAtom()?.product_info,
                                        msgId: props?.msgId || ''
                                    }
                                }
                            });
                        }}
                    >
                        更多服务推荐
                        <WiseDownArrow size={42} className='wz-ml-9' />
                    </View>
                </View>
            </View>
        );
    }, [showRecommendSku, props?.msgId, msgId, scrollToMessage]);

    return (
        <View className={cx(styles.container)}>
            <Block>
                <TopDes isGuaranteed={skuData?.isGuaranteed} data={skuData?.topDes} />
            </Block>
            {/* 优惠券模块 */}
            <Block>{genCouponModule}</Block>
            {/* sku列表模块 */}
            <Block>
                <SkuModule
                    data={memoSkuList}
                    updateSkuDetailData={handleUpdateSkuDetailData}
                    updateCollectedInfoAndSku={updateCollectedInfoAndSku}
                    isLogin={isLogin}
                    cLoginBtnOps={cLoginBtnOps}
                />
            </Block>

            {getLineTextCom()}

            {/* sku详情弹窗 */}
            <Portal>{genSkuDetailPopup}</Portal>
            <Portal>{genCouponDetailPopup}</Portal>
            <Portal>{genCouponExplainPopup}</Portal>
            <Portal>{PhoneConsultModalNode}</Portal>
            {/* 非最新的sku卡，不可点击，有遮罩层 */}
            {isSkuDisabled && (
                <View
                    className={styles.isDisabledContainer}
                    onClick={() => {
                        showToast({
                            title: '请点击最新的服务卡',
                            icon: 'none'
                        });
                        adjectiveDirectedSkuMsgId &&
                            scrollToMessage(
                                adjectiveDirectedSkuMsgId,
                                'components/StreamFocusServiceDocIm/FocusServiceCard/index'
                            );
                    }}
                ></View>
            )}
        </View>
    );
};

export default memo(FocusServiceCard);
