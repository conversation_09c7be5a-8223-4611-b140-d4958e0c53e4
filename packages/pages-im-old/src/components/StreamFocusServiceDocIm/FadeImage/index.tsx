import React, {useState, useEffect, useRef} from 'react';
import {View} from '@tarojs/components';
import {HImage} from '@baidu/health-ui';
import cx from 'classnames';

interface FadeImageProps {
    src: string;
    width?: number | string;
    height?: number | string;
    fadeInDuration?: string;
    fadeOutDuration?: string;
    className?: string;
    style?: React.CSSProperties;
    [key: string]: unknown;
}

const FadeImage = ({
    src,
    width,
    height,
    fadeInDuration = '1s',
    fadeOutDuration = '0.5s',
    className,
    style,
    ...rest
}: FadeImageProps) => {
    const [displayedSrc, setDisplayedSrc] = useState(src);
    const [fadeState, setFadeState] = useState<'fade-in' | 'fade-out' | ''>('');
    const nextSrcRef = useRef<string | null>(null);

    useEffect(() => {
        if (src && src !== displayedSrc) {
            setFadeState('fade-out');
            nextSrcRef.current = src;
        }
    }, [src, displayedSrc]);

    const handleTransitionEnd = () => {
        if (fadeState === 'fade-out' && nextSrcRef.current) {
            setDisplayedSrc(nextSrcRef.current);
            setFadeState('fade-in');
            nextSrcRef.current = null;
        } else if (fadeState === 'fade-in') {
            setFadeState('');
        }
    };

    return (
        <View
            className={cx('fade-image-wrapper', className)}
            style={{
                transition:
                    fadeState === 'fade-out'
                        ? `opacity ${fadeOutDuration}`
                        : fadeState === 'fade-in'
                            ? `opacity ${fadeInDuration}`
                            : `opacity ${fadeInDuration}`,
                opacity: fadeState === 'fade-out' ? 0 : 1,
                ...style
            }}
            onTransitionEnd={handleTransitionEnd}
        >
            <HImage src={displayedSrc} width={width} height={height} {...rest} />
        </View>
    );
};

export default FadeImage;
