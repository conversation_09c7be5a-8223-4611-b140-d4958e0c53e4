import React, {memo, type FC} from 'react';
import {pxTransform} from '@tarojs/taro';
import cx from 'classnames';
import {View, Text} from '@tarojs/components';
import {isEmpty} from '../../../../../utils/index';
import {getSystemInfo} from '../../../../../utils/taro/get_system_info/index';

import styles from '../../index.module.less';

interface ImageItem {
    small?: string;
    origin?: string;
    fileName?: string;
}

interface PatientImagesProps {
    images?: ImageItem[];
    unShowImageCount?: number;
    className?: string;
}

const {bigFontSizeClass} = getSystemInfo();

const PatientImages: FC<PatientImagesProps> = ({images, unShowImageCount, className}) => {
    if (isEmpty(images)) {
        return null;
    }

    return (
        <View className={cx('wz-mt-21', className)}>
            <View className='wz-pl-24'>
                <View className={cx(styles.markdown)}>
                    <Text className={cx(styles.bold, 'bold')}>图片资料：</Text>
                </View>
            </View>
            <View className={cx(styles.patientImage, 'wz-mt-27')}>
                {images?.map((item, index) => (
                    <View
                        key={item?.small}
                        className={cx(styles.patientImageItem, 'wz-mr-9')}
                        style={{backgroundImage: `url(${item?.small})`}}
                    >
                        {index === (images?.length || 0) - 1 && unShowImageCount ? (
                            <View
                                className={cx(
                                    styles.patientImageItemDesc,
                                    bigFontSizeClass ? styles.patientImageBigFont : '',
                                    'wz-flex wz-col-center wz-row-center'
                                )}
                                style={{fontSize: bigFontSizeClass ? '12PX' : pxTransform(42)}}
                            >
                                ＋{unShowImageCount}
                            </View>
                        ) : null}
                    </View>
                ))}
            </View>
        </View>
    );
};

export default memo(PatientImages, (prevProps, nextProps) => {
    if (prevProps?.images?.length !== nextProps?.images?.length) {
        return false;
    }

    if (prevProps?.unShowImageCount !== nextProps?.unShowImageCount) {
        return false;
    }

    // 比较图片数组内容
    if (prevProps?.images && nextProps?.images) {
        for (let i = 0; i < prevProps?.images?.length; i++) {
            if (prevProps?.images[i]?.fileName !== nextProps?.images[i]?.fileName) {
                return false;
            }
        }
    }

    return true;
});
