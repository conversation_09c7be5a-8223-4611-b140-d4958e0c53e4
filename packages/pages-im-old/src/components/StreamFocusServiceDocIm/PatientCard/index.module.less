.patientContainer {
    background: #e9f5fd;
    border-radius: 66px;
    border: 3px solid #fff;
    width: 100%;
    position: relative;
    box-sizing: border-box;

    .patientImage {
        display: flex;
        justify-content: left;

        &Item {
            width: 171px;
            height: 171px;
            border-radius: 22.5px;
            border: 0.75px solid #dcdde04c;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            position: relative;

            &Desc {
                position: absolute;
                top: 0;
                right: 0;
                z-index: 9;
                background-image: url('https://med-fe.cdn.bcebos.com/wenzhen-mini-app/vita/patientCardIcon.png');
                background-size: contain;
                background-repeat: no-repeat;
                background-position: top right;
                text-align: center;
                width: 72px;
                height: 72px;
                line-height: 72px;
                color: #fff;
            }

            .patientImageBigFont {
                width: 90px;
                height: 90px;
                line-height: 90px;
            }
        }
    }

    .markdown {
        padding: 0;
        padding-left: 32px;
        font-size: 42px;
        line-height: 66px;
        text-indent: -32px;

        .bold {
            font-weight: 700;
        }

        /* stylelint-disable-next-line selector-pseudo-class-no-unknown */
        :global {
            .p {
                margin-bottom: 0;
                color: #272933;
            }

            .bold {
                position: relative;
                color: #272933;
                padding-left: 35px;
            }

            .bold::before {
                position: absolute;
                inset: 0;
                margin: auto 0;
                content: '';
                width: 12px;
                height: 12px;
                background: #848691;
                border-radius: 50%;
            }

            .italic {
                font-style: normal;
                color: #848691;
            }

            .gradient-icon::after {
                background: linear-gradient(
                    90deg,
                    rgb(242 250 255 / 10%) 0%,
                    rgb(242 250 255 / 90%) 100%
                );
            }
        }
    }

    .patientLeft {
        color: #525252;
    }

    .patientRightBtn {
        border: 1px solid #00c8c8;
        border-radius: 90px;
        color: #00c8c8;
        flex-shrink: 0;
        height: 84px;
        box-sizing: border-box;
    }

    &Title {
        color: #272933;
        font-size: 48px;
        height: 84px;
    }

    .noInfo {
        color: #b8b8b8;
    }
}

.patientContainerGradient {
    width: 100%;
    position: relative;
}

.borderTop {
    border-radius: 63px 63px 0 0;
}

.fade-image-wrapper {
    display: flex;
    align-items: center;

    /* 让动画更丝滑 */
    will-change: opacity;
}

.ineffectiveIcon {
    width: 243px;
    height: 180px;
    position: absolute;
    z-index: 9;
    top: 0;
    right: 153px;
}

.isDisabledContainer {
    position: absolute;
    background: #fff;
    inset: 0;
    opacity: 0.4;
    z-index: 5;
    border-radius: 66px 66px 0 0;
}
