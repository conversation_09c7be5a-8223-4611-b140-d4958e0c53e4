import cx from 'classnames';

import {memo, useMemo, useState, type FC} from 'react';
import {View, Text, ScrollView} from '@tarojs/components';

import {WiseDownArrow} from '@baidu/wz-taro-tools-icons';
import styles from './index.module.less';
import {type TypewriterContainerProps} from './index.d';

const TypewriterContainer: FC<TypewriterContainerProps> = props => {
    const {
        children,
        isScroll,
        typewriterNum,
        content,
        showAllDesc,
        setShowAllDesc,
        type,
        isFinish
    } = props;
    const [isExceed, setisExceed] = useState(false);

    const memoScrollClass = useMemo(() => {
        if (showAllDesc || type === 2 || type === 3) {
            return '';
        }

        return styles.patientDesc;
    }, [showAllDesc, type]);

    // 暂时去掉展示按钮
    const memoShowMoreBtn = useMemo(() => {
        // if (isExceed && !showAllDesc && isFinish) {
        //     return true;
        // }
        return false;
    }, []);

    const memoScrollTop = useMemo(() => {
        return isFinish ? {} : {scrollTop: typewriterNum * 500};
    }, [isFinish, typewriterNum]);

    if (!content) {
        return null;
    }

    if (isScroll) {
        return (
            <ScrollView
                {...memoScrollTop}
                scrollY
                className={memoScrollClass}
                onScroll={e => {
                    if (e?.detail?.scrollTop) {
                        !isExceed && setisExceed(true);
                    }
                }}
            >
                {children}
                {memoShowMoreBtn ? (
                    <View
                        className={cx(styles.showMore, 'wz-flex wz-col-center wz-fs-42')}
                        onClick={() => {
                            setShowAllDesc(true);
                        }}
                    >
                        <Text className={styles.showMoreText}>展开</Text>
                        <WiseDownArrow size={42} />
                    </View>
                ) : null}
            </ScrollView>
        );
    }

    return children;
};

export default memo(TypewriterContainer);
