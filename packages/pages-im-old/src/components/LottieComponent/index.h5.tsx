import cx from 'classnames';
import {View} from '@tarojs/components';
import lottie, {AnimationItem} from 'lottie-web';
import {FC, useEffect, useRef} from 'react';

import type {LottieComponentProps} from './index.d';

import styles from './index.module.less';

const LottieComponent: FC<LottieComponentProps> = (props: LottieComponentProps) => {
    const {
        className,
        customStyle,
        bosPath,
        renderer = 'canvas',
        loop = true,
        autoplay = true,
        onAnimationComplete,
        onAnimationError
    } = props;

    const lottieContainer = useRef<HTMLDivElement>(null);
    const animationRef = useRef<AnimationItem | null>(null);

    useEffect(() => {
        if (!lottieContainer.current) {
            return;
        }

        try {
            animationRef.current = lottie.loadAnimation({
                container: lottieContainer.current,
                renderer,
                loop,
                autoplay,
                path: bosPath
            });

            // 添加 onAnimationComplete 回调
            if (onAnimationComplete) {
                animationRef.current.addEventListener('complete', onAnimationComplete);
            }

            // 添加 onAnimationError 回调
            if (onAnimationError) {
                animationRef.current.addEventListener('error', onAnimationError);
            }

            // 在组件卸载时销毁动画
            return () => {
                if (animationRef.current) {
                    animationRef.current.destroy();
                }
            };
        } catch (error) {
            console.error('Lottie 动画加载失败:', error);
            if (onAnimationError) {
                onAnimationError();
            }
        }
    }, [renderer, loop, autoplay, onAnimationComplete, onAnimationError, bosPath]);

    return (
        <View
            ref={lottieContainer}
            style={{...customStyle}}
            className={cx(styles.container, className)}
        />
    );
};

export default LottieComponent;
