import React from 'react';

export interface LottieComponentProps {
    canvasWith?: number;
    canvasHeight?: number;
    className?: string;
    customStyle?: React.CSSProperties;
    bosPath: string; // lottie 的 bos json 地址
    loop?: boolean;
    autoplay?: boolean;
    hidden?: boolean;
    renderer?: 'svg' | 'canvas';
    onAnimationComplete?: () => void;
    onAnimationError?: () => void;
    onEnded?: () => void;
}
