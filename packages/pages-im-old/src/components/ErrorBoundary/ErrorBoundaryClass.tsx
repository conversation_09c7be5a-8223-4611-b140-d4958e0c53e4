import React from 'react';

const isDev = process.env.NODE_ENV === 'development';

// 捕获异常后显示的组件
type FallbackElement = React.ReactElement<
    unknown,
    string | React.FC | typeof React.Component
> | null;
export declare function FallbackRender(props: FallbackProps): FallbackElement;

// 捕获异常后显示组件传入的props
export interface FallbackProps {
    error: Error;
    resetErrorBoundary: () => void; // fallback 组件里将该函数绑定到“重置”按钮
}

// ErrorBoundary props；对外暴露；通过hooks传入
export interface ErrorBoundaryProps {
    fallback?: FallbackElement;
    FallbackComponent?: React.ComponentType<FallbackProps>; // Fallback 组件
    fallbackRender?: typeof FallbackRender; // 渲染 fallback 元素的函数
    onError?: (error: Error, info: string) => void;
    onReset?: () => void; // 重置逻辑
}

// ErrorBoundaryState 内部初始状态Props
interface ErrorBoundaryState {
    error: Error | null; // Error 类型，报错信息
    errrMaxNum: number; // 错误次数
}

// 初始相关状态
const initialState: ErrorBoundaryState = {
    error: null, // 错误信息
    errrMaxNum: 4 // 最大错误次数
};

let initErrorTime = 0; // 初始错误次数

export class ErrorBoundary extends React.Component<
    React.PropsWithChildren<ErrorBoundaryProps>,
    ErrorBoundaryState
> {
    state = initialState;

    static getDerivedStateFromError(error: Error) {
        return {error};
    }

    componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
        if (this.props.onError) {
            this.props.onError(error, errorInfo.componentStack);
        }
        // 统计错误次数
        initErrorTime = ++initErrorTime;
        this.errorReport({error});
    }

    errorReport = error => {
        if (process.env.TARO_ENV === 'h5' && !isDev && error) {
            window?.__Weirwood && window.__Weirwood?.error?.captureException(error);
        }
        // !(process.env.TARO_ENV === 'h5') && weirwoodErrorReport(error);
    };

    // 重置该组件error异常状态
    reset = () => {
        this.setState(initialState);
    };

    // 执行自定义重置逻辑，并重置组件状态；重新渲染 fallback 组件
    resetErrorBoundary = () => {
        if (this.props.onReset) {
            this.props.onReset();
        }
        this.reset();
    };

    render() {
        const {fallback, FallbackComponent, fallbackRender} = this.props;
        const {error, errrMaxNum} = this.state;

        if (error !== null && initErrorTime >= errrMaxNum) {
            const fallbackProps: FallbackProps = {
                error,
                resetErrorBoundary: this.resetErrorBoundary
            };

            if (React.isValidElement(fallback)) {
                return fallback;
            }
            if (typeof fallbackRender === 'function') {
                return (fallbackRender as typeof FallbackRender)(fallbackProps);
            }
            if (FallbackComponent) {
                return <FallbackComponent {...fallbackProps} />;
            }

            throw new Error('请传入异常兜底组件 fallback | allbackRender |FallbackComponent');
        }

        return this.props.children;
    }
}
