@import '../../style/variable.less';

.historyCon {
    font-family: PingFangSC-Medium;
    display: flex;
    flex-direction: column;
    height: 100%;
    box-sizing: border-box;
    padding-top: 264px;
    z-index: @zIndex-modal;

    .historyTitle {
        width: 100%;
        height: 162px;
        color: #1f1f1f;
        box-sizing: border-box;
        border-bottom: 1px solid #e0e0e0;
        flex-shrink: 0;
    }

    .btnActive {
        background-image: linear-gradient(270deg, rgb(255 255 255 / 0%) 0%, #e5f9f9 100%);
    }

    .itemActive {
        background-image: linear-gradient(270deg, rgb(255 255 255 / 0%) 0%, #e5f9f9 100%);
    }

    .historyListWrap {
        flex: 1;
        box-sizing: border-box;
        overflow: auto;

        .historyItem {
            margin-right: auto;
            overflow: hidden;
            flex: 1;

            .historyText {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

            .historyTime {
                color: #858585;
                font-family: PingFang SC;
            }
        }

        .historyRecordEmpty {
            font-family: PingFang SC;
            width: 100%;
            padding-top: 252px;

            --empty-description-padding: 14px;

            .emptyImg {
                width: 420px;
                height: 420px;
            }
        }
    }

    .operate {
        flex-shrink: 0;

        .operateBtn {
            border-radius: 27px;
            height: 108px;
            box-sizing: border-box;
            font-family: PingFangSC-Regular;
            color: #fff;
            line-height: 48px;
            border: none;

            --button-font-size-small: 48px;
            --button-padding-small: 36px;

            &Icon {
                font-size: 60px;
                vertical-align: middle;
                padding: 0 0 0 10px;
            }
        }
    }

    .dialogBtn {
        width: 100%;
        flex-shrink: 0;
        box-sizing: border-box;

        .btn {
            height: 126px;
            width: 100%;
            background: #00c8c8;
            border: none;
            border-radius: 66px;
            color: #fff;

            --button-font-size-large: 51px;

            margin: 27px auto;
        }

        .btnIcon {
            margin-right: 4px;
            font-weight: 700;
        }
    }
}

@keyframes skeleton-loading {
    0% {
        background-position: 100% 50%;
    }

    100% {
        background-position: 0 50%;
    }
}

.skeletonText {
    height: 48px;
    background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
    background-size: 400% 100%;
    animation: skeleton-loading 1.4s ease infinite;
    border-radius: 4px;
}

.skeletonTime {
    height: 39px;
    width: 200px;
    background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
    background-size: 400% 100%;
    animation: skeleton-loading 1.4s ease infinite;
    border-radius: 4px;
}

.skeletonDelete {
    width: 60px;
    height: 60px;
    background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
    background-size: 400% 100%;
    animation: skeleton-loading 1.4s ease infinite;
    border-radius: 50%;
}

.loadingText {
    font-family: PingFang SC;
    color: #858585;
}
