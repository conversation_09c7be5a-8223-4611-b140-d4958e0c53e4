import { memo, type FC } from 'react';

import TriageImTemplate from '../../skeleton/triageIm';
import type { SkeletonTemplateProps } from './index.d';

const SkeletonTemplate: FC<SkeletonTemplateProps> = props => {
    // 基于name 引入不同的页面骨架屏
    const { name } = props;
    const componentsMap = {
        triageIm: <TriageImTemplate />,
    };

    // 读取配置
    return componentsMap[name] || null;
};

export default memo(SkeletonTemplate);
