// Author: <PERSON><PERSON><PERSON><PERSON>
// Date: 2025-04-08 19:59:25
// Description: 上传图片组件

import React, {memo, useCallback} from 'react';
import {WImage} from '@baidu/wz-taro-tools-core';
import cx from 'classnames';
import {View} from '@tarojs/components';
import {eventCenter} from '@tarojs/taro';

import {UploadImageProps} from './index.d';
import style from './index.module.less';

const UploadImage = memo((props: UploadImageProps) => {
    const {openUploadPopup} = props;

    const handleUpload = useCallback(async () => {
        try {
            openUploadPopup && openUploadPopup();
            // 若在语音播报 则关闭
            eventCenter.trigger('stopTTS');
        } catch (error) {
            console.error(error);
        }
    }, [openUploadPopup]);

    return (
        <View onClick={handleUpload} className={cx(style.uploadImage, 'wz-flex')}>
            {/* <WisePic size={69} /> */}
            <WImage
                src='https://med-fe.cdn.bcebos.com/vita/uploadIcon.png'
                className={style.uploadImageIcon}
            />
        </View>
    );
});

UploadImage.displayName = 'UploadImage';

export default UploadImage;
