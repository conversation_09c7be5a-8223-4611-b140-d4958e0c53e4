.sendVoiceBtn {
    flex-direction: column;
    padding-bottom: 60px;
    padding-top: 150px;

    .sendVoiceText {
        padding-bottom: 60px;
        color: #848691;
    }

    .sendBtnItem {
        width: 100%;
        height: 184px;
        border-radius: 66px;
        padding: 0;
    }

    .cancelTextColor {
        color: #fd503e;
    }

    .cancelBackgroundColor {
        background-color: #fd503e;
        border: 1px solid #fd503e;
    }

    .voiceIngGif {
        background-image: url('https://med-fe.cdn.bcebos.com/wenzhen-mini-app/voicegreen.gif');
        background-repeat: no-repeat;
        background-size: cover;
        width: 100%;
        height: 100%;
    }

    .voiceCancelGif {
        background-image: url('https://med-fe.cdn.bcebos.com/wenzhen-mini-app/voicegreen.gif');
        background-repeat: no-repeat;
        background-size: cover;
        width: 100%;
        height: 100%;
    }
}

.voiceModalMain {
    position: relative;

    .shadowBox {
        position: absolute;
        top: 45px;
        width: 100%;
        height: 45px;
        background: linear-gradient(180deg, #e8e8f2 20%, #fff0 100%);
        z-index: 3;

        .cancelText {
            position: absolute;
            left: 30%;
            background-color: #fa999f;
            border: 1px solid #fa999f;
            border-radius: 81px;
            font-size: 42px;
            color: #fff;
        }
    }
}

.scrollResult {
    height: 397px;
    padding-top: 150px;

    .textCenter {
        text-align: center;
    }

    .textLeft {
        text-align: left;
    }
}

.voiceTextarea {
    padding: 0 108px;
    box-sizing: border-box;
    line-height: 80px;
    font-size: 60px;
    position: relative;
    color: #272933;
}
