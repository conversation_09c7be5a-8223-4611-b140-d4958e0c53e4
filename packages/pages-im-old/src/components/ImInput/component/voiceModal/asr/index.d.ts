export interface IAsrHandler<T = unknown> {
    start: (res?: VoiceTokenInit) => void;
    stop: () => void;
    cancel: () => T;
    canIUse: boolean;
}

export interface IHandlerCallbackNode {
    onError: (err: IAsrError) => void;
    onStart: (...args: unknown[]) => void;
    onRecognize: (...args: unknown[]) => void;

    /**
     * 识别过程中，一句话识别结束，下一句话还未开始
     */
    onRecognizeStop: (...args: unknown[]) => void;
    onRecorderStop: (...args: unknown[]) => void;
    onStop: (...args: unknown[]) => void;
    onCancel: (...args: unknown[]) => void;
}

export interface IAsrError {
    errCode: number;
    errType?: 'noMic' | 'deviceConflict';
    errHandle?: 'reStart' | 'igorne' | 'getMic';
    errToast?: string;
}

export interface VoiceTokenInit {
    secretkey: string;
    secretid: string;
    token: string;
}
