import type {Ref} from 'react';

import type {SceneTypeOfParams} from '../../models/services/triageStream/sse/index.d';
import type {IPicProps} from '../../typings/upload';
import type {UploadStatus} from './capsuleTools/imageListTools/index.d';

export interface ImInputProps {
    ref?: Ref<unknown> | undefined;
    needHold?: boolean;
    maxLine?: number;
    maxLength?: number;
    placeholder?: string;
    confirmHold?: boolean; // 点击右下角发送是否收起键盘
    isDisabled?: boolean; // 禁用发送
    hasImg?: boolean; // 是否上传图片了
    isFocus?: boolean; // 是否聚焦
    imgList?: IPicProps[];
    statusList?: UploadStatus[];
    sceneType?: SceneTypeOfParams;
    setImgList: (imgList: IPicProps[]) => void;
    setStatusList: (statusList: UploadStatus[]) => void;
    onClickCallBack?: () => void;
    openUploadPopup: (sceneType: SceneTypeOfParams) => void;
}
