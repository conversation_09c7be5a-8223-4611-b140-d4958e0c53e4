// Author: z<PERSON><PERSON>yu03
// Date: 2025-04-28 11:12:40
// Description: 图片上传hook

import {useCallback} from 'react';
import {previewPic} from '../../../utils/basicAbility/upload';
import {BUCKET_NAME, ONLINE_HOST} from '../../../constants/common';
import {API_HOST} from '../../../models/apis/host';
import {useConversationDataController} from '../../../hooks/triageStream/useConversationDataController';

import type {SceneTypeOfParams} from '../../../models/services/triageStream/sse/index.d';

const bucketConfName =
    API_HOST && ONLINE_HOST.indexOf(API_HOST) > -1 ? BUCKET_NAME[2] : `${BUCKET_NAME[2]}-test`;

/**
 * useUpload hook
 * 用于图片上传的自定义 hook，返回 handleUpload 方法
 * @returns { handleUpload } 上传图片的方法
 */
export const useUpload = () => {
    const {createConversation} = useConversationDataController();

    /**
     * handleUpload
     * 触发图片选择和上传，上传成功后创建一条图片类型的会话消息
     * @param {Object} param0
     * @param {number} param0.count 允许选择的图片数量，默认1
     */
    const handleUpload = useCallback(
        async ({count = 1, sceneType}: {count?: number; sceneType: SceneTypeOfParams}) => {
            try {
                const preData = await previewPic({count, bucketConfName});
                if (!preData || !Object.keys(preData)?.length) return;
                await createConversation({
                    msg: {
                        type: 'image',
                        content: preData?.[0]?.path,
                        origin: preData?.[0]?.path,
                        preData,
                        sceneType
                    }
                });
            } catch (error) {
                console.error(error);
            }
        },
        [createConversation]
    );

    return {
        handleUpload
    };
};
