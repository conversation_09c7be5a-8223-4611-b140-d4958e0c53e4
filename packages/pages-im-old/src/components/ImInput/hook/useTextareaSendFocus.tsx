import {TextareaProps} from '@tarojs/components';
import {useRef, useCallback} from 'react';
import {useAtom, useAtomValue} from 'jotai';
import {textareaHoldAtom, textareaFocusAtom} from '../../../store/triageStreamAtom';
import {useScrollControl} from '../../../hooks/common/useScrollControl';

export const useTextareaSendFocus = () => {
    const [isFocus, setIsFocus] = useAtom(textareaFocusAtom);
    const isHold = useAtomValue(textareaHoldAtom);
    const h5Focus = useAtomValue(textareaFocusAtom);
    const textareaRef = useRef<TextareaProps>(null);
    const {scrollToBottom} = useScrollControl();

    // 处理输入框聚焦和失焦
    const handleFocusBlur = useCallback(
        e => {
            setIsFocus(e.type === 'focus');
            scrollToBottom('textarea-send-focus');
        },
        [scrollToBottom]
    );

    const handleBlur = useCallback(() => {
        setIsFocus(false);
    }, []);

    const handleFocus = useCallback(() => {
        setIsFocus(true);
    }, []);

    return {
        isHold,
        h5Focus,
        isFocus,
        textareaRef,
        handleFocusBlur,
        handleBlur,
        handleFocus
    };
};
