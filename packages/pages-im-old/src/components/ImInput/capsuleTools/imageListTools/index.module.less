.imageMain {
    position: relative;
    background-color: #fff;
    border-top-right-radius: 63px;
    border-top-left-radius: 63px;
    border: 1px solid #e0e0e0;
    border-bottom: none;
    overflow-x: scroll;
    scrollbar-width: none; /* firefox */
    scrollbar-color: transparent transparent; /* thumb and track color */

    .capsuleShadow {
        position: absolute;
        width: 100%;
        height: 47px;
        background: linear-gradient(180deg, rgb(239 243 249 / 0%) 0%, #eff3f9 100%);
        z-index: 10;
        top: -47px;
        left: 0;
    }

    .imageContainer {
        position: relative;
        border: 0.5px solid #dcdde04d;
        width: 270px;
        height: 270px;
        border-radius: 36px;

        .imageItem {
            width: 270px;
            height: 270px;
            border-radius: 36px;
        }

        .loadingCom {
            position: relative;
            width: 270px;
            height: 270px;

            .coverView {
                width: 100%;
                height: 100%;
                position: absolute;
                border-radius: 36px;
                left: 0;
                top: 0;
                background-color: rgb(0 0 0 / 50%);
            }

            .loadingIcon {
                position: absolute;
                left: 30%;
                top: 30%;
            }

            .loadingImg {
                width: 100%;
                height: 100%;
                border-radius: 36px;
            }
        }

        .errCom {
            position: relative;
            width: 270px;
            height: 270px;

            .coverView {
                width: 100%;
                height: 100%;
                position: absolute;
                border-radius: 36px;
                left: 0;
                top: 0;
                background-color: rgb(0 0 0 / 50%);
            }

            .errItext {
                position: absolute;
                left: 21%;
                top: 25%;
                flex-direction: column;

                .retryUpload {
                    width: 72px;
                    height: 72px;
                }

                .retryText {
                    font-size: 42px;
                    color: #fff;
                }
            }

            .errImg {
                width: 100%;
                height: 100%;
                border-radius: 36px;
            }
        }

        .closeIcon {
            position: absolute;
            width: 60px;
            height: 60px;
            top: -25px;
            right: -25px;
        }
    }

    .continueUploadMain{
        width: 270px;
        height: 270px;
        justify-content: center;
        background-color: #F5F6FA;
        border-radius: 45px;
        flex-shrink: 0;

        .continueUpload{
            width: 102px;
            height: 102px;
        }
    }
}
