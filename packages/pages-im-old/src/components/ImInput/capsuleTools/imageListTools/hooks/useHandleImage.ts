// Author: <PERSON><PERSON><PERSON><PERSON>
// Date: 2025-06-17 21:29:41
// Description: 图片处理hook
import {useState} from 'react';

import type {IPicProps} from '../../../../../typings/upload';
import type {UploadStatus} from '../index.d';

export const useHandleImage = () => {
    const [imgList, setImgList] = useState<IPicProps[]>([]);
    const [statusList, setStatusList] = useState<UploadStatus[]>([]);

    return {
        imgList,
        statusList,
        setStatusList,
        setImgList
    };
};
