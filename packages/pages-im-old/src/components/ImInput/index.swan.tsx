// Author: z<PERSON><PERSON>yu03
// Date: 2025-02-12 14:58:50
// Description: 输入框swan
import cx from 'classnames';
import {View, Textarea} from '@tarojs/components';
import {useAtomValue} from 'jotai';
import {createCanvasContext, hideKeyboard, nextTick, pxTransform} from '@tarojs/taro';
import {memo, useCallback, useEffect, useMemo, useState} from 'react';
import {Button, SafeArea, WImage} from '@baidu/wz-taro-tools-core';
import {getSystemInfo} from '@baidu/vita-utils-shared';

import type {IPicProps} from '../../typings/upload';

import {checkAuth} from '../../utils/basicAbility/getSysAuth';
import {showToast} from '../../utils/customShowToast';
import {ubcCommonClkSend, ubcCommonViewSend} from '../../utils/generalFunction/ubc';
import {validateIsIphoneX, isCanUseKey} from '../../utils/index';
import {isIos} from '../../utils/jumpWx';
// import { useGetTextareaHoldType } from '@src/hooks/triageStream/dataController';
import {useConversationDataController} from '../../hooks/triageStream/useConversationDataController';
import {
    useGetImageSource,
    useGetInputData,
    useSetImageSource
} from '../../hooks/triageStream/pageDataController';
import {useGetSwanMsgListSceneStatus} from '../../hooks/triageStream/useGetSwanMsgListSceneStatus';
import {
    useGetTextareaPlaceholder,
    useSetTextareaPlaceholder
} from '../../hooks/triageStream/dataController';
import {useSetTipsData} from '../../hooks/triageStream/useHandleTipsAction';
// import type {IPicProps} from '../../typings/upload.d';
import {useUpdateServiceDeclareAtomShow} from '../../store/triageStreamAtom/imTools';
import {isGeneratingAtom, setIsUserInterruptedAtom} from '../../store/triageStreamAtom';
import {useTextareaSendFocus} from './hook/useTextareaSendFocus';

import VoiceCom from './component/voiceCom'; // 语音录制组件
import VoiceSwitch from './component/voiceSwitch'; // icon组件
import UploadImage from './uploadImgTools'; // 上传图片组件
import BottomAiText from './bottomAiText'; // 底部AI文案组件

import type {ImInputProps} from './index.d';

import styles from './index.module.less';

const isIpx = validateIsIphoneX();
const ImInput = memo((props: ImInputProps) => {
    const {
        maxLine = 4,
        // placeholder = '请输入想咨询的内容',
        isDisabled = false,
        confirmHold = false,
        maxLength = 500,
        isFocus = false,
        needHold = false,
        hasImg = false,
        imgList = [],
        statusList = [],
        setStatusList,
        sceneType,
        setImgList,
        openUploadPopup,
        onClickCallBack
    } = props;

    const {bigFontSizeClass} = getSystemInfo();

    const [isShowVoice, setIsShowVoice] = useState<boolean>(false); // true为展示语音组件
    const [keyboardHeight, setKeyboardHeight] = useState<number>(0); // 键盘高度
    const [isAutoHeight, setIsAutoHeight] = useState<boolean>(true); // 是否支持自动增高
    const [lineCount, setLineCount] = useState<number>(1);
    const [val, setVal] = useState<string>('');
    const [isShowImageTools, setIsShowImageTools] = useState<boolean>(true);
    const {placeholder} = useGetTextareaPlaceholder();
    const {setPlaceholder} = useSetTextareaPlaceholder();
    const [curSceneType, setCurSceneType] = useState(sceneType);

    const isGenerating = useAtomValue(isGeneratingAtom);
    const {createConversation, cancelPreSSE} = useConversationDataController();
    const {inputData} = useGetInputData() || {};
    const {imageSource} = useGetImageSource();
    const {setImageSource} = useSetImageSource();
    const {setServiceShow} = useUpdateServiceDeclareAtomShow();
    // const {userData} = useGetUserData();
    // const {status: isWelcomeMouleDisplay} = useGetImWelcomeMouleDisplayStatus();
    const {status: isSwanMsgListScene} = useGetSwanMsgListSceneStatus();
    // const showCapsuleTools = useGetCapsuleToolsShow();
    const {setTipsData} = useSetTipsData();

    // 使用自定义 Hook
    const {isHold, textareaRef, handleBlur, handleFocusBlur} = useTextareaSendFocus();

    const {uploadImg, uploadImgInstruction} = inputData || {};

    // useEffect(() => {
    //     setKeyboardHeight(isFocus ? 367 - 34 : 0);
    // }, [isFocus]);

    useEffect(() => {
        sceneType && setCurSceneType(sceneType);
    }, [curSceneType, sceneType]);

    // 修复键盘高度监听器
    useEffect(() => {
        const handleKeyboardHeightChange = (res: any) => {
            try {
                if (res?.height) {
                    // ipx 减去底部刘海高度
                    setKeyboardHeight(isIpx ? res.height - 34 : res.height);
                } else {
                    setKeyboardHeight(0);
                }
            } catch (error) {
                console.error(error);
            }
        };
        // 注册键盘高度变化监听器
        // eslint-disable-next-line no-undef
        swan.onKeyboardHeightChange(handleKeyboardHeightChange);

        // 清理函数，组件卸载时移除监听器
        return () => {
            // eslint-disable-next-line no-undef
            swan.offKeyboardHeightChange(handleKeyboardHeightChange);
        };
    }, []);

    // 上传图片的状态
    const uploadStatus = useMemo(() => {
        if (statusList?.find(item => item === 'pending')) {
            return 'pending';
        } else if (statusList?.every(item => item === 'success')) {
            return 'success';
        } else if (statusList?.find(item => item === 'failed')) {
            return 'failed';
        }
    }, [statusList]);

    const isUploadSuccess = useMemo(() => {
        return !statusList?.find(item => item === 'pending' || item === 'failed');
    }, [statusList]);

    const sendDisabled = useMemo(() => {
        if (isDisabled) {
            return true;
        }
        if (hasImg) {
            return !isUploadSuccess;
        } else {
            return !val?.trim();
        }
    }, [isDisabled, hasImg, isUploadSuccess, val]);

    /**
     * @description icon切换
     */
    const handleChangeIcon = useCallback(() => {
        if (!isShowVoice) {
            // 语音按钮点击埋点
            ubcCommonClkSend({
                value: 'switchAudioInput'
            });
        }
        setStatusList([]);
        setImgList([]);
        setImageSource('');
        setVal('');
        setIsShowVoice(!isShowVoice);
    }, [setImageSource, setImgList, setStatusList, isShowVoice]);

    /**
     * @description 输入框行数&是否自动增高
     */
    const handleChangeLine = useCallback(
        e => {
            isIos() && setLineCount(e.detail.lineCount);
            setIsAutoHeight(!(e.detail.lineCount >= maxLine));
        },
        [maxLine]
    );

    const handleInput = useCallback(
        e => {
            setVal(e.detail.value || '');
            if (isIos()) {
                if (lineCount < maxLine) {
                    setIsAutoHeight(true);
                } else {
                    setIsAutoHeight(false);
                }
            }
        },
        [lineCount, maxLine]
    );

    // 发送图片
    const handleSendImg = useCallback(
        (preData: IPicProps[]) => {
            try {
                preData?.[0]?.path &&
                    createConversation({
                        msg: {
                            type: 'image',
                            content: preData?.[0]?.path,
                            origin: preData?.[0]?.path,
                            sceneType: imageSource,
                            preData
                        }
                    });
            } catch (error) {
                console.error(error);
            }
        },
        [imageSource, createConversation]
    );

    // 发送图片+文字消息
    const handleSendImageText = useCallback(
        msgContent => {
            try {
                createConversation({
                    msg: {
                        type: 'richText',
                        content: msgContent,
                        sceneType: imageSource
                    }
                });
            } catch (error) {
                console.error(error);
            }
        },
        [imageSource, createConversation]
    );

    const clearInputOrImg = useCallback(() => {
        // 清空输入框
        textareaRef.current && (textareaRef.current.value = '');
        setVal('');
        setImgList([]);
        setStatusList([]);
        setImageSource('');
    }, [setImageSource, setImgList, setStatusList, textareaRef]);

    // 处理发送逻辑
    const handleSend = useCallback(() => {
        if (!textareaRef) return;

        const sendText = textareaRef.current?.value?.trim();

        if (hasImg) {
            const imgCount = imgList?.length || 0;
            if (!sendText) {
                clearInputOrImg();
                if (imgCount > 1) {
                    const msgContent = {
                        images: imgList,
                        text: textareaRef?.current?.value?.trim()
                    };
                    handleSendImageText(msgContent); // 多图发富文本组件
                } else {
                    handleSendImg(imgList); // 单图发图片组件
                }
            } else {
                // TODO: 发送图片+文字消息
                const msgContent = {
                    images: imgList,
                    text: sendText
                };
                clearInputOrImg();
                handleSendImageText(msgContent);
            }
            // 发送消息埋点
            const isDisabled = sendDisabled ? 1 : 0;
            ubcCommonClkSend({
                value: 'imSendMsgBtn',
                ext: {
                    product_info: {
                        source: `${imageSource}_upload`,
                        isDisabled,
                        picUpdateStatus: uploadStatus,
                        imgCount
                    }
                }
            });
        } else {
            if (!sendText) return;
            clearInputOrImg();
            // 执行发送逻辑
            createConversation({
                msg: {
                    type: 'text',
                    content: sendText,
                    sceneType: 'wzBotConversation'
                }
            });
        }
    }, [
        textareaRef,
        hasImg,
        sendDisabled,
        imageSource,
        uploadStatus,
        clearInputOrImg,
        handleSendImg,
        imgList,
        handleSendImageText,
        createConversation
    ]);

    const handleConfirm = useCallback(() => {
        handleSend();
    }, [handleSend]);

    const handleImage = useCallback(() => {
        setTipsData(uploadImgInstruction);
        openUploadPopup(sceneType || 'inputImg');
        ubcCommonClkSend({
            value: 'inputUpdateImg'
        });
    }, [sceneType, openUploadPopup, setTipsData, uploadImgInstruction]);

    const showErrorToast = useCallback(() => {
        if (uploadStatus === 'pending') {
            showToast({
                title: '图片正在上传中，请稍后尝试',
                icon: 'none'
            });
        } else {
            showToast({
                title: '图片上传失败，请删除后重新尝试',
                icon: 'none'
            });
        }
        // 发送消息埋点
        const isDisabled = sendDisabled ? 1 : 0;
        ubcCommonClkSend({
            value: 'imSendMsgBtn',
            ext: {
                product_info: {
                    source: `${imageSource}_upload`,
                    isDisabled,
                    picUpdateStatus: uploadStatus
                }
            }
        });
    }, [imageSource, sendDisabled, uploadStatus]);

    const showImgTools = useMemo(() => {
        if (!uploadImg) {
            return false;
        }

        if (!isShowImageTools || val?.trim()?.length) {
            return false;
        }

        return true;
    }, [isShowImageTools, uploadImg, val]);

    const handleStopSSE = useCallback(() => {
        cancelPreSSE('用户点击终止按钮，主动停止');
        setIsUserInterruptedAtom(true);
        ubcCommonClkSend({
            value: 'imStopBtn'
        });
    }, [cancelPreSSE]);

    /**
     * @description 渲染语音组件
     */
    const genVoiceCom = useMemo(() => {
        return <VoiceCom handleChangeIcon={handleChangeIcon} />;
    }, [handleChangeIcon]);

    /**
     * @description 输入框的ios和安卓不同兼容
     */

    const customTextClassnames = useMemo(() => {
        if (isIos()) {
            return cx(
                styles.textarea,
                // 'wz-fs-48',
                'wz-flex',
                !isAutoHeight ? styles.textareaAutoHeight : '',
                lineCount > 1 ? 'wz-mt-36 wz-mb-33' : ''
            );
        }

        return cx(
            styles.textarea,
            'wz-flex'
            // lineCount > 2 ? 'wz-mb-33 wz-mt-33' : ''
        );
    }, [isAutoHeight, lineCount]);

    const androidStyle = useMemo(() => {
        if (!isIos()) {
            return {height: pxTransform(66 * lineCount)};
        }

        return {};
    }, [lineCount]);

    useEffect(() => {
        if (!val) {
            setLineCount(1);
            setIsAutoHeight(false);
        } else {
            if (!isIos() && lineCount < maxLine) {
                const ctx = createCanvasContext('myCanvas');
                ctx.font = '10px';
                const {width = 0} = ctx.measureText(val);
                const newLines = Math.ceil(width / 170);
                newLines !== lineCount && setLineCount(newLines);
            }
        }
    }, [lineCount, maxLine, val]);

    /**
     * @description 渲染输入框
     */
    const genInput = useMemo(() => {
        return (
            <Textarea
                ref={textareaRef}
                // focus={isFocus}
                maxlength={maxLength}
                holdKeyboard={
                    needHold ? (isCanUseKey('input.hold-keyboard') ? !!isHold : false) : false
                }
                className={customTextClassnames}
                style={androidStyle || {}}
                placeholderClass={
                    isSwanMsgListScene
                        ? bigFontSizeClass
                            ? styles.bigFontSizeClass
                            : styles.swanMsgTextareaPlaceholder
                        : bigFontSizeClass
                            ? styles.bigFontSizeClass
                            : styles.textareaPlaceholder
                }
                confirmHold={confirmHold}
                autoHeight={isIos() ? isAutoHeight : false}
                placeholder={placeholder}
                confirmType='send'
                onConfirm={handleConfirm}
                onFocus={e => {
                    handleFocusBlur(e);
                    setIsShowImageTools(false);
                    // setIsInputFocus(true);
                    setServiceShow(false); // 隐藏服务声明
                }}
                onBlur={e => {
                    nextTick(() => {
                        handleFocusBlur(e);
                        setIsShowImageTools(true);
                        // setIsInputFocus(false);
                        setServiceShow(true); // 展示服务声明
                    });
                }}
                onLineChange={handleChangeLine}
                onInput={handleInput}
                adjustPosition={false}
                showConfirmBar={false}
            />
        );
    }, [
        textareaRef,
        // isFocus, // TODO: 本次无需hold，所以改为非受控组件 @zhangzhiyu03
        customTextClassnames,
        androidStyle,
        needHold,
        isHold,
        confirmHold,
        placeholder,
        maxLength,
        isAutoHeight,
        isSwanMsgListScene,
        bigFontSizeClass,
        handleInput,
        handleConfirm,
        handleFocusBlur,
        handleChangeLine,
        setServiceShow
    ]);

    /**
     * @description 渲染停止生成按钮
     */
    const stopBtn = useMemo(() => {
        return (
            <View
                className={cx(
                    styles.stopGenerateImage,
                    'wz-flex',
                    isSwanMsgListScene && cx('wz-mb-27', 'wz-ml-18')
                )}
            >
                <WImage
                    src='https://med-fe.cdn.bcebos.com/wenzhen-mini-app/vita/stop-generating.png'
                    className={styles.stopGenerateIcon}
                    onClick={handleStopSSE}
                />
            </View>
        );
    }, [handleStopSSE, isSwanMsgListScene]);

    const genBtn = useMemo(() => {
        if (hasImg && !isUploadSuccess) {
            return (
                <Button
                    dataset-id='sendBtn'
                    id='sendBtn'
                    color='primary'
                    shape='round'
                    // disabled={sendDisabled}
                    className={cx(styles.sendBtn, styles.disableBtn, 'wz-flex', 'wz-fs-42')}
                    onClick={showErrorToast}
                >
                    发送
                </Button>
            );
        }

        return (
            <Button
                dataset-id='sendBtn'
                id='sendBtn'
                color='primary'
                shape='round'
                disabled={sendDisabled}
                className={cx(styles.sendBtn, 'wz-flex', 'wz-fs-42')}
                onClick={handleSend}
            >
                发送
            </Button>
        );
    }, [handleSend, hasImg, isUploadSuccess, sendDisabled, showErrorToast]);

    const genCom = useMemo(() => {
        // 是否需要显示上传按钮
        const shouldShowUpload = !isGenerating && showImgTools && !isSwanMsgListScene && !hasImg;
        if (isShowVoice) {
            return genVoiceCom;
        }

        return (
            <>
                {genInput}
                {shouldShowUpload && <UploadImage openUploadPopup={handleImage} />}
            </>
        );
    }, [
        genInput,
        genVoiceCom,
        hasImg,
        isShowVoice,
        isSwanMsgListScene,
        handleImage,
        showImgTools,
        isGenerating
    ]);

    const renderSwanMsgBtn = useMemo(() => {
        if (isShowVoice && !hasImg) return null;

        if (isGenerating && !hasImg && !val?.trim()) {
            return stopBtn;
        }

        if (val?.trim() || isFocus || hasImg) {
            return genBtn;
        }

        if (isSwanMsgListScene) {
            return (
                <View className={cx('wz-mb-27', 'wz-ml-18')}>
                    <UploadImage openUploadPopup={handleImage} />
                </View>
            );
        }

        return null;
    }, [
        hasImg,
        isShowVoice,
        isFocus,
        genBtn,
        handleImage,
        isGenerating,
        val,
        stopBtn,
        isSwanMsgListScene
    ]);
    const genSwanMsgCom = useMemo(() => {
        if (isSwanMsgListScene) {
            return (
                <>
                    {!hasImg ? (
                        <View className={cx('wz-mb-24', 'wz-mr-39')}>
                            <VoiceSwitch
                                handleChangeIcon={() => {
                                    handleChangeIcon();
                                    handleBlur();
                                    hideKeyboard();
                                }}
                                isShowVoice={isShowVoice}
                            />
                        </View>
                    ) : null}

                    <View
                        className={cx(
                            styles.isSwanMsgInputMain,
                            hasImg ? styles.hasImgSwanMsgInputMain : '',
                            'wz-flex',
                            'wz-plr-42',
                            'wz-br-66'
                        )}
                    >
                        {genCom}
                        {hasImg ? genBtn : null}
                    </View>
                    {/* 手百消息进入 */}
                    {!hasImg && renderSwanMsgBtn}
                </>
            );
        }

        return (
            <View
                className={cx(
                    styles.imInputMain,
                    hasImg ? styles.hasImgInputMain : '',
                    'wz-flex',
                    'wz-plr-42',
                    'wz-br-66'
                )}
            >
                {!hasImg ? (
                    <VoiceSwitch
                        handleChangeIcon={() => {
                            handleChangeIcon();
                            handleBlur();
                            hideKeyboard();
                        }}
                        isShowVoice={isShowVoice}
                    />
                ) : null}
                {genCom}
                {renderSwanMsgBtn}
            </View>
        );
    }, [
        genCom,
        handleBlur,
        handleChangeIcon,
        genBtn,
        isShowVoice,
        isSwanMsgListScene,
        renderSwanMsgBtn,
        hasImg
    ]);

    useEffect(() => {
        if (isShowVoice) {
            checkAuth('scope.record');
        }
    }, [isShowVoice]);

    useEffect(() => {
        if (imgList && imgList?.length) {
            isUploadSuccess && setPlaceholder('可以补充您的健康情况~');

            // 兜底上传图片后，只展示输入框
            setVal('');
            setIsShowVoice(false); // 重新设置为文本输入
        } else {
            setPlaceholder('有什么健康问题尽管问我');
        }
    }, [imgList, isUploadSuccess, setPlaceholder]);

    useEffect(() => {
        if (isGenerating && !hasImg && !val?.trim()) {
            ubcCommonViewSend({
                value: 'imStopBtn'
            });
        }
    }, [isGenerating, hasImg, val]);

    return (
        <View className={cx(styles.ImInputSwanContainer)} onClick={onClickCallBack}>
            {/* 交互区 */}
            <View
                className={cx(
                    styles.featureCom,
                    'wz-flex',
                    'wz-plr-36',
                    hasImg ? 'wz-pb-30' : 'wz-pt-30 wz-pb-15'
                )}
            >
                {genSwanMsgCom}
            </View>
            {/* 底部安全区 */}
            {!isFocus && <BottomAiText />}
            {!isShowImageTools && <View style={{height: keyboardHeight}} />}
            {/* <View className={styles.safeKeyboard} style={{height: keyboardHeight}} /> */}
            <SafeArea position='bottom' style={{background: 'inherit'}} />
        </View>
    );
});

ImInput.displayName = 'ImInput';

export default ImInput;
