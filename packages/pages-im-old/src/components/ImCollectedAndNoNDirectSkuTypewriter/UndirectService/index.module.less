@import url('../../../style/variable.less');

.undirectService {
    width: 100%;
    background: #fff;
    border-radius: 0 0 63px 63px;
    overflow: hidden;
    box-sizing: border-box;
    position: relative;

    .isDisabledContainer {
        position: absolute;
        background: #fff;
        inset: 0;
        opacity: 0.5;
        z-index: @zIndex-doctorMask;

        .isDisabledIcon {
            position: absolute;
            right: 0;
        }

        .ineffectiveIcon {
            width: 198px;
            height: 156px;
            position: absolute;
            top: 0;
            right: 0;
        }
    }

    .horizontal-line-container {
        width: 100%;
        position: relative;
        display: block;
    }

    .skuTop {
        padding-bottom: 60px;
    }

    .horizontal-line {
        position: absolute;
        left: 0;
        right: 0;
        top: 36px;
        height: 0.33px;
        background: #e6e6e6;
    }

    .horizontal-line-tip {
        position: relative;
        background: #fff;
        z-index: 2;
    }

    .titleWrapper {
        flex-direction: column;

        .mainTextWrapper {
            padding-top: 45px;
            color: #525252;
            letter-spacing: 0;
            text-align: center;
            line-height: 48px;

            .line {
                border: 1px #f1f1f1 solid;
                height: 0;
                width: 160px;
            }

            .point {
                width: 9px;
                height: 9px;
                background: #f1f1f1;
                border-radius: 4.5px;
            }
        }

        .subTextWrapper {
            color: #858585;
            line-height: 42px;
        }
    }

    .skuWrapper {
        background-color: #fff;

        .skuSkeletonItem {
            box-sizing: border-box;
            position: relative;
            padding: 53px 0;

            &::before {
                content: '';
                position: absolute;
                top: 0;
                width: 1050px;
                left: 0;
                height: 0.33px;
                background-color: #f1f1f1;
            }

            &:nth-of-type(1) {
                &::before {
                    content: '';
                    display: none;
                }
            }
        }

        .skuItem {
            box-sizing: border-box;
            position: relative;
            padding: 53px 0;
            align-items: center;

            .left {
                flex: 1;

                .consult {
                    color: #1f1f1f;

                    .tag {
                        padding: 0 15px;
                        padding-top: 3px;
                        color: #fd503e;
                        height: 41px;
                        line-height: 41px;
                        background: rgb(253 80 62 / 10%);
                        border: 2px solid rgb(253 80 62 / 50%);
                        border-radius: 24px;
                        font-weight: 400;
                        vertical-align: middle;
                    }
                }

                .tagWrapper {
                    font-weight: 700;

                    .tagTxt {
                        margin-top: 2px;
                        line-height: 33px;
                    }
                }

                .skus {
                    color: #858585;
                    line-height: 42px;
                }

                .reasons {
                    color: #b86a37;
                    margin-bottom: 10px;
                    font-weight: 700;

                    .reasonsText {
                        margin: 0 3px;
                    }

                    .reasonsIcon {
                        display: inline-block;
                        width: 22px;
                        height: 36px;
                        flex-shrink: 0;
                    }
                }

                .goodAts {
                    margin-bottom: 10px;
                    color: #858585;
                    font-weight: 700;

                    .colon {
                        color: #00bdbd;
                    }

                    .goodAtIcon {
                        width: 72px;
                        height: 33px;
                        flex-shrink: 0;
                    }

                    .goodAtHighLightLine {
                        position: relative;

                        &::after {
                            content: '';
                            position: absolute;
                            display: inline-block;
                            height: 15px;
                            width: 100%;
                            background: rgb(0 200 200 / 15%);
                            bottom: 5px;
                            left: 0;
                        }
                    }
                }
            }

            .center {
                flex: 1;
                height: 145px;
            }

            .right {
                display: flex;
                flex-direction: column;
                align-items: flex-end;
                flex-shrink: 0;

                .priceArea {
                    align-items: baseline;

                    .originPrice {
                        color: #b8b8b8;
                        line-height: 42px;
                        text-decoration: line-through;
                    }

                    .curPrice {
                        color: #fd503e;
                        text-align: right;
                        line-height: 48px;
                        font-weight: 600;
                    }
                }

                .toConsult {
                    width: 216px;
                    height: 84px;
                    background: #00c8c8;
                    border-radius: 48px;
                    color: #fff;
                    letter-spacing: 0;
                }
            }

            &::before {
                content: '';
                position: absolute;
                top: 0;
                width: 1050px;
                left: 0;
                height: 0.33px;
                background-color: #f1f1f1;
            }

            &:nth-of-type(1) {
                &::before {
                    content: '';
                    display: none;
                }
            }
        }

        .skuItem:first-child {
            padding-top: 0;
        }

        .skuItemCombine {
            padding: 0 0 60px;

            .left {
                .consult {
                    color: #000311;
                    font-weight: 600;
                }

                .skus {
                    color: #848691;
                }
            }

            .right {
                .priceArea .curPrice {
                    color: #fd503e;
                }

                .toConsult {
                    width: 198px;
                    height: 96px;
                    background: linear-gradient(115.5deg, #00cfa3 0%, #00d3ea 102.87%);
                    color: #fff;
                }

                .toConsultDisabled {
                    opacity: 0.5;
                }
            }

            &::before {
                content: '';
                display: none;
            }
        }
    }

    .popTitle {
        border-bottom: none;
    }
}

.titleIcon {
    width: 186px;
    height: 54px;
}

.line1 {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
}

.btn {
    background: linear-gradient(271.36deg, #00d3ea 0%, #00cfa3 100%);
    position: relative;
}

.guide::after {
    content: '';
    width: 200px;
    height: 200px;
    background-image: url('https://med-fe.cdn.bcebos.com/wenzhen-mini-app/vita/guideHand.gif');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: left center;
    position: absolute;
    right: 220px;
    top: 30px;
}

.directStyle {
    color: #858585;
    bottom: 0;
    background: #fff;
    width: 100%;
    border-radius: 45px;
    padding-top: 12px;
    padding-bottom: 60px;
}
