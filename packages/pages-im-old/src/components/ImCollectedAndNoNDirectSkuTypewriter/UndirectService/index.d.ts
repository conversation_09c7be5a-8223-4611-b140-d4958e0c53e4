import type {ExtInfo, ImUndirectServicetCon, undirectService} from '../index.d';
import {MsgId} from '../../../typings';
import type {expertItem} from '../../../components/ImCollectedAndReExpert/index.d';

export interface UndirectServiceDataV2Props {
    skuData: ImUndirectServicetCon;
    // 适配组合卡的sku点击新模式
    updateCollectedInfoAndSku: (type: string, payload?: undirectServiceUpdatePayload) => void;
    lineText?: string;
    skuDisabledToastText?: string;
    isLogin?: boolean;
    updateSkuDetailData: (selectSkuData: undirectService) => void;
    isSkuDetailPopupShow: boolean;
    setIsSkuDetailPopupShow: (value: boolean) => void;
    isSkuDisabled?: boolean;
    adjectiveRecommendUnDirectMsgId?: MsgId | undefined;
    hideTopSkuArea?: boolean;
    storageKey: string;
    isHistoryMsg: boolean;
    patientFinishTypewriter: boolean;
    // 是否命中组合卡renderType
    isRenderCombine?: boolean;
    renderType?: number;
    // 是否展示非定向首卡底部展开更多
    showLineText?: boolean;
    showSkeleton?: boolean;
    msgId?: MsgId;
    ext?: ExtInfo;
    isLast?: boolean;
    maxShowLen?: number;
    forceHideMoreBtn?: boolean;
}

export interface UbcExtInfo {
    pos?: number;
    value_id?: string;
}

export interface undirectServiceUpdatePayload {
    selectSkuData?: undirectService;
    expertData?: expertItem;
    ubcExtInfo?: UbcExtInfo;
}
