/*
 * @Author: renyi03
 * @Description: 非定向服务列表弹层
 */
import cx from 'classnames';
import {View, Text, Image} from '@tarojs/components';
import {memo, FC, useMemo} from 'react';
import {SafeArea} from '@baidu/wz-taro-tools-core';
import {WiseCheckSelectedSolid} from '@baidu/wz-taro-tools-icons';
import {CLoginButton} from '@baidu/vita-ui-cards-common';

import {IProps} from './index.d';
import styles from './index.module.less';

const ServicePopup: FC<IProps> = (props: IProps) => {
    const {selectSku = {}, isLogin, updateCollectedInfoAndSku} = props;

    const {detailInfo = {}} = selectSku;
    const {
        doctorTitle,
        oldPrice,
        features = [],
        explains = [],
        finalPrice,
        discount,
        btn,
        promotionReductionPrice,
        couponReductionPrice
    } = detailInfo;
    const priceDetail = '价格明细';

    const priceServeList = useMemo(() => {
        return (
            <View className={cx('wz-pl-51', 'wz-pr-51', 'wz-fs-54', 'wz-mb-3', styles.priceServe)}>
                <View className={cx(styles.priceTitle, 'wz-fw-500')}>{priceDetail}</View>
                {oldPrice && (
                    <View className={cx('wz-flex', 'wz-row-between', 'wz-fs-45', 'wz-mt-51')}>
                        <View>{selectSku?.title || '服务价格'}</View>
                        <Text className={cx('wz-fw-700')}>￥{oldPrice}</Text>
                    </View>
                )}
                {promotionReductionPrice && (
                    <View className={cx('wz-flex', 'wz-fs-45', 'wz-row-between', 'wz-mt-45')}>
                        <View className={cx('wz-flex', 'wz-row-between', 'wz-col-center')}>
                            <Image
                                className={cx(styles.priceImg)}
                                src='https://med-fe.cdn.bcebos.com/reducePiceIcon.png'
                            />
                            <View>平台立减</View>
                        </View>
                        <Text className={cx('wz-fw-700', styles.prefix)}>
                            -￥{promotionReductionPrice}
                        </Text>
                    </View>
                )}
                {couponReductionPrice && (
                    <View className={cx('wz-flex', 'wz-fs-45', 'wz-row-between', 'wz-mt-45')}>
                        <View className={cx('wz-flex', 'wz-row-between', 'wz-col-center')}>
                            <Image
                                className={cx(styles.priceImg)}
                                src='https://med-fe.cdn.bcebos.com/couponIcon.png'
                            />
                            <View>优惠券</View>
                        </View>
                        <Text className={cx('wz-fw-700', styles.prefix)}>
                            -￥{couponReductionPrice}
                        </Text>
                    </View>
                )}
            </View>
        );
    }, [priceDetail, oldPrice, promotionReductionPrice, couponReductionPrice, selectSku?.title]);

    return (
        <View className={cx(styles.undirectServicePopWrapper)}>
            <View className={cx('wz-pl-51', 'wz-pr-51', styles.skuDec)}>
                <View className={cx('wz-fs-57', 'wz-fw-500', styles.skuTitle)}>{doctorTitle}</View>
                <View className={cx('wz-mt-51', styles.infoContent)}>
                    {features.map((f, i) => (
                        <View
                            className={cx('wz-fs-45', 'wz-flex', 'wz-col-center', styles.infoItem)}
                            key={i}
                        >
                            <WiseCheckSelectedSolid color='#00C8C8' size={54} />
                            <Text className={cx('wz-pl-18')}>{f}</Text>
                        </View>
                    ))}
                </View>
            </View>
            <View className={cx('wz-pl-51', 'wz-pr-51', styles.explainWrapper)}>
                <View className={cx(styles.explainContent)}>
                    {explains.map((e, i) => (
                        <View key={i} className={cx('wz-fs-45', styles.explainItem)}>
                            {e}
                        </View>
                    ))}
                </View>
            </View>
            {/* 价格明细 */}
            {priceServeList}
            <View
                className={cx(
                    'wz-pl-51',
                    'wz-pr-51',
                    'wz-mb-24',
                    'wz-mt-30',
                    'wz-flex',
                    'wz-row-between',
                    'wz-fw-500',
                    styles.bottom
                )}
            >
                <>
                    <View className={cx('wz-flex', 'wz-col-top', styles.priceWrapper)}>
                        <View className={cx('wz-flex', styles.sum)}>
                            <View className={cx('wz-fs-45', 'wz-mt-9', 'wz-fw-500', styles.label)}>
                                合计：
                            </View>
                            <View className={cx('wz-fs-57', 'wz-fw-500', styles.count)}>
                                <Text className={cx('wz-fs-54', 'wz-fw-500', styles.prefix)}>
                                    ￥
                                </Text>
                                <Text className={cx('wz-fs-72', 'wz-fw-500')}>{finalPrice}</Text>
                            </View>
                        </View>
                        {Number(discount) !== 0 && discount && (
                            <View
                                className={cx('wz-flex', 'wz-mt-21', 'wz-fs-39', styles.discount)}
                            >
                                <View className={styles.label}>已优惠</View>
                                <View className={styles.count}>
                                    <Text className={cx('wz-ml-12', styles.prefix)}>￥</Text>
                                    {discount}
                                </View>
                            </View>
                        )}
                    </View>
                    {/* 立即咨询 */}
                    <CLoginButton
                        isLogin={isLogin}
                        closeShowNewUserTag={true}
                        useH5CodeLogin={true}
                        onLoginFail={error => {
                            console.error('error', error);
                        }}
                        onLoginSuccess={() => {
                            updateCollectedInfoAndSku('sku', {selectSkuData: selectSku});
                        }}
                    >
                        <View
                            className={cx(
                                'wz-fs-54',
                                'wz-fw-700',
                                'wz-flex',
                                'wz-row-center',
                                'wz-col-center',
                                styles.btn
                            )}
                        >
                            {btn}
                        </View>
                    </CLoginButton>
                </>
            </View>
            <SafeArea position='bottom' style={{background: '#fff'}} />
        </View>
    );
};

export default memo(ServicePopup);
