.skuContainer {
    width: 100%;
    background: #fff;
    border-radius: 63px;
}

.undirectServiceWrapper {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.8s cubic-bezier(0.4, 0, 0.2, 1);

    .underline {
        position: relative;

        & + .underline {
            margin-top: 72px;
        }

        &::after {
            content: '';
            position: absolute;
            bottom: 0;
            width: 1080px;
            left: 0;
            right: 0;
            margin: auto;
            height: 6px;
            background-color: #e6e6e6;
            transform: scale(1, 0.25);
        }

        &:last-child::after {
            display: none;
        }
    }
}

.expand {
    max-height: 10000px; // 设为足够大以容纳内容
}

.gradient {
    background-repeat: no-repeat;
    background-size: calc(100% - 6px) 100%;
    background-position: 3px 0;
    background-image: linear-gradient(to bottom, #e9f5fd calc(100% - 132px), #fff 100%);
}

.auth {
    justify-content: space-between;
    margin-top: 0 !important;

    .authImg {
        width: 426px;
        height: 48px;
    }

    .desc {
        color: #848691;
        font-family: PingFang SC;
        line-height: 1;
        letter-spacing: 0;
        text-align: left;
    }
}
