.expertSkeletonWrapper {
    width: 100%;
    min-width: 0;
}

.avatarSkeleton {
    width: 144px;
    height: 144px;
    border-radius: 50%;
}

.docNameSkeleton {
    height: 48px;
    width: 480px;
    border-radius: 12px;
}

.hosInfoSkeleton {
    width: 615px;
    height: 48px;
    border-radius: 12px;
}

.goodAtSkeleton {
    width: 633px;
    height: 42px;
    border-radius: 12px;
}

.tipsSkeleton {
    width: 909px;
    height: 42px;
    border-radius: 12px;
}

.btnSkeleton {
    width: 198px;
    height: 96px;
    border-radius: 90px;
}

.skeletonBg {
    background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
    background-size: 400% 100%;
    animation: skeleton-loading 1.4s ease infinite;
}

@keyframes skeleton-loading {
    0% {
        background-position: 100% 50%;
    }

    100% {
        background-position: 0 50%;
    }
}
