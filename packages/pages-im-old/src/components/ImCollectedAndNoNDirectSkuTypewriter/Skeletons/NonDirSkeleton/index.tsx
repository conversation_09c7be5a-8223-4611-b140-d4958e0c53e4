import {View} from '@tarojs/components';
import cx from 'classnames';

import styles from './index.module.less';

const NonDirSkeleton = () => {
    return (
        <View className='wz-flex wz-row-between'>
            <View>
                <View className={cx(styles.skuTitleSkeleton, styles.skeletonBg)}></View>
                <View className={cx(styles.skuTipSkeleton, styles.skeletonBg)}></View>
            </View>
            <View className={cx(styles.btnSkeleton, styles.skeletonBg)}></View>
        </View>
    );
};

export default NonDirSkeleton;
