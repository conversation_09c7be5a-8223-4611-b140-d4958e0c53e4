import {useState, useCallback, useMemo, FC} from 'react';
import {Dialog, Button} from '@baidu/wz-taro-tools-core';

type InteractionType = 'modal' | 'toast' | 'request' | 'openLink' | 'close';

interface ButtonConfig {
    value: string;
    interaction: InteractionType;
    interactionInfo: InteractionInfo;
}

interface ModalContent {
    title: string;
    buttonList: ButtonConfig[];
}

interface InteractionInfo {
    interaction: InteractionType;
    interactionInfo: {
        modalContent?: ModalContent;
        title?: string;
        url?: string;
        params?: Record<string, unknown>;
    };
}

interface PreCheckOrderModalConfig {
    open?: boolean;
    onClose?: () => void;
    content?: string;
    buttons?: ButtonConfig[]; // 修改为接收按钮配置列表
    onButtonClick?: (button: ButtonConfig) => void; // 统一的按钮点击回调
}

// 定义Dialog控制类型
interface ModalControl {
    openModal: (config: PreCheckOrderModalConfig) => void;
    closeModal: () => void;
}

// 默认配置
const defaultConfig: PreCheckOrderModalConfig = {
    open: false,
    content: '',
    buttons: [],
    onButtonClick: () => {},
    onClose: () => {}
};

// 定义按钮样式常量
const BUTTON_STYLES = {
    close: {}, // 关闭类型按钮的样式
    primary: {color: '#00c8c8'} // 其他类型按钮的样式
};

/**
 * 预检查订单模态框 hooks
 */
export const usePreCheckOrderModal = () => {
    // 状态管理
    const [modalConfig, setModalConfig] = useState<PreCheckOrderModalConfig>(defaultConfig);

    // 打开模态框
    const openModal = useCallback((config: Partial<PreCheckOrderModalConfig>) => {
        setModalConfig({
            ...defaultConfig,
            ...config,
            open: true
        });
    }, []);

    // 关闭模态框
    const closeModal = useCallback(() => {
        setModalConfig(prev => ({
            ...prev,
            open: false
        }));
    }, []);

    const PreCheckOrderModal = useMemo(() => {
        const Modal: FC<PreCheckOrderModalConfig> = ({
            open = modalConfig.open,
            onClose = closeModal,
            content = modalConfig.content,
            buttons = modalConfig.buttons,
            onButtonClick = modalConfig.onButtonClick
        }) => {
            // 处理按钮点击
            const handleButtonClick = useCallback(
                (button: ButtonConfig) => {
                    onButtonClick?.(button);
                },
                [onButtonClick]
            );

            return (
                <Dialog open={open} onClose={onClose}>
                    <Dialog.Content>{content}</Dialog.Content>
                    <Dialog.Actions>
                        {buttons?.map((button, index) => {
                            // 根据交互类型确定按钮样式
                            const isCloseType = button.interaction === 'close';
                            const buttonStyle = isCloseType
                                ? BUTTON_STYLES.close
                                : BUTTON_STYLES.primary;

                            return (
                                <Button
                                    key={index}
                                    onClick={() => handleButtonClick(button)}
                                    style={buttonStyle}
                                >
                                    {button.value}
                                </Button>
                            );
                        })}
                    </Dialog.Actions>
                </Dialog>
            );
        };

        return Modal;
    }, [modalConfig, closeModal]);

    return {
        openModal,
        closeModal,
        PreCheckOrderModal
    };
};

// 导出的类型定义
export {
    InteractionType,
    ButtonConfig,
    ModalContent,
    InteractionInfo,
    PreCheckOrderModalConfig,
    ModalControl
};
