import cx from 'classnames';
import {pxTransform} from '@tarojs/taro';

import {memo, type FC, type ReactNode, useMemo, useState, useEffect} from 'react';
import {View, Text} from '@tarojs/components';

import {CLoginButton, MarkDown} from '@baidu/vita-ui-cards-common';
import {WImage} from '@baidu/wz-taro-tools-core';

import {formatAgeText} from '../common/utils';
import {handlerWenzhenStorage} from '../../../utils/index';
import {VITA_PATIENT_DESC_IS_SHOW_MORE} from '../../../constants/storageEnv';
import TypewriterContainer from '../TypewriterContainer/index';
import FadeImage from '../FadeImage/index';
import {ubcCommonViewSend, ubcCommonClkSend} from '../../../utils/generalFunction/ubc';
import {useScrollControl} from '../../../../../pages-im/src/hooks/common/useScrollControl';
import {imgUrlMap} from '../../../constants/resourcesOnBos';

import styles from './index.module.less';
import type {PatientInfoProps} from './index.d';

/**
 *
 * @description 定向服务卡 v2 版本
 * @returns
 */
const showMoreStorage = handlerWenzhenStorage(VITA_PATIENT_DESC_IS_SHOW_MORE, 50);
export const activeIcon =
    'https://med-fe.cdn.bcebos.com/wenzhen-mini-app/vita/skuTypwerLoading.gif';
export const completeIcon =
    'https://med-fe.cdn.bcebos.com/wenzhen-mini-app/vita/typwerComplete.png';
const unrecognizedIcon = 'https://med-fe.cdn.bcebos.com/wenzhen-mini-app/vita/unrecognized.png';
const currentShowLine = 6;

const LoginedComponet = ({
    onLoginSuccess,
    children,
    className
}: {
    onLoginSuccess: () => void;
    children: ReactNode;
    className: string;
}) => (
    <View onClick={onLoginSuccess} className={className}>
        {children}
    </View>
);

const PatientCard: FC<PatientInfoProps> = props => {
    const {
        collectedInfo,
        isLogin,
        updateCollectedInfoAndSku,
        isSkuDisabled,
        patientFinishTypewriter,
        setPatientFinishTypewriter,
        adjectiveRecommendUnDirectMsgId,
        storageKey,
        statusText,
        paddingBottom = 0,
        className = ''
    } = props;

    const {curPatient, clinicalDesc} = collectedInfo || {};
    const [step, setStep] = useState(1);
    const [typewriterNum, setTypewriterNum] = useState(0);
    const [showAllDesc, setShowAllDesc] = useState(false);
    const {scrollToBottom} = useScrollControl();

    const CLoginButtonComponent = isLogin ? LoginedComponet : CLoginButton;

    const memoPatientInfo = useMemo(() => {
        const patientInfo = {
            patientText: '',
            patientDesc: '',
            type: 0,
            finishText: ''
        };

        const useinfoHeatText = '**就诊人:**';
        const descHeatText = '**病情信息:**';

        const {collectionComplete, patientFail, questionFail} = statusText || {};

        const finishTextMap = {
            0: collectionComplete?.text,
            1: patientFail?.text,
            2: questionFail?.text,
            3: questionFail?.text
        };

        const {name, age, gender} = curPatient || {};

        if (!curPatient || (!name && !age && !gender)) {
            patientInfo.patientText = `${useinfoHeatText} *待补充*`;
            patientInfo.type = 1;
        } else {
            const nameStr = name ? name : '匿名';
            const ageStr = age ? formatAgeText(age) : '';
            const genderStr = gender ? gender : '';

            patientInfo.patientText = `${useinfoHeatText} ${nameStr} ${ageStr} ${genderStr}`;
            if (!ageStr || !genderStr) {
                patientInfo.type = 1;
            }
        }

        if (clinicalDesc) {
            patientInfo.patientDesc = `${descHeatText} ${clinicalDesc}`;
        } else {
            patientInfo.patientDesc = `${descHeatText} *待补充*`;
            patientInfo.type = patientInfo.type === 1 ? 3 : 2;
        }

        patientInfo.finishText = finishTextMap[patientInfo.type];

        return patientInfo;
    }, [clinicalDesc, curPatient, statusText]);

    const typewriterData = useMemo(() => {
        const {patientCollecting, questionCollecting} = statusText || {};
        return [
            {
                sectionId: 1,
                content: memoPatientInfo.patientText,
                type: 'markdown',
                isFinish: false,
                icon: activeIcon,
                className: cx(styles.markdown, 'wz-mb-21'),
                tips: patientCollecting?.text
            },
            {
                sectionId: 2,
                content: memoPatientInfo.patientDesc,
                type: 'markdown',
                icon: activeIcon,
                isFinish: false,
                scroll: true,
                className: styles.markdown,
                tips: questionCollecting?.text
            },
            {
                sectionId: 3,
                content: '',
                type: 'markdown',
                isFinish: true,
                icon: memoPatientInfo?.type === 0 ? completeIcon : unrecognizedIcon,
                tips: memoPatientInfo.finishText
            }
        ];
    }, [memoPatientInfo, statusText]);

    const memoRenderData = useMemo(() => {
        return patientFinishTypewriter ? typewriterData : typewriterData?.slice(0, step);
    }, [patientFinishTypewriter, step, typewriterData]);

    useEffect(() => {
        if (step === typewriterData?.length) {
            setPatientFinishTypewriter(true);

            // 打字机动画结束，触发滚动到底部
            scrollToBottom('ImCollectedAndNoNDirectSkuTypewriter');
        }
    }, [
        adjectiveRecommendUnDirectMsgId,
        scrollToBottom,
        setPatientFinishTypewriter,
        step,
        typewriterData?.length
    ]);

    useEffect(() => {
        const keys = showMoreStorage('get') || [];
        if (keys.includes(storageKey)) {
            setShowAllDesc(true);
        } else {
            if (showAllDesc) {
                showMoreStorage('add', storageKey);
            }
        }
    }, [storageKey, showAllDesc]);

    useEffect(() => {
        ubcCommonViewSend({
            value: 'no_direct_sku_typewriter_patient'
        });
    }, []);

    return (
        <CLoginButtonComponent
            className='wz-flex'
            isLogin={isSkuDisabled ? isSkuDisabled : isLogin}
            closeShowNewUserTag={true}
            useH5CodeLogin={true}
            onLoginFail={error => {
                console.error('error', error);
            }}
            onLoginSuccess={async () => {
                await updateCollectedInfoAndSku('patient');

                ubcCommonClkSend({
                    value: `no_direct_sku_typewriter_patient_clk_${isLogin}`
                });
            }}
        >
            <View
                className={cx(
                    styles.patientContainerGradient,
                    patientFinishTypewriter ? styles.borderTop : 'wz-br-63',
                    isSkuDisabled ? styles.disable : '',
                    className
                )}
                style={{
                    paddingBottom: pxTransform(paddingBottom)
                }}
            >
                <View
                    className={cx(
                        styles.patientContainer,
                        'wz-plr-45 wz-pb-48 wz-pt-42',
                        isSkuDisabled ? styles.disable : ''
                    )}
                >
                    <View
                        className={cx(
                            styles.patientContainerTitle,
                            'wz-flex wz-col-center wz-row-between'
                        )}
                    >
                        <View className='wz-flex wz-col-center wz-row-left'>
                            <FadeImage
                                src={memoRenderData[memoRenderData.length - 1]?.icon}
                                width={60}
                                height={60}
                                fadeInDuration='0.3s'
                                fadeOutDuration='0.3s'
                            />
                            <Text className='wz-fs-48 wz-fw-500 wz-ml-18'>
                                {memoRenderData[memoRenderData.length - 1]?.tips}
                            </Text>
                        </View>
                        {patientFinishTypewriter ? (
                            <CLoginButton
                                isLogin={isSkuDisabled ? isSkuDisabled : isLogin}
                                closeShowNewUserTag={true}
                                useH5CodeLogin={true}
                                onLoginFail={error => {
                                    console.error('error', error);
                                }}
                                onLoginSuccess={async () => {
                                    await updateCollectedInfoAndSku('patient');
                                }}
                            >
                                <View
                                    className={cx(
                                        styles.patientRightBtn,
                                        isSkuDisabled ? styles.disable : '',
                                        'wz-plr-45 wz-ptb-21 wz-fs-42 wz-fw-500 wz-flex wz-col-center'
                                    )}
                                    style={{border: '1PX solid #00c8c8'}}
                                >
                                    修改
                                </View>
                            </CLoginButton>
                        ) : null}
                    </View>
                    <View className={cx(styles.patientLeft, 'wz-fs-42 wz-fw-400 wz-mt-39')}>
                        {memoRenderData?.map(item => {
                            const typewriterHandleFun = !patientFinishTypewriter
                                ? {
                                    typewriterProcessCallback: num => {
                                        if (!item.scroll) {
                                            return;
                                        }

                                        const line = num % 25;
                                        if (line >= currentShowLine) {
                                            setTypewriterNum(line);
                                        }
                                    },
                                    typewriterSuccessCallback: () => {
                                        setStep(step => step + 1);
                                    }
                                }
                                : {};

                            return (
                                <TypewriterContainer
                                    key={item.sectionId}
                                    content={item.content}
                                    isScroll={item.scroll || false}
                                    typewriterNum={typewriterNum}
                                    showAllDesc={showAllDesc}
                                    setShowAllDesc={setShowAllDesc}
                                    type={memoPatientInfo.type}
                                    isFinish={patientFinishTypewriter}
                                >
                                    <MarkDown
                                        markdownClassName={item.className}
                                        content={item.content}
                                        isMockTypwer
                                        loadingType=':gradient:'
                                        isTypewriter={!patientFinishTypewriter}
                                        msgEnd={patientFinishTypewriter}
                                        {...typewriterHandleFun}
                                    />
                                </TypewriterContainer>
                            );
                        })}
                    </View>
                </View>
                {isSkuDisabled && (
                    <WImage src={imgUrlMap.ineffectiveNew} className={styles.ineffectiveIcon} />
                )}
            </View>
        </CLoginButtonComponent>
    );
};

export default memo(PatientCard);
