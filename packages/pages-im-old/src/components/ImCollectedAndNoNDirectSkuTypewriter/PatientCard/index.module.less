.patientContainer {
    background: #e9f5fd;
    border-radius: 63px;
    border: 3px solid #fff;
    width: 100%;
    position: relative;
    box-sizing: border-box;

    .markdown {
        padding: 0;
        padding-left: 32px;
        font-size: 42px;
        line-height: 66px;
        text-indent: -32px;

        /* stylelint-disable-next-line selector-pseudo-class-no-unknown */
        :global {
            .p {
                margin-bottom: 0;
                color: #272933;
            }

            .bold {
                position: relative;
                color: #272933;
                padding-left: 35px;
            }

            .bold::before {
                position: absolute;
                inset: 0;
                margin: auto 0;
                content: '';
                width: 12px;
                height: 12px;
                background: #848691;
                border-radius: 50%;
            }

            .italic {
                font-style: normal;
                color: #848691;
            }

            .gradient-icon::after {
                background: linear-gradient(
                    90deg,
                    rgb(242 250 255 / 10%) 0%,
                    rgb(242 250 255 / 90%) 100%
                );
            }
        }
    }

    .patientLeft {
        color: #525252;
    }

    .patientRightBtn {
        border: 1px solid #00c8c8;
        border-radius: 90px;
        color: #00c8c8;
        flex-shrink: 0;
        height: 84px;
        box-sizing: border-box;
    }

    &.disable {
        opacity: 0.4;
        position: relative;
    }

    &Title {
        color: #272933;
        font-size: 48px;
        height: 84px;
    }

    .noInfo {
        color: #b8b8b8;
    }
}

.patientContainerGradient {
    width: 100%;
    position: relative;
}

.borderTop {
    border-radius: 63px 63px 0 0;
}

.fade-image-wrapper {
    display: flex;
    align-items: center;

    /* 让动画更丝滑 */
    will-change: opacity;
}

.ineffectiveIcon {
    width: 198px;
    height: 156px;
    position: absolute;
    top: 0;
    right: 120px;
}
