import type {PatientList} from '../ConsultForm/patientList.d';
import {Qid, MsgId} from '../../../typings';
export interface CurPatientProps {
    contactId: string;
    name: string;
    age: string;
    gender: string;
}

export interface patientItemProps extends CurPatientProps {
    isCertified: number; // 是否实名
}

export interface ImageInfo {
    fileName?: string;
    icon?: string;
    origin?: string;
    small?: string;
}

interface TextData {
    text: string;
}

interface StatusText {
    collectionComplete: TextData;
    patientCollecting: TextData;
    patientFail: TextData;
    questionCollecting: TextData;
    questionFail: TextData;
}

export interface CollectedInfoProps {
    editable?: boolean; // 是否可修改
    allowedCreatePatient?: boolean; // 是否允许创建患者
    curPatient?: CurPatientProps; // 当前选中就诊人信息
    patientList?: PatientList[];
    clinicalDesc?: string; // 病情描述
    servicePhone?: string; // 服务电话
    zhusu?: string;
    age?: string;
    gender?: string;
    contactId?: string;
    openEditPatientPop?: string;
    images?: ImageInfo[];
    qid?: Qid;
    statusText: StatusText;
}

export interface PatientInfoProps {
    collectedInfo: CollectedInfoProps;
    isLogin: boolean;
    updateCollectedInfoAndSku: (type: string) => void;
    isSkuDisabled?: boolean;
    patientFinishTypewriter: boolean;
    setPatientFinishTypewriter: (v: boolean) => void;
    storageKey: string;
    adjectiveRecommendUnDirectMsgId?: MsgId | undefined;
    statusText: StatusText;
    paddingBottom?: number;
    className?: string;
}
