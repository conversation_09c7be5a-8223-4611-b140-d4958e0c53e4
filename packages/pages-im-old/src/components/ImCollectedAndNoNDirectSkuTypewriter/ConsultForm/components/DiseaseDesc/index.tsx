/*
 *  @Author: lijing106
 * @Description: 病情描述组件
 */
import {memo, type FC, useMemo, useState, useCallback, useEffect} from 'react';
import {View, Text} from '@tarojs/components';
import cx from 'classnames';
import {Textarea} from '@baidu/wz-taro-tools-core';
import {ZHUSU_MAX_TEXT, ACTION} from '../../const';
import type {ACTION_TYPE, IState, IAction} from '../../index.d';
import styles from './index.module.less';

interface IDiseaseDescProps {
    state: IState;
    dispatch: (args: IAction) => void;
}

const DiseaseDesc: FC<IDiseaseDescProps> = props => {
    const {state, dispatch} = props;
    const [diseaseValue, setDiseaseValue] = useState(state?.zhusu || '');

    const memoTextAreaCountClass = useMemo(
        () => ((state?.zhusu?.length || 0) >= ZHUSU_MAX_TEXT ? 'textareaCountLimit' : ''),
        [state?.zhusu?.length]
    );

    // 修改主诉
    const handleDiseaseValue = useCallback(val => {
        setDiseaseValue(val);
    }, []);

    // 更新主诉
    useEffect(() => {
        dispatch({
            type: ACTION.changeZhusu as ACTION_TYPE,
            payload: {zhusu: diseaseValue}
        });
    }, [diseaseValue, dispatch]);

    return (
        <View className={cx(styles.diseaseDesc, 'wz-pt-48 wz-mlr-39')}>
            <View className='wz-mb-45 wz-fs-48 wz-fw-500'>病情描述</View>
            <View className={cx(styles.textareaBox, 'wz-br-27 wz-lr-27 wz-pt-18 wz-plr-30')}>
                <Textarea
                    className={cx(styles.textareaCon, 'wz-br-27 wz-fs-45')}
                    name='content'
                    placeholder='请输入病情描述'
                    placeholderClass='textareaPlaceholder'
                    maxlength={ZHUSU_MAX_TEXT}
                    value={diseaseValue}
                    autoHeight={false}
                    cursor-spacing='20'
                    adjustPosition
                    onInput={e => {
                        handleDiseaseValue(e.detail.value);
                    }}
                    cursor={-1}
                />
                <View className={cx(styles.textareaCount, 'wz-fs-42')}>
                    <Text className={memoTextAreaCountClass}>{state.zhusu?.length || 0}</Text>/
                    {ZHUSU_MAX_TEXT - (state.zhusu?.length || 0)}
                </View>
            </View>
        </View>
    );
};

export default memo(DiseaseDesc);
