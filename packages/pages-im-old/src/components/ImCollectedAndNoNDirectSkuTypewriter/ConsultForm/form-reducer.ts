/*
 * @Author: liu<PERSON>i
 * @Description: 表单处理逻辑
 */
import type {IAction, IState} from './index.d';
import {ACTION} from './const';

export const formReducer = (state: IState, action: IAction) => {
    const {type = ACTION.changePatient, payload} = action;

    switch (type) {
        case ACTION.changeZhusu: {
            const zhusu = payload?.zhusu || '';

            return {
                ...state,
                zhusu
            };
        }
        case ACTION.changeImages: {
            const images = payload?.images || [];
            return {
                ...state,
                images
            };
        }
        default:
            if (payload?.age) {
                const age = payload?.age || '';
                const ageArr = age.split('-');
                const ageText =
                    ageArr.length === 2
                        ? +ageArr[0] > 0
                            ? `${ageArr[0]}岁${ageArr[1]}个月`
                            : `${ageArr[1]}个月`
                        : `${ageArr[0]}岁`;

                return {
                    ...state,
                    ageText,
                    ...payload
                };
            }

            return {
                ...state,
                ...payload
            };
    }
};
