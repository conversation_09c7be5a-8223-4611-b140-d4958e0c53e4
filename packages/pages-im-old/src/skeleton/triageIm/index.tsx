import cx from 'classnames';
import { memo, FC } from 'react';
import { View } from '@tarojs/components';
import { SafeArea, Skeleton, Transition } from '@baidu/wz-taro-tools-core';

import { getSystemInfo } from '../../utils/taro/get_system_info/index';
import styles from './index.module.less';

const msgList = [
    {
        dire: 'left',
        type: 'middleMsg'
    },
    {
        dire: 'left',
        type: 'largeMsg'
    },
    {
        dire: 'right',
        type: 'smallMsg'
    },
    {
        dire: 'right',
        type: 'largeMsg'
    },
    {
        dire: 'left',
        type: 'largeMsg'
    },
    {
        dire: 'right',
        type: 'largeMsg'
    }
];

const IndexTemplate: FC = () => {
    const sysInfo = getSystemInfo();
    const statusBarHeight = Number(sysInfo?.statusBarHeight || 0) ? sysInfo.statusBarHeight : 0;

    return (
        <Transition in appear mountOnEnter name='fade'>
            <View
                className={styles.triageIm}
                style={{
                    height: `calc(100vh - ${statusBarHeight || 0}px)`,
                    paddingTop: `${statusBarHeight || 0}px`
                }}
            >
                <View className={styles.content}>
                    {msgList.map((msg, index) => {
                        return (
                            <View key={index}>
                                <View
                                    className='wz-flex wz-col-top wz-mb-51'
                                    style={{
                                        flexDirection: msg.dire === 'right' ? 'row' : 'row-reverse'
                                    }}
                                >
                                    <View
                                        className={cx(
                                            styles.flex1,
                                            `${msg.dire === 'right' ? 'wz-mr-33 wz-flex wz-row-right' : 'wz-ml-33'}`
                                        )}
                                    >
                                        <Skeleton className={styles[msg.type]} />
                                    </View>
                                    <Skeleton variant='circle' className={cx(styles.avator)} />
                                </View>
                            </View>
                        );
                    })}
                </View>
                <View className={cx(styles.inputTool, 'wz-flex wz-plr-51 wz-col-top')}>
                    <View className='wz-flex wz-mt-30 wz-mb-45' style={{ width: '100%' }}>
                        <View className={styles.input} />
                        <Skeleton variant='circle' className={cx(styles.addIcon, 'wz-ml-27')} />
                    </View>
                    <View className='wz-flex wz-row-between wz-col-center' style={{ width: '100%' }}>
                        <Skeleton className={cx(styles.button)} />
                        <Skeleton className={cx(styles.button)} />
                        <Skeleton className={cx(styles.button)} />
                    </View>
                </View>
                <SafeArea position='bottom' style={{ background: '#fcfcfc' }} />
            </View>
        </Transition>
    );
};

export default memo(IndexTemplate);
