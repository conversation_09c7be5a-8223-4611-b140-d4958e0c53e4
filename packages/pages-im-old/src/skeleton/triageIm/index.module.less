.triageIm {
    width: 100%;
    display: flex;
    flex-direction: column;
}

.content {
    width: 100%;
    background: #f3f4f5;
    padding: 51px;
    box-sizing: border-box;
    flex: 1;
    padding-top: 183px;
}

.flex1 {
    flex: 1;
    align-items: flex-start;
}

.avator {
    width: 120px;
    height: 120px;
    border-radius: 62px;
    background: #fcfcfc;
}

.smallMsg {
    width: 32.2%;
    height: 120px;
    border-radius: 27px;
    background: #fcfcfc;
}

.middleMsg {
    width: calc(100% - 153px);
    height: 120px;
    border-radius: 27px;
    background: #fcfcfc;
}

.largeMsg {
    width: calc(100% - 153px);
    height: 252px;
    border-radius: 27px;
    background: #fcfcfc;
}

.inputTool {
    background: #fcfcfc;
    height: 288px;
    flex-direction: column;
}

.button {
    width: 260px;
    height: 54px;
    background: #f3f4f5;
    border-radius: 43px;
}

.input {
    flex: 1;
    height: 120px;
    border-radius: 61px;
    background: #f3f4f5;
}

.addIcon {
    width: 180px;
    height: 96px;
    border-radius: 49px;
    background: #f3f4f5;
}
