export {default as Test} from './src/pages/test';
export {default as Im} from './src/pages/im';
export {default as DocIm} from './src/pages/docIm';

import {getDataForUbcAtom} from './src/store/triageStreamAtom/index';

import {
    ubcCommonClkSend,
    ubcCommonViewSend,
    ubcCommonTimingSend,
    ubcFn
} from './src/utils/generalFunction/ubc';

import {showToast} from './src/utils/customShowToast';
import {navigate} from './src/utils/basicAbility/commonNavigate';
import {versionControl} from './src/utils/generalFunction/version';
import httpRequest from './src/utils/basicAbility/comonRequest/cui';

export type {ICardProps, InteractionType, InteractionInfo} from './src/typings/index.d';

// todo: utils 后续再进行拆分
const utils = {
    showToast,
    ubcCommonClkSend,
    ubcCommonViewSend,
    ubcCommonTimingSend,
    ubcFn,
    navigate,
    httpRequest,
    versionControl
};

const store = {
    getDataForUbcAtom
};

export {utils, store};
