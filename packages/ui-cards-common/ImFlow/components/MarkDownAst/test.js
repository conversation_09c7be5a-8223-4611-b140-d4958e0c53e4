// 测试用例
const testCases = [
    // 基础HTTP/HTTPS链接
    'https://www.baidu.com',
    'http://example.com',
    'https://github.com/user/repo',
    
    // WWW链接
    'www.pumch.cn',
    'www.google.com',
    'www.example.org',
    
    // 带路径的链接
    'https://www.baidu.com/search?q=test',
    'www.example.com/path/to/page',
    'https://api.example.com/v1/users',
    
    // 你提供的测试数据
    '北京协和医院妇科肿瘤门诊位于东院区门诊楼3层，可通过官网（www.pumch.cn）或电话（010-69151188）预约挂号。',
    
    // 边界情况
    'https://sub.domain.com/path?param=value#anchor',
    'www.test-site.co.uk/page',
    'https://192.168.1.1:8080/admin',
    
    // 中文标点符号测试
    '访问www.example.com，获取更多信息。',
    '链接：https://www.test.com；请查看详情！',
    '网站www.demo.org？还是www.other.net！',
    
    // 括号测试
    '官网（www.pumch.cn）提供服务',
    '链接(https://example.com)在这里',
    '网址【www.test.com】可用',
    
    // 特殊字符测试
    'https://example.com/path"with"quotes',
    "www.test.com'single'quotes",
    'https://site.com<tag>content</tag>',
    
    // 无效链接
    'www.invalid',
    'http://',
    'https://.',
    'www.',
    
    // 混合内容
    '请访问https://www.baidu.com搜索信息，或者www.google.com查询资料。',
];

// 测试函数
function testUrlMatching() {
    const rule = /^(https?:\/\/[a-zA-Z0-9.-]+(?:\.[a-zA-Z]{2,})?(?:\/[^\s<>"'，。！？；：()]*)?|www\.[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(?:\/[^\s<>"'，。！？；：()]*)?)/;
    
    testCases.forEach((testCase, index) => {
        console.log(`\n测试 ${index + 1}: "${testCase}"`);
        
        // 查找所有可能的URL位置
        for (let i = 0; i < testCase.length; i++) {
            const substr = testCase.substring(i);
            const match = rule.exec(substr);
            
            if (match) {
                let url = match[0];
                // 移除末尾的标点符号
                url = url.replace(/[，。！？；：、）】}）\]).]+$/, '');
                console.log(`  找到URL: "${url}" (位置: ${i})`);
                i += match[0].length - 1; // 跳过已匹配的部分
            }
        }
    });
}

// 运行测试
testUrlMatching();