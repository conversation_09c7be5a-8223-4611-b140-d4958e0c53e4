.sourceLink {
    line-height: 1;
    font-family: PingFang SC;
    background: #f5f6fa;
    box-sizing: border-box;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 543px;
    min-width: 543px;
    flex-shrink: 0;

    .title {
        color: #000311;
    }

    .content {
        .avatars {
            flex-shrink: 0;

            .avatar {
                width: 42px;
                height: 42px;
                border-radius: 50%;
                border: 3px solid #edeef0;
                box-sizing: border-box;
            }
        }

        .label {
            font-weight: 400;
            text-align: left;
            color: #848691;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            display: block;
            height: 42px;
            line-height: 42px;
            flex: 1;
            min-width: 0;
        }
    }
}
