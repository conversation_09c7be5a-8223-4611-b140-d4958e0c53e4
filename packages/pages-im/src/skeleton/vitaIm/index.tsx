import cx from 'classnames';
import {memo, FC, useCallback} from 'react';
import {View} from '@tarojs/components';
import {Skeleton, Transition} from '@baidu/wz-taro-tools-core';

import {getSystemInfo} from '../../utils/taro/get_system_info/index';
import styles from './index.module.less';

const msgList = [
    {
        dire: 'right',
        type: 'smallMsg'
    },
    {
        dire: 'left',
        type: 'middleMsg'
    },
    {
        dire: 'right',
        type: 'smallMsg'
    },
    {
        dire: 'left',
        type: 'middleMsg'
    }
];

const IndexTemplate: FC = () => {
    const sysInfo = getSystemInfo();
    const statusBarHeight = Number(sysInfo?.statusBarHeight || 0) ? sysInfo.statusBarHeight : 0;

    const renderCon = useCallback(msg => {
        if (msg.dire === 'right') {
            return <View className={styles[msg.type]} />;
        } else {
            return (
                <View className={styles[msg.type]}>
                    <Skeleton className={styles.skeOne} />
                    <Skeleton className={styles.skeTwo} />
                </View>
            );
        }
    }, []);

    return (
        <Transition in appear mountOnEnter name='fade'>
            <View
                className={styles.triageIm}
                style={{
                    height: `calc(100vh - ${statusBarHeight || 0}px)`,
                    paddingTop: `${statusBarHeight || 0}px`
                }}
            >
                <View className={styles.content}>
                    {msgList.map((msg, index) => {
                        return (
                            <View key={index}>
                                <View
                                    className='wz-flex wz-col-top wz-mb-51'
                                    style={{
                                        flexDirection: msg.dire === 'right' ? 'row' : 'row-reverse'
                                    }}
                                >
                                    <View
                                        className={cx(
                                            styles.flex1,
                                            `${msg.dire === 'right' ? 'wz-flex wz-row-right' : ''}`
                                        )}
                                    >
                                        {renderCon(msg)}
                                        {/* <Skeleton className={styles[msg.type]} /> */}
                                    </View>
                                </View>
                            </View>
                        );
                    })}
                </View>
            </View>
        </Transition>
    );
};

export default memo(IndexTemplate);
