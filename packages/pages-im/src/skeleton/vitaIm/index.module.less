.triageIm {
    width: 100%;
    display: flex;
    flex-direction: column;
}

.content {
    width: 100%;
    padding: 36px;
    box-sizing: border-box;
    flex: 1;
    padding-top: 183px;
}

.flex1 {
    flex: 1;
    align-items: flex-start;
}

.smallMsg {
    width: 52.2%;
    height: 171px;
    border-radius: 63px 12px 63px 63px;
    background: #b2eeee;
    box-shadow: 0 0 200.01px 0 #00000003;
}

.middleMsg {
    width: 100%;
    padding: 60px 45px;
    box-sizing: border-box;
    background: #fff;
    border-radius: 12px 63px 63px;
    box-shadow: 0 0 200.001px 0 rgb(0 0 0 / 1%);
}

.skeOne {
    width: 100%;
    background: #eff3f9;
    height: 57px;
    border-radius: 12px;
}

.skeTwo {
    width: 50%;
    background: #eff3f9;
    height: 57px;
    border-radius: 12px;
    margin-top: 24px;
}
