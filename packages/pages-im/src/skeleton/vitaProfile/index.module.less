.userWrap {
    padding: 183px 36px 0;

    .userContainer {
        padding: 3px;
        background: rgb(255 255 255 / 30%);
        border: 3px solid #fff;
        border-radius: 63px 63px 0 0;

        .userMsg {
            display: flex;
            align-items: center;
            padding: 72px 0 117px 60px;
            border-radius: 63px 63px 0 0;

            .userAvatar {
                display: flex;
                justify-content: center;
                padding: 6px;
                margin-right: 45px;
                background: #fff;
                border-radius: 50%;

                .avatar {
                    width: 174px;
                    height: 174px;
                    border-radius: 50%;
                }
            }

            .userTextSkeleton {
                width: 270px;
                height: 54px;
                border-radius: 12px;
                background: #fff;
            }
        }
    }
}

.directWrap {
    padding: 0 36px;
    transform: translateY(-57px);

    .directBlock {
        margin-bottom: 30px;
        background: #fff;
        border-radius: 63px;

        .linkIcon {
            width: 48px;
            height: 48px;
        }

        .directItem {
            padding: 0 45px;

            .inner {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 60px 0;
                border-bottom: 1px solid #ededf0;

                .innerLeft {
                    display: flex;
                    align-items: center;

                    .skeletonIcon {
                        width: 78px;
                        height: 78px;
                        border-radius: 12px;
                        margin-right: 30px;
                        background: #eff3f9;
                    }

                    .skeletonTitle {
                        width: 270px;
                        height: 48px;
                        border-radius: 12px;
                        background: #eff3f9;
                    }
                }
            }

            &:last-child {
                .inner {
                    border-bottom: none;
                }
            }
        }
    }
}
