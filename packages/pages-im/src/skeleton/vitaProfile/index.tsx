import {memo, FC} from 'react';
import {View, Image} from '@tarojs/components';
import {Skeleton, Transition} from '@baidu/wz-taro-tools-core';
import {getSystemInfo} from '../../utils/taro/get_system_info/index';

import styles from './index.module.less';

const loginDefault = 'https://med-fe.cdn.bcebos.com/vita/login-default.png';
const linkIcon = 'https://med-fe.cdn.bcebos.com/vita/linkDirect.png';

const IndexTemplate: FC = () => {
    const sysInfo = getSystemInfo();
    const statusBarHeight = Number(sysInfo?.statusBarHeight || 0) ? sysInfo.statusBarHeight : 0;

    return (
        <Transition in appear mountOnEnter name='fade'>
            <View
                className={styles.triageIm}
                style={{
                    height: `calc(100vh - ${statusBarHeight || 0}px)`,
                    paddingTop: `${statusBarHeight || 0}px`
                }}
            >
                <>
                    <View className={styles.userWrap}>
                        <View className={styles.userContainer}>
                            <View className={styles.userMsg}>
                                <View className={styles.userAvatar}>
                                    <Image
                                        src={loginDefault}
                                        className={styles.avatar}
                                        mode='widthFix'
                                    />
                                </View>
                                <View className={styles.userTextSkeleton}></View>
                            </View>
                        </View>
                    </View>
                    <View className={styles.directWrap}>
                        {[3, 2].map((item, index) => (
                            <View className={styles.directBlock} key={index}>
                                <View className={styles.directItem}>
                                    {Array(item)
                                        .fill(null)
                                        .map((_, idx) => (
                                            <View className={styles.inner} key={idx}>
                                                <View className={styles.innerLeft}>
                                                    <Skeleton
                                                        className={styles.skeletonIcon}
                                                        animation='wave'
                                                    />
                                                    <Skeleton
                                                        className={styles.skeletonTitle}
                                                        animation='wave'
                                                    />
                                                </View>
                                                <Image
                                                    src={linkIcon}
                                                    className={styles.linkIcon}
                                                    mode='widthFix'
                                                />
                                            </View>
                                        ))}
                                </View>
                            </View>
                        ))}
                    </View>
                </>
            </View>
        </Transition>
    );
};

export default memo(IndexTemplate);
