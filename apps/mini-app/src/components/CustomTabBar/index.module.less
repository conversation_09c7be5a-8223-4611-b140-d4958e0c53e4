.tabBar {
    height: 174px;
    display: flex;
    align-items: center;
    background: #fff;
    position: relative;

    .tabItem {
        color: #8faedd;
        text-align: center;
        flex: 1;
    }

    .tabItemImg {
        width: 66px;
        height: 66px;
        display: block;
        margin: 0 auto;
    }

    .tabItemText {
        font-size: 36px;
        line-height: 1;
        margin-top: 12px;
        font-family: PingFang SC;
    }

    .tabItemActive {
        color: #00c8c8;
        animation: bounce-scale 0.4s ease;

        .tabItemImg {
            width: 66px;
            height: 66px;
        }

        .tabItemText {
            font-weight: 700;
        }
    }

    .tabItem_ai {
        &.tabItemActive {
            .tabItemImg {
                width: 120px;
                height: 120px;
            }
        }
    }
}

.tabBarAi {
    background-color: transparent;
}

@keyframes bounce-scale {
    0% {
        transform: scale(1);
    }

    30% {
        transform: scale(0.9);
    }

    60% {
        transform: scale(1.1);
    }

    100% {
        transform: scale(1);
    }
}
