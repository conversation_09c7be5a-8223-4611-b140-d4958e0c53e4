import {View, Image} from '@tarojs/components';
import {useCallback, useEffect} from 'react';
import cx from 'classnames';
import {
    ubcCommonViewSend,
    ubcCommonClkSend
} from '../../../../../packages/pages-im/src/utils/generalFunction/ubc';
import type {TabItem, TabId} from './index.d';
import styles from './index.module.less';

const tabs: TabItem[] = [
    {
        id: 'ai',
        label: '管家',
        icon: 'https://med-fe.cdn.bcebos.com/vita/tab-ai-0808.png',
        activeIcon: 'https://med-fe.cdn.bcebos.com/vita/tab-ai-active-0808.png',
        title: 'AI健康管家',
        path: '/pages/im/index'
    },
    {
        id: 'service',
        label: '服务',
        icon: 'https://med-fe.cdn.bcebos.com/vita/tab-service-0808.png',
        activeIcon: 'https://med-fe.cdn.bcebos.com/vita/tab-service-active-0808.png',
        title: 'AI健康管家',
        path: '/pages/service/index'
    },
    {
        id: 'profile',
        label: '我的',
        icon: 'https://med-fe.cdn.bcebos.com/vita/tab-profile-0808.png',
        activeIcon: 'https://med-fe.cdn.bcebos.com/vita/tab-profile-active-0808.png',
        title: '我的',
        path: '/pages/profile/index'
    }
];
const CustomTabBar = ({
    activeTab,
    onTabPress
}: {
    activeTab: TabId;
    onTabPress: (tab: TabItem) => void;
}) => {
    useEffect(() => {
        ubcCommonViewSend({
            value: 'vitaTabShow',
            page: 'vita/pages/im/index'
        });
    }, []);

    const handleOnPressTab = useCallback(
        tab => {
            onTabPress && onTabPress(tab);
            ubcCommonClkSend({
                value: tab?.id + 'Tab',
                page: 'vita/pages/im/index'
            });
        },
        [onTabPress]
    );

    return (
        <View className={cx(styles.tabBar, activeTab === 'ai' ? styles.tabBarAi : '')}>
            {tabs.map(tab => (
                <View
                    key={tab.id}
                    // imtab 高亮样式和其他不一样
                    className={cx(
                        styles.tabItem,
                        `${activeTab === tab.id ? styles.tabItemActive : ''}`,
                        styles[`tabItem_${tab.id}`]
                    )}
                    onClick={() => handleOnPressTab(tab)}
                >
                    <Image
                        className={styles.tabItemImg}
                        src={activeTab === tab.id ? tab.activeIcon : tab.icon}
                    />
                    {!(tab.id === 'ai' && activeTab === tab.id) && (
                        <View className={styles.tabItemText}>{tab.label}</View>
                    )}
                </View>
            ))}
        </View>
    );
};

export default CustomTabBar;
